<?php

use App\Http\Controllers\Auth\LoginController;
use Livewire\Livewire;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/robots.txt', 'App\Http\Controllers\RobotsController@robots');

Route::get('/cars-feed', 'App\Http\Controllers\FeedController@exportCarsCsv');

// route to verify show / no show status of reservation
Route::get('verify/{reservation_uuid}/{resolution}', [
    'as' => 'reservations.verify',
    'uses' => 'App\Http\Controllers\ReservationsController@verify',
]);

Route::group([
    'prefix' => LaravelLocalization::setLocale(),
    'namespace'   => 'App\Http\Controllers',
    'middleware' => ['ipRedirect', 'localizationRoutes', 'localizationRedirect']
], function () {
    //     var_dump(LaravelLocalization::transRoute('routes.cars'));

    // Redirect pages to localized branches route
    Route::get('cars', function () {
        return redirect(route('listings.index'));
    });
    // Redirect pages to localized branches route
    Route::get('cars/{slug}', function ($slug) {
        return redirect(route('listings.show', ['slug' => $slug]));
    });

    Route::get(LaravelLocalization::transRoute('routes.cars'), [
        'as' => 'listings.show',
        'uses' => 'ListingsController@show'
    ]);
    Route::resource(LaravelLocalization::transRoute('routes.cars_search'), 'ListingsController', ['only' => ['index'], 'names' => ['index' => 'listings.index']]);

    Route::get('/', [
        'as' => 'home',
        'uses' => 'HomeController@getIndex'
    ]);

    // Redirect contact to localized contact route
    Route::match(['get', 'post'], '/contact', function () {
        return redirect(route('contact'));
    });
    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.contact'), [
        'as' => 'contact',
        'uses' => 'ContactController@showContact'
    ]);
    Route::post(LaravelLocalization::transRoute('routes.contact'), [
        'as' => 'contact.store',
        'uses' => 'ContactController@handleContact'
    ]);

    // Redirect pages to localized services route
    Route::get('/services', function () {
        return redirect(route('services'));
    });

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.services'), [
        'as' => 'services',
        'uses' => 'PagesController@services'
    ]);

    // Redirect pages to localized branches route
    Route::get('/branches', function () {
        return redirect(route('branches'));
    });

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.branches'), [
        'as' => 'branches',
        'uses' => 'PagesController@branches'
    ]);

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.places.heraklion'), [
        'as' => 'heraklion',
        'uses' => 'PlacesController@heraklion'
    ]);

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.places.heraklion_airport'), [
        'as' => 'heraklion_airport',
        'uses' => 'PlacesController@heraklion_airport'
    ]);

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.places.chania_airport'), [
        'as' => 'chania_airport',
        'uses' => 'PlacesController@chania_airport'
    ]);

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.places.chania'), [
        'as' => 'chania',
        'uses' => 'PlacesController@chania'
    ]);

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.places.rethymno'), [
        'as' => 'rethymno',
        'uses' => 'PlacesController@rethymno'
    ]);

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.places.agios_nikolaos'), [
        'as' => 'agios_nikolaos',
        'uses' => 'PlacesController@agios_nikolaos'
    ]);

    // Redirect pages to localized faq route
    Route::get('/faq', function () {
        return redirect(route('faq'));
    });

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.faq'), [
        'as' => 'faq',
        'uses' => 'PagesController@faq'
    ]);

    // Redirect pages to localized privacy route
    Route::get('/privacy', function () {
        return redirect(route('privacy'));
    });

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.privacy'), [
        'as' => 'privacy',
        'uses' => 'PagesController@privacy'
    ]);

    // Redirect pages to localized safety route
    Route::get('/travelling-safely', function () {
        return redirect(route('safety'));
    });

    // Localized url, check resources/lang/{language}/routes.php
    Route::get(LaravelLocalization::transRoute('routes.safety'), [
        'as' => 'safety',
        'uses' => 'PagesController@safety'
    ]);

    // Routes for PagesController
    /*
     * If the localized route has the same name as the route then it needs to be defined explicitly and not through this loop
     */
    $pageRoutes = ['about', 'policy', 'insurance'];

    // Redirect pages to localized PagesController route
    Route::get('/{pageRoute}', function ($pageRoute) {
        return redirect(route($pageRoute));
    })->where('pageRoute', implode('|', $pageRoutes));

    foreach ($pageRoutes as $pageRoute) {
        // Localized url, check resources/lang/{language}/routes.php
        Route::get(LaravelLocalization::transRoute('routes.' . $pageRoute), [
            'as' => $pageRoute,
            'uses' => 'PagesController@' . $pageRoute
        ]);
    }

    // (google) reviews page
    Route::get('/reviews', [
        'as' => 'reviews.index',
        'uses' => 'ReviewsController@index'
    ]);

    // fleet booking page
    Route::get('/book/fleet', [
        'as' => 'booking.fleet.index',
        'uses' => 'BookingsController@fleetIndex',
    ]);
    // landing booking page
    Route::get('/book-online', [
        'as' => 'booking.show',
        'uses' => 'BookingsController@showBooking',
    ]);
    Route::post('/book-online', [
        'as' => 'booking.handle',
        'uses' => 'BookingsController@handleBooking',
    ]);

    Livewire::setUpdateRoute(function ($handle) {
        return Route::post('/livewire/update', $handle);
    });

    Route::get('/reservation/{reservation_id}', [
        'as' => 'reservation.confirm',
        'uses' => 'ReservationsController@confirm'
    ]);

    Route::get('/logout', function () {
        if (Auth::check()) {
            Auth::logout();
        }
        return Redirect::route('home');
    });
    // public routes for reservations
    Route::resource('reservations', 'ReservationsController')->only(['store'])->names(['store' => 'reservations.store']);

    // public routes for blog
    Route::get('blog/tag/{tag}', [
        'as' => 'post.tags.index',
        'uses' => 'PostsController@indexWithTags'
    ]);
    Route::get('blog/{slug}', [
        'as' => 'posts.show',
        'uses' => 'PostsController@show'
    ]);
    Route::resource('blog', 'PostsController', ['only' => ['index'], 'names' => ['index' => 'posts.index']]);

    // search
    Route::get('search', [
        'as' => 'search.index',
        'uses' => 'SearchController@index'
    ]);

    // public routes for feedback
    Route::get('feedback/{reservation_hash}', [
        'as' => 'feedback.create',
        'uses' => 'FeedbackController@create'
    ]);
    Route::post('feedback', [

        'as' => 'feedback.store',
        'uses' => 'FeedbackController@store'
    ]);

    // public routes for commercial offers
    Route::get('offers/{offer_slug}', [
        'as' => 'offers.show',
        'uses' => 'OffersController@show'
    ]);
});



// handle session variables
Route::group([
    'namespace'   => 'App\Http\Controllers',
    'middleware' => 'ajax.only'
], function () {
    Route::post('/sessionsclear', [
        'as' => 'sessions.clearall',
        'uses' => 'SessionsController@destroyAll'
    ]);
    Route::resource('sessions', 'SessionsController', ['only' => ['store']]);

    Route::post('/getprice', [
        'as' => 'getprice',
        'uses' => 'ListingsController@getPrice'
    ]);

    Route::post(
        '/newsletter',
        [
            'as' => 'newsletters.store',
            'uses' => 'ContactController@handleNewsletter'
        ]
    );

    // public routes for quotes
    Route::resource('quotes', 'QuotesController', ['only' => ['store']]);

    // public routes for repeatingClients
    Route::resource('repeatingClients', 'RepeatingClientsController', ['only' => ['store']]);
});

// two factor verification routes
Route::get('verify/resend', 'App\Http\Controllers\Auth\TwoFactorController@resend')->name('verify.resend');
Route::resource('verify', 'App\Http\Controllers\Auth\TwoFactorController')->only(['index', 'store']);


// admin routes
Route::group([
    'prefix' => 'admin',
    'namespace'   => 'App\Http\Controllers',
    'middleware' => ['auth', \App\Http\Middleware\TwoFactor::class, 'enabled.only']
], function () {

    Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('login', [LoginController::class,'login']);
    Route::post('logout',  [LoginController::class,'logout'])->name('logout');

    // dashboard
    Route::get('dashboard', [
        'as' => 'admin.dashboard',
        'uses' => 'DashboardController@getIndex'
    ]);

    // accessories
    Route::resource('accessories', 'AccessoriesController', [
        'as' => 'admin',
        'except' => 'show'
    ]);

    // locations
    Route::resource('locations', 'LocationsController', [
        'as' => 'admin',
        'except' => 'show'
    ]);

    // coupons
    Route::resource('coupons', 'CouponsController', [
        'as' => 'admin',
        'except' => ['show', 'destroy']
    ]);

    // popup
    Route::resource('popup', 'PopupController', [
        'as' => 'admin',
        'except' => 'show'
    ]);

    // feedback
    Route::resource('feedback', 'FeedbackController', [
        'as' => 'admin',
        'except' => ['create', 'store']
    ]);

    // pricing
    Route::resource('pricing', 'PricingController', [
        'as' => 'admin',
    ])->except(['create', 'store', 'show', 'destroy', 'update']);
    // pricing update
    Route::put('pricing', [
        'as'    => 'admin.pricing.update',
        'uses'  => 'PricingController@update'
    ]);
    // pricing availability
    Route::put('availability', [
        'as'    => 'admin.availability.update',
        'uses'  => 'PricingController@availabilityUpdate'
    ]);

    // reservations
    Route::post('reservations',  [
        'as' => 'reservations',
        'uses' => 'ReservationsController@index'
    ]);

    // reservations
    Route::resource('reservations', 'ReservationsController', [
        'as' => 'admin',
        'except' => ['store', 'edit']
    ]);

    // repeatingClients
    Route::post('repeatingClients',  [
        'as' => 'repeatingClients',
        'uses' => 'RepeatingClientsController@index'
    ]);

    // repeatingClients
    Route::resource('repeatingClients', 'RepeatingClientsController', [
        'as' => 'admin',
        'except' => ['store']
    ]);

    // quotes
    Route::resource('quotes', 'QuotesController', [
        'as' => 'admin',
        'except' => ['store', 'show']
    ]);

    // customers
    Route::resource('customers', 'CustomersController', [
        'as' => 'admin',
        'only' => ['index']
    ]);

    // categories
    Route::resource('categories', 'CategoriesController', [
        'as' => 'admin',
        'except' => ['show']
    ]);

    // newsletters list
    Route::get('newsletters', [
        'as' => 'admin.newsletters.index',
        'uses' => 'ContactController@getNewslettersIndex'
    ]);

    //    Route::get('db_change', function(){});

    Route::get('listing-images', ['as' => 'listing-images', 'uses' => 'ImageController@getListingImages']);
    Route::get('location-images', ['as' => 'location-images', 'uses' => 'ImageController@getLocationImages', 'action' => 'location']);
    Route::post('upload-image', ['as' => 'upload-image', 'uses' => 'ImageController@postUpload']);
    Route::post('upload-location-image', ['as' => 'upload-location-image', 'uses' => 'ImageController@postUpload', 'action' => 'location']);
    Route::post('upload-remove', ['as' => 'upload-remove', 'uses' => 'ImageController@deleteUpload']);
    Route::post('upload-location-remove', ['as' => 'upload-location-remove', 'uses' => 'ImageController@deleteUpload', 'action' => 'location']);
});


// Only expose routes on debug env
if (env('APP_DEBUG')) {
    Route::get('/reservation_client', function () {
        echo "Latest reservations:<br>";
        foreach (\App\Reservation::select('id')->where('site', 1)->orderBy('id', 'DESC')->take(10)->get()->pluck('id')->toArray() as $id) {
            echo "<a href='" . url('/reservation_client', [$id, 'old']) . "' target='_blank'>Reservation #{$id} old email</a>\t\t\t<a href='" . url('/reservation_client', [$id, 'new']) . "' target='_blank'>Reservation #{$id} new email</a><br>";
        }
    });

    Route::get('/reservation_reminder', function () {
        echo "Latest reservations:<br>";
        foreach (\App\Reservation::select('id')->where('site', 1)->orderBy('id', 'DESC')->take(10)->get()->pluck('id')->toArray() as $id) {
            echo "<a href='" . url('/reservation_reminder', [$id, 'old']) . "' target='_blank'>Reservation #{$id} old email</a>\t\t\t<a href='" . url('/reservation_reminder', [$id, 'new']) . "' target='_blank'>Reservation #{$id} new email</a><br>";
        }
    });

    Route::get('/reservation_feedback', function () {
        echo "Latest reservations:<br>";
        foreach (\App\Reservation::select('id')->where('site', 1)->orderBy('id', 'DESC')->take(10)->get()->pluck('id')->toArray() as $id) {
            echo "<a href='" . url('/reservation_feedback', [$id, 'old']) . "' target='_blank'>Reservation feedback #{$id} old email</a>\t\t\t<a href='" . url('/reservation_feedback', [$id, 'new']) . "' target='_blank'>Reservation feedback #{$id} new email</a><br>";
        }
    });
    Route::get('/reservation_feedback/{reservation_id}/{emailVersion}', function ($reservation_id, $emailVersion) {
        $reservation = \App\Reservation::find($reservation_id);
        if (empty($reservation)) {
            echo "Could not find Reservation";
            die;
        }
        $customer = $reservation->getCustomer();
        if (empty($customer)) {
            echo "Could not find Customer";
            die;
        }
        $listing = $listing = \App\Listing::find($reservation->listing_id);
        if (empty($listing)) {
            echo "Could not find Listing";
            die;
        }
        if ($emailVersion !== 'old' && $emailVersion !== 'new') {
            echo "Invalid mail option";
            die;
        }
        $group = \App\Group::find($listing->group_id);

        $emailTemplate = $emailVersion === 'new' ? 'emails.feedback_request_revamp' : 'emails.feedback_request';

        return view($emailTemplate, ['reservation' => $reservation, 'listing' => $listing, 'customer' => $customer, 'group' => $group]);
    });

    //    Route::get('/multiMail', 'TestController@multiMail');
        Route::get('/deepl', 'App\Http\Controllers\TestController@deepl');

        Route::get('/offer-email', 'App\Http\Controllers\TestController@offerEmail');
}


// Uncomment code below to display all SQL statements executed in Eloquent
// Event::listen('illuminate.query', function($query, $bindings, $time, $name) {
//     $data = compact('bindings', 'time', 'name');
//
//     // Format binding data for sql insertion
//     foreach ($bindings as $i => $binding) {
//     if ($binding instanceof \DateTime) {
//     $bindings[$i] = $binding->format('\'Y-m-d H:i:s\'');
//     } else if (is_string($binding)) {
//     $bindings[$i] = "'$binding'";
//     }
//     }
//
//     // Insert bindings into query
//     $query = str_replace(array('%', '?'), array('%%', '%s'), $query);
//     $query = vsprintf($query, $bindings);
//
//     echo $query . "<br/>";
//     echo 'time: ' . $time . "ms<br/>";
// });

Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class,'login']);
Route::post('logout',  [LoginController::class,'logout'])->name('logout');

//Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
