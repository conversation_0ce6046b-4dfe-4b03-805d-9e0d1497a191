<?php

namespace App\Jobs;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Routing\Router;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendDuplicateReservationReminder extends Job implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Execute the job.
     *
     */
    public function handle(Router $router)
    {
        Mail::send('emails.duplicate_reservations_admin', [], function($message)
        {
            $message->to(config('mail_addresses.argiry'), 'Argiry Fragkaki')
                ->cc(['<EMAIL>'])
                ->subject('There are duplicate reservations that need your attention');
        });

        // log reminder send action
        Log::info('Duplicate reservation reminder sent');

    	return true;
    }
}
