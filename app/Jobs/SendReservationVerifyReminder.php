<?php

namespace App\Jobs;

use App\Reservation;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

use Illuminate\Routing\Router;
use App\Helpers\ChangePortal;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendReservationVerifyReminder extends Job implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reservation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Reservation $reservation)
    {
        $this->reservation = $reservation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Router $router)
    {
    	// Check if we already have resolution for this reservation
        // in that case we do not want to send anyfin
    	if($this->reservation->show_verified)
    	{
    		return true;
    	}

        $reservation = $this->reservation;

    	// Send a verify reminder for the reservation

        Mail::send('emails.verify_reservations_admin', ['reservation' => $reservation], function($message) use ($reservation)
        {
            $message->to(config('mail_addresses.argiry'), 'Argiry Fragkaki')
                ->cc(['<EMAIL>'])
                ->subject('Please verify the reservation with id: ' . $this->reservation->id);
        });

        // log feedback request send action
        Log::info('Reservation id: ' . $reservation->id . ' verify reminder sent');

    	$reservation->verify_reminded_at = Carbon::now();
    	$reservation->save();

    	return true;
    }
}
