<?php

namespace App\Jobs;

use App\Reservation;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

use Illuminate\Routing\Router;
use App\Helpers\ChangePortal;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendReservationFeedback extends Job implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $reservation;
    
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Reservation $reservation)
    {
        $this->reservation = $reservation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Router $router)
    {
    	// Check if we already have feedback for this reservation
        // in that case we do not want to send anyfin
    	if( ! is_null($this->reservation->feedback) )
    	{
    		return true;
    	}

        $reservation = $this->reservation;

    	// no feedback functionality for cretanrentals currently
        if ( (int)$reservation->site === 2 )
        {
            return true;
        }

        // if the reservation has no hash, create one
        // temp fix for all reservations that were created prior to deploying this feature
        // TODO lose it after some months
        if ( empty($reservation->feedback_hash) )
        {
            $reservation->feedback_hash = md5( rand(0, 1000) );
            $reservation->save();
        }

    	// Send a feedback request for the reservation
    	App::setLocale(DB::table('languages')->where('id', $this->reservation->language_id)->value('locale'));
    	$customer = $reservation->getCustomer();

    	// failproof against the non-existence of a customer
        if( is_null($customer) )
        {
            return true;
        }

    	// portal details
    	if( (int)$reservation->site === 2 )
    	{
    		$mailData['subjectSite']		= 'Cretanrentals.com';
    		$mailTemplatePrefix 			= 'cretanrentals::';
    		// Load cretan portal
    		ChangePortal::LoadPortal($router, 'cretanrentals');
    	}
    	else
        {
    		$mailData['subjectSite']		= 'Eurodollar';
    		$mailTemplatePrefix 			= '';
    		// Load eurodollar portal
    		ChangePortal::LoadPortal($router, 'eurodollar');
    	}

    	// ...to client
    	Mail::send($mailTemplatePrefix . 'emails.feedback_request_revamp', ['reservation' => $reservation, 'customer' => $customer, 'mailData' => $mailData], function($message) use ($reservation, $customer, $mailData)
    	{
    		$message
    			->to($customer->email, $customer->name)
    			->bcc('<EMAIL>', 'bmenekl2')
    			->subject($mailData['subjectSite'] . ': ' . trans('email.feedback_request.subject') . ' ' . $reservation->id);
    	});

        // log feedback request send action
        Log::info('Reservation id: ' . $reservation->id . ' feedback request sent');

    	$reservation->feedback_requested_at = Carbon::now();
    	$reservation->save();

    	return true;
    }
}
