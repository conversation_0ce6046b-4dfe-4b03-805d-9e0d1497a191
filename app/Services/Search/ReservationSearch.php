<?php

namespace App\Services\Search;

use App\Reservation;
use Illuminate\Http\Request;

class ReservationSearch extends Search
{

    public static function apply(Request $request)
    {
        // as an extra layer of security we loop only over the validated request data
        // in this manner, the form request acts as a gatekeeper of search filters
        // we only wants to iterate through validated data and unempty ones
        $filters = array_filter($request->validated());

        $query = static::applyDecoratorsFromFilters($filters, (new Reservation())->newQuery()
            ->orderBy('created_at', 'desc'));

        return static::getResults($query, 10);
    }

}
