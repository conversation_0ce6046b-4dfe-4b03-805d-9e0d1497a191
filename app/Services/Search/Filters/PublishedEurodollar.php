<?php

namespace App\Services\Search\Filters;

use Illuminate\Database\Eloquent\Builder;

class PublishedEurodollar extends GlobalFilter
{
    /**
     * Filter key to use in query
     *
     * @var string
     */
    protected static string $filter_key = 'published_eurodollar';

    /**
     * Apply a given search value to the builder instance.
     *
     * @param Builder $builder
     * @param mixed $value
     * @return Builder $builder
     */
    public static function apply(Builder $builder, $value): Builder
    {
        return self::filterPseudoBoolean($builder, $value);
    }
}