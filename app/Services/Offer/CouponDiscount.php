<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 5/24/15
 * Time: 09:10
 */

namespace App\Services\Offer;

use App\Coupon;
use App\Site;

class CouponDiscount extends DiscountBehaviour {

    protected $discount;

    protected $coupon;

//    protected $couponRepo;

    public function __construct(DiscountBehaviour $discount, $coupon_text)
    {
        $this->discount = $discount;
        $this->coupon_text = $coupon_text;
    }


    public function getDiscountAmount()
    {
        $site = Site::where('name', '=', config('appportal.name'))->first();
        // get the coupon data
        $coupon = Coupon::where('text', '=', $this->coupon_text)->where('site', '=', $site->id)->first();

        // faissafe checks
        if ( empty($coupon) || $coupon->valid == false )
        {
            // do not employ discount for an unknown or invalid coupon
            return $this->discount->getDiscountAmount();
        }

        return $this->discount->getDiscountAmount()
            + round($this->discount->calculateTotalPrice() * $coupon->discount_percentage);
    }
}