<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 12/26/14
 * Time: 10:54
 */

namespace App\Services\Validation;


class RepeatingClientValidator extends Validator {

    /**
     * Default rules
     *
     * @var array
     */
    protected $rules = [
        'listing_id' => ['required', 'integer'],
        'email' => ['required', 'email'],
//         'pickup_date' => 'required',
//         'pickup_time' => 'required',
//         'pickup_location' => 'required',
//         'dropoff_date' => 'required',
//         'dropoff_time' => 'required',
//         'dropoff_location' => 'required',
    ];


    /**
     * Rules for updating a repeatingClient
     *
     * @var array
     */
    protected $updateRules = [
        'listing_id' => ['required', 'integer'],
        'email' => ['required', 'email'],
//         'pickup_date' => 'required',
//         'pickup_time' => 'required',
//         'pickup_location' => 'required',
//         'dropoff_date' => 'required',
//         'dropoff_time' => 'required',
//         'dropoff_location' => 'required',
    ];
}