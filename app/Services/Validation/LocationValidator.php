<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 12/30/14
 * Time: 18:15
 */

namespace App\Services\Validation;

use Illuminate\Support\Facades\Config;

class LocationValidator extends Validator {

    /**
     * Default rules
     *
     * @var array
     */
    protected $rules = [
        'area' => ['required']
    ];

    /**
     * Rules for updating a user
     *
     * @var array
     */
    protected $updateRules = [
        'area' => ['required']
    ];
    
    /**
     * Dynamicaly set all the translation validations
     */
    public function __construct(){
    	foreach(Config::get('translationLocales') as $locale){
    		$this->rules['name_' . $locale] = ['required'];
    		$this->updateRules['name_' . $locale] = ['required'];
    	}
    }
}