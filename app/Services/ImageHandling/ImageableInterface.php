<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 12/31/14
 * Time: 19:38
 */

namespace App\Services\ImageHandling;


use Illuminate\Http\Request;

interface ImageableInterface {

    /**
     * Delete the image that is stored for a given model
     *
     * @return $this
     */
    public function deleteImage();

    /**
     * Grab the uploaded image and store it in a permanent location
     *
     * @param Request $request
     * @return mixed
     */
    public function storeImage($destination = 'img/');

}