<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 12/31/14
 * Time: 19:29
 */

namespace App\Services\ImageHandling;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;


trait ImageableTrait {

    /**
     * Delete the image that is stored for a given model
     *
     * @return $this
     */
    public function deleteImage()
    {
        // @todo: do not delete the image at all times. Only if the submitted is different
        // if the accessory already has an image then delete the file from disk
        $path = public_path() . '/' . $this->image;
        if(!empty($this->image) && File::exists($path))
        {
            File::delete($path);
        }

        return $this;
    }

    /**
     * Grab the uploaded image and store it in a permanent location
     *
     * @param string $destination
     * @return $this
     */
    public function storeImage($destination = 'img/')
    {
        // get hold of uploaded image
        $image = request()->file('image');

        // prepare the image name
        $image_name = Str::random(6) . '_' . $image->getClientOriginalName();

        // move the file from the temporary uploaded location
        // to a more permanent one
        $image->move(public_path() . '/' . $destination, $image_name);

        // store new image path
        $this->image = $destination . $image_name;
        $this->save();

        return $this;
    }
}