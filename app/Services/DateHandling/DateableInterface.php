<?php namespace App\Services\DateHandling;


interface DateableInterface {

    /**
     * Returns the name of the period that a given date falls into
     *
     * @param $date
     * @return string
     */
    public function getPeriod($date);

    /**
     * Returns the minimum (most cheap) period for a given date range
     *
     * @param $start_date
     * @param $end_date
     * @return string
     */
    public function getMinimumPeriod();

    /**
     * Sets the periodCounters according to a given date range.
     * Also sets the total_days and extra_day_charge for this period
     *
     * @return $this
     */
    public function setPeriodCounters();

    /**
     * Returns the periodCounters attribute
     *
     * @return mixed
     */
    public function getPeriodCounters();

    /**
     * Returns the total days of the offer
     *
     * @return mixed
     */
    public function getTotalDays();
}