<?php namespace App;

use App\Traits\AccessorsMutators\OfferAccessorsMutators;
use App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators;
use App\Traits\Relationships\OfferRelationships;
use Illuminate\Database\Eloquent\Model;

class Offer extends Model
{
    use OfferAccessorsMutators,
        PickupDropoffLocationAccessorsMutators,
        OfferRelationships;

	protected $fillable = [
	'pickup_date',
	'pickup_location',
	'pickup_time',
	'dropoff_date',
	'dropoff_location',
	'dropoff_time',
	'comment',
	'title',
	'customer_id',
	'listing_id',
	'offered_price',
	'one_off',
	'enabled',
	'sent',
	'note',
	];


    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'pickup_date'   => 'date:Y-m-d',
        'dropoff_date'  => 'date:Y-m-d',
        'sent_at'       => 'datetime',
    ];

    public function isReady()
    {
        if(
            $this->enabled &&
            ! empty($this->slug) &&
            ! empty($this->title) &&
            ! empty($this->customer_id) &&
            ! empty($this->pickup_date) &&
            ! empty($this->pickup_time) &&
            ! empty($this->pickup_location) &&
            ! empty($this->dropoff_date) &&
            ! empty($this->dropoff_time) &&
            ! empty($this->dropoff_location) &&
            ! empty($this->listing_id) &&
            ! empty($this->offered_price)
        )
        {
            return true;
        }

        return false;
    }

}
