<?php

namespace App;

use App\Traits\AccessorsMutators\FeedbackAccessorsMutators;
use App\Traits\Relationships\FeedbackRelationships;
use Lang;

class Feedback extends BaseModel
{
    use FeedbackAccessorsMutators,
        FeedbackRelationships;

    protected $fillable = [
        'customer_id',
        'value_for_money_rating',
        'cleanliness_rating',
        'desk_service_rating',
        'pickup_rating',
        'dropoff_rating',
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'feedbacks';


    protected function performCustomCreationTasks(array $attributes = [])
    {
        // get listing
        // if site is cretanrentals, we have only group, so fetch the first listing of this group
//        if(config('appportal.name') == 'cretanrentals'){
//            $listing = Listing::where('group_id',$attributes['group'])->first();
//        }
//        else{
//            $listing = Listing::find($attributes['listing_id']);
//
//        }
//
//        if (!empty($listing))
//        {
//            // Fetch site based on current portal
//            $site = Site::where('name', config('appportal.name'))->first();
//            $this->site = $site ? $site->id : null;
//
//            // get offer
//            $pickup_date_time = $attributes['pickup_date'] . ' ' . $attributes['pickup_time'];
//            $dropoff_date_time = $attributes['dropoff_date'] . ' ' . $attributes['dropoff_time'];
//            $date_range = new DateRange($pickup_date_time, $dropoff_date_time);
//
//            // get group
//            $group = config('appportal.name') == 'eurodollar' ? Group::find($listing->group_id) : Group::find($attributes['group']);
//
//            // ...listing attributes needed for reservation
//            $this->listing_id = $listing->id;
//            $this->listing_group = $group->name;
//            $this->listing_manufacturer = $listing->manufacturer;
//            $this->listing_model = $listing->model;
//            $this->listing_transmission = $listing->transmission;
//
//            // ...total days
//            $this->total_days = $date_range->getTotalDays();
//        }
//
//        // we saves here the customer in order to get an id to use for the customer_id field
//        $customer_id = DB::table('customers')->insertGetId([
//            "name"			=>  empty($attributes['customer_name']) ? null : $attributes['customer_name'],
//            "email"			=>  $attributes['customer_email'],
//            "address"		=>  empty($attributes['customer_address']) ? null : $attributes['customer_address'],
//            "country_id"	=>  empty($attributes['customer_country_id']) ? null : $attributes['customer_country_id'],
//            "telephone" 	=>  empty($attributes['customer_telephone']) ? null : $attributes['customer_telephone'],
//            "created_at"	=>  Carbon::now(),
//            "updated_at"	=>  Carbon::now(),
//            "site"          =>  $this->site
//        ]);
//        $this->customer_id = $customer_id;

        return $this;
    }

    protected function performCustomUpdateTasks(array $attributes = [])
    {
        return $this;
    }
}
