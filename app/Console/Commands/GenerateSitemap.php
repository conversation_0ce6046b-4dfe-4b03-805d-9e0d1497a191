<?php

namespace App\Console\Commands;

use App\Listing;
use App\Post;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\SitemapIndex;
use Spatie\Sitemap\Tags\Url;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate the sitemap.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        SitemapIndex::create()
            ->add('/sitemap_en.xml')
            ->add('/sitemap_el.xml')
            ->add('/sitemap_fr.xml')
            ->add('/sitemap_de.xml')
            ->add('/sitemap_it.xml')
            ->writeToFile(public_path('sitemap_index.xml'));

        // we do not wants xml sitemap fer russian
        $enabled_locales = array('en', 'el', 'fr', 'de', 'it');

        foreach($enabled_locales as $locale)
        {
            // set the locale to handle the localised routes
            App::setLocale($locale);

            $sitemap = Sitemap::create();

            // homepage of naked domain is only added to the en xml sitemap
            if($locale == 'en')
            {
                $sitemap->add(Url::create(route('home'))
                    ->setLastModificationDate(Carbon::yesterday())
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                    ->setPriority(1.0)
                );
            }

            // homepage with language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('home'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0)
            );

            // landing booking page
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('booking.show'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(1.0)
            );

            // heraklion homepage with language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('heraklion'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0)
            );

            // heraklion airport homepage with language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('heraklion_airport'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0)
            );

            // chania homepage with language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('chania'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0)
            );

            // chania airport homepage with language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('chania_airport'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0)
            );

            // rethymno homepage with language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('rethymno'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0)
            );

            // agios nikolaos homepage with language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('agios_nikolaos'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0)
            );

            // listings index language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('listings.index'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(0.9)
            );

            // listings details
            $sitemap->add(Listing::publishedEurodollar()->get());

            // posts index language segment in url
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('posts.index'), App::getLocale()))
                ->setLastModificationDate(Carbon::yesterday())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(0.8)
            );

            // posts details
            $sitemap->add(Post::published()->translatedIn($locale)->get());

            // reviews
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('reviews.index'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.6)
            );

            // rental policy
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('policy'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.6)
            );

            // insurance policy
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('insurance'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.6)
            );

            // about us
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('about'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.6)
            );

            // contact us
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('contact'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.7)
            );

            // services
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('services'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.6)
            );

            // faq
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('faq'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.6)
            );

            // privacy policy
            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('privacy'), App::getLocale()))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.4)
            );

            // safety
//            $sitemap->add(Url::create(\LaravelLocalization::localizeUrl(route('safety'), App::getLocale()))
//                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
//                ->setPriority(0.3)
//            );

            // write the sitemap to file
            $sitemap->writeToFile(public_path('sitemap_' . $locale . '.xml'));
        }

        // log execution of command
        Log::info('GenerateSitemap command executed');

        return true;
    }
}
