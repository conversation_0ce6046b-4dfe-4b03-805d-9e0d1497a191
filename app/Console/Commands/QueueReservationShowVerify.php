<?php

namespace App\Console\Commands;

use App\Jobs\SendReservationVerifyReminder;
use Illuminate\Console\Command;
use App\Reservation;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class QueueReservationShowVerify extends Command
{
	use DispatchesJobs;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verify:reservations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add reservations that admins should receive verification notification for, to queue';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
    	// Find the reservations that should receive verification notification and add them to the queue
    	// verification notification should be sent to admins fer reservations that:
    	// ended more than 1 day ago
    	// ended less than 30 days ago
    	// have not received a show verify reminder yet
    	// have not been marked as SHOW_VERIFIED
    	$goBackPeriod = 1;
    	$reservations = DB::table('reservations')
    	->select('id')
    	->where('dropoff_date', '<', Carbon::now()->subDays($goBackPeriod))
    	->where('dropoff_date', '>', Carbon::now()->subDays(45))
    	->where('show_verified', false)
    	->whereNull('verify_reminded_at')
        ->whereNull('deleted_at')
        ->limit(10)
    	->get();

//    	dd($reservations);

    	foreach($reservations as $reservationId)
        {
    		$reservation = Reservation::findOrFail($reservationId->id);

    		$this->dispatch(new SendReservationVerifyReminder($reservation));
    	}

        // log execution of command
        Log::info('QueueReservationShowVerify command executed');

    	return true;
    }
}
