<?php
namespace App\Helpers;

use Illuminate\Routing\Router;
use Illuminate\Routing\RouteCollection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class ChangePortal
{
	public static function LoadPortal(Router $router, $portal)
	{
		$rc = new RouteCollection();
		// Clear already loaded routes
		$router->setRoutes($rc);
		
		// decide which routes to load
		if($portal === 'eurodollar')
		{
			$filePath = base_path('routes/web.php');
			$appUrl = 'http://www.rentcars-crete.com';
		}
		elseif($portal === 'cretanrentals')
		{
			$filePath = app_path('../Modules/CretanRentals/Routes/web.php');
			$appUrl = 'http://' . env('CRETAN_RENTALS_DOMAIN', 'www.cretanrentals.com');
		}
		else
		{
			return false;
		}
		
		// Load routes
		$router->group([], function($router) use ($filePath)
		{
			require $filePath;
		});
		
		// Set url
		Config::set('app.url', $appUrl);
		URL::forceRootUrl( Config::get('app.url') );
		
		// Set portal config
		Config::set('appportal.name', $portal);
		// Set mail server details
		Config::set('mail.driver', config('mail_' . $portal . '.driver'));
		Config::set('mail.host', config('mail_' . $portal . '.host'));
		Config::set('mail.port', config('mail_' . $portal . '.port'));
		Config::set('mail.from', config('mail_' . $portal . '.from'));
		Config::set('mail.encryption', config('mail_' . $portal . '.encryption'));
		Config::set('mail.username', config('mail_' . $portal . '.username'));
		Config::set('mail.password', config('mail_' . $portal . '.password'));
		
		return true;
	}
}