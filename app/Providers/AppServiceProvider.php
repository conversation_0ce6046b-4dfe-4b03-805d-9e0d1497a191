<?php

namespace App\Providers;

use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Paginator::useBootstrap();
        // a blade directive to avoid formatting price over and over, used like @money($var) in template
        Blade::directive('money', function ($amount) {
            return "<?php echo number_format($amount, 0, ',', '.') . ' €'; ?>";
        });
        // a blade directive to avoid formatting price over and over, used like @money_decimal($var) in template
        Blade::directive('money_decimal', function ($amount) {
            return "<?php echo number_format($amount, 2, ',', '.') . ' €'; ?>";
        });
        // a blade directive to avoid formatting thousands over and over, used like @number_present($var) in template
        Blade::directive('number_present', function ($amount) {
            return "<?php echo number_format($amount, 0, ',', '.'); ?>";
        });
    }
}
