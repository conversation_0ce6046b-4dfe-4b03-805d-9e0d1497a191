<?php $__env->startSection('title'); ?><?php echo Lang::get('home.page_title'); ?><?php $__env->stopSection(); ?>

<?php $__env->startSection('meta_description'); ?><?php echo Lang::get('home.meta_description'); ?><?php $__env->stopSection(); ?>

<?php $__env->startSection('head_extra'); ?>
<link rel="stylesheet" type="text/css" href="<?php echo asset('packages/owlcarousel/assets/owl.carousel.css'); ?>">
<link rel="stylesheet" type="text/css" href="<?php echo asset('packages/weather-icons/css/weather-icons.min.css'); ?>">
<link rel="stylesheet" type="text/css" href="<?php echo asset('css/pages.homepage.css'); ?>">
<?php $__env->stopSection(); ?>  

<?php $__env->startSection('footer_js'); ?>
    <script src="<?php echo asset('packages/owlcarousel/owl.carousel.js'); ?>"></script>
    <script src="<?php echo asset('packages/open-weather/openWeather.js'); ?>"></script>
    <script src="<?php echo asset('js/jquery.homepage.js'); ?>"></script>
<?php $__env->stopSection(); ?>  

<?php $__env->startSection('body'); ?>

<!-- SLIDER
================================================== -->
<section class=" bck dark   bck_slider  ">
    <div class="photo_gradient">
    	<div class="container ">
    		<div class="desktop-12  columns " id="slider_container"  >
    			 <div id="slider_title" class="text  " >
    			 <h1 class="text color white main-title condensed normal text-shadow"><?php echo Lang::get('common.slider_title'); ?></h1>
    			 <h2 class="text theme  main-subtitle condensed text-shadow"><?php echo Lang::get('common.slider_subtitle'); ?></h2>
    			 <a href="#" id="move_to_search" class="button button-small margin-top button-transparent-inverse text condensed"> <?php echo Lang::get('common.search_now'); ?></a>
    			 </div>
    		</div>
    	</div>
    </div>	
</section>



<!-- FORM
================================================== -->
<section class=" padding-large stripes  " id="search_holder">
	<div class="container ">
		<div class="desktop-12 padding-bottom columns   "  >
		<h1 class="text bold condensed"><?php echo Lang::get('common.check_availability'); ?></h1>
		</div>
		  <?php echo Form::open(array('class'=>'main')); ?>

		       <?php echo $__env->make('frontend.listings.partials._search_form_homepage', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
		  <?php echo Form::close(); ?>

    </div>	 
</section>

   

    

   


 

<!-- WHAT WE OFFER
================================================== -->
<section class=" padding-large"  id="container_where">
	<div class="container ">
		<div class="desktop-12 padding-bottom columns   "  >
		<h1 class="text bold condensed"><?php echo Lang::get('services.what_offer'); ?></h1>
		</div>
	</div>
	<div class="container desktop-margin">	
		<div class="desktop-4  columns   "  >
		    <div class="media">
                <img src="<?php echo asset('images/icons/protection.png'); ?>" width="55"   alt="" class="img margin-bottom-small padding-right" />
              <div class="bd">
                <h2 class="margin-bottom-small condensed bold" ><?php echo Lang::get('services.insurance'); ?></h2>
			     <p class="text book small"><?php echo Lang::get('services.insurance_text'); ?></p>
              </div>
            
            </div>
		</div>
		
		<div class="desktop-4  columns   "  >
		    <div class="media">
                <img src="<?php echo asset('images/icons/no_card.png'); ?>" width="55"   alt="" class="img margin-bottom-small padding-right" />
              <div class="bd">
                <h2 class="margin-bottom-small condensed bold" ><?php echo Lang::get('services.no_credit_card'); ?></h2>
			     <p class="text book small"><?php echo Lang::get('services.no_credit_card_text'); ?></p>
              </div>
            
            </div>
		</div>
		
		<div class="desktop-4  columns   "  >
		    <div class="media">
                <img src="<?php echo asset('images/icons/no_deposit.png'); ?>" width="55"   alt="" class="img margin-bottom-small padding-right" />
              <div class="bd">
                <h2 class="margin-bottom-small condensed bold" ><?php echo Lang::get('services.no_deposit'); ?></h2>
			     <p class="text book small"><?php echo Lang::get('services.no_deposit_text'); ?></p>
              </div>
            
            </div>
		</div>
		
		
		
		
		
		
    </div>
	<div class="container">	
	   <div class="desktop-4  columns   "  >
		    <div class="media">
                <img src="<?php echo asset('images/icons/check.png'); ?>" width="55"   alt="" class="img margin-bottom-small padding-right" />
              <div class="bd">
                <h2 class="margin-bottom-small condensed bold" ><?php echo Lang::get('services.inclusive_rates'); ?></h2>
			     <p class="text book small"><?php echo Lang::get('services.inclusive_rates_text'); ?></p>
              </div>
            
            </div>
		</div>
	   <div class="desktop-4  columns   "  >
		    <div class="media">
                <img src="<?php echo asset('images/icons/airplane.png'); ?>" width="55"   alt="" class="img margin-bottom-small padding-right" />
              <div class="bd">
                <h2 class="margin-bottom-small condensed bold" ><?php echo Lang::get('services.airport'); ?></h2>
			     <p class="text book small"><?php echo Lang::get('services.airport_text'); ?></p>
              </div>
            
            </div>
		</div>
		
	   <div class="desktop-4  columns   "  >
		    <div class="media">
                <img src="<?php echo asset('images/icons/odometer.png'); ?>" width="55"   alt="" class="img margin-bottom-small padding-right" />
              <div class="bd">
                <h2 class="margin-bottom-small condensed bold" ><?php echo Lang::get('services.kilometers'); ?></h2>
			     <p class="text book small"><?php echo Lang::get('services.kilometers_text'); ?></p>
              </div>
            
            </div>
		</div>
		
	
	</div>
	<div class="container text right margin-top-large">
		<div class="desktop-12  columns  phone-centered "  >
		<a href="<?php echo route('services'); ?>" class="button button-transparent text condensed"><i class="fa fa-angle-right bold text large color theme"></i> <?php echo Lang::get('services.all_services'); ?></a>
		</div>
	</div>
</section>


<!-- SLIDER
	================================================== -->
<section class=" bck dark  testimonials padding-large ">
	<div class="container padding-small">
		<div class="desktop-8 offset-2 text center  columns "  >
		<!-- Set up your HTML -->
		<div class="owl-carousel">
		  <div> 
		  <h2 class="text color white book">“<?php echo Lang::get('common.testimonial1_text'); ?>”</h2>
		  <h4 class="text color white bold small_caps margin-top padding-top-small"><?php echo Lang::get('common.testimonial1_initials'); ?></h4>
		  <h4 class="text color white book small_caps  "><?php echo Lang::get('common.testimonial1_country'); ?></h4>  
		  </div>
		  <div> 
		  <h2 class="text color white book">“<?php echo Lang::get('common.testimonial2_text'); ?>”</h2>
		  <h4 class="text color white bold small_caps margin-top padding-top-small"><?php echo Lang::get('common.testimonial2_initials'); ?></h4>
		  <h4 class="text color white book small_caps  "><?php echo Lang::get('common.testimonial2_country'); ?></h4>  
		  </div>
		  <div> 
		  <h2 class="text color white book">“<?php echo Lang::get('common.testimonial3_text'); ?>”</h2>
		  <h4 class="text color white bold small_caps margin-top padding-top-small"><?php echo Lang::get('common.testimonial3_initials'); ?></h4>
		  <h4 class="text color white book small_caps  "><?php echo Lang::get('common.testimonial3_country'); ?></h4>  
		  </div>
		</div>
			 
			 
		</div>
	</div>
</section>


<!-- WHERE TO FIND US
	================================================== -->
<section class=" padding-large stripes">
	<div class="container ">
		<div class="desktop-6  columns   "  >
			<h1 class="text bold condensed"><?php echo Lang::get('where.where'); ?></h1>
		</div>
		<div class="desktop-6 text    columns  hide-phone  "  >
			<div class="media on-right" style="width:170px; overflow:visible">
				  <div class="img text right">
					 <h2 style="font-size:34px;"><i class="wi weather-icon"></i>  </h2>
					<h2 class="text bold condensed" style="font-size:28px;"><i class="wi wi-thermometer"></i> <span class="weather-temperature"></span>   </h2>
				  </div>
				  <div class="bd padding-left-small text bold large  "  >
				  <h6 class="  condensed text bold small small_caps"><?php echo Lang::get('common.today'); ?></h6>
					<h6 class="weather-description condensed text small"></h6>
					 
				</div>
			 </div>
		</div>
		<div class="desktop-3 tablet-4 columns   "  > 
			<h2 class="margin-bottom-small padding-top-small condensed bold"><?php echo Lang::get('where.headquarters'); ?></h2>
			<ul class="text condensed color grey  "> 
				<li id="place_1"><i class="fa fa-angle-right bold text color theme"></i> <?php echo Lang::get('where.heraklion'); ?></li>
			</ul>
			<h2 class="margin-bottom-small condensed bold margin-top"><?php echo Lang::get('where.branches'); ?></h2>
			<ul class="text condensed color grey  "> 
				<li id="place_2"><i class="fa fa-angle-right bold text color theme"></i> <?php echo Lang::get('where.heraklion_airport'); ?></li>
				<li id="place_3"><i class="fa fa-angle-right bold text color theme"></i> <?php echo Lang::get('where.chania_airport'); ?></li>
				<li id="place_4"><i class="fa fa-angle-right bold text color theme"></i> <?php echo Lang::get('where.nikolaos_port'); ?></li>
				<li id="place_5"><i class="fa fa-angle-right bold text color theme"></i> <?php echo Lang::get('where.rethymno'); ?></li>
				<li id="place_6"><i class="fa fa-angle-right bold text color theme"></i> <?php echo Lang::get('where.matala'); ?></li>
				<li id="place_7"><i class="fa fa-angle-right bold text color theme"></i> <?php echo Lang::get('where.siteia'); ?></li>
			</ul>
			 
		</div>
		<div class="desktop-9 tablet-8   text right columns  margin-top-large "  >
			<div style="posisiton:relative" id="map"> 
			<img src="images/map.png" class="responsive" alt=""  />
			<div  id="marker_1"><i class="fa fa-map-marker    "></i></div>
			<div  id="marker_2"><i class="fa fa-map-marker    "></i></div>
			<div  id="marker_3"><i class="fa fa-map-marker    "></i></div>
			<div  id="marker_4"><i class="fa fa-map-marker    "></i></div>
			<div  id="marker_5"><i class="fa fa-map-marker    "></i></div>
			<div  id="marker_6"><i class="fa fa-map-marker    "></i></div>
			<div  id="marker_7"><i class="fa fa-map-marker    "></i></div>
 			</div>
			 
		</div>
		<div class="desktop-12   text right columns  phone-centered margin-top  "  >
		<a href="<?php echo route('branches'); ?>" class="button button-transparent text condensed "><i class="fa fa-angle-right bold text large color theme"></i> <?php echo Lang::get('where.all_branches'); ?></a>
		</div>
		 
	</div>
</section>


<!-- SEARCH TEASER
	================================================== -->
<section class=" bck theme  ">
	<div class="container padding">
		<div class="desktop-8    columns phone-centered "    >
			<h2 class="text  condensed     text  padding-top-tiny padding-bottom-tiny  " style="font-size:26px; line-height:30px;"   ><?php echo Lang::get('common.see_fleet_text'); ?></h2> 
			 
		</div>
		<div class="desktop-4 text right    columns phone-centered"  >
			<a href="<?php echo route('listings.index'); ?>" class="button text condensed"><?php echo Lang::get('common.see_fleet'); ?></a>
			 
		</div>
	</div>
</section>

 
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.frontend.base', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>