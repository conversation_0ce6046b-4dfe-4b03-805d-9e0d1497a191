<?php

namespace App;

use App\Traits\Relationships\CategoryRelationships;

class Category extends BaseModel 
{
    use CategoryRelationships;

    protected $fillable = [
        'name',
        'description'
    ];

    /**
     *
     * @param array $attributes
     *
     * @return void
     */
    protected function performCustomCreationTasks(array $attributes = [])
    {
        return $this;
    }

    /**
     *
     * @param array $attributes
     *
     * @return void
     */
    protected function performCustomPostCreationTasks(array $attributes = [])
    {
        if (!empty($attributes['listings']))
        {
            $this->listings()->attach($attributes['listings']);
        }
        return $this;
    }

    /**
     * @param array $attributes
     *
     * @return void
     */
    protected function performCustomUpdateTasks(array $attributes = [])
    {
        if (!empty($attributes['listings']))
        {
            $this->listings()->sync($attributes['listings']);
        }

        return $this;
    }

    // Relationships
    public function listings()
    {
        return $this->belongsToMany('App\Listing');
    }
}
