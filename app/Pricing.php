<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Pricing extends Model {

    protected $table = 'group_period';

    protected $fillable = [
        'price',
        'price_cretan',
        'available',
        'available_cretan',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (Pricing $pricing) {
            LogPricing::create([
                'group_id'      => $pricing->group_id,
                'period_id'     => $pricing->period_id,
                'price'         => $pricing->price,
                'price_cretan'  => $pricing->price_cretan,
            ]);
        });
        static::updated(function (Pricing $pricing) {
//            $data = [
//                'group_id'      => $pricing->group_id,
//                'period_id'     => $pricing->period_id,
//                'created_at'        =>
//                    LogPricing
//                        ::where(DB::raw('DATE_FORMAT(created_at , "%Y-%m-%d")'), date('Y-m-d'))
//                        ->where('group_id', $pricing->group_id)
//                        ->where('period_id', $pricing->period_id)
//                        ->first()
//                        ->created_at ?? Carbon::now(),
//            ];
//            LogPricing::updateOrCreate(
//                $data,
//                [
//                    'price'             => $pricing->price,
//                    'price_cretan'      => $pricing->price_cretan,
//                    'old_price'         => $pricing->getOriginal('price'),
//                    'old_price_cretan'  => $pricing->getOriginal('price_cretan'),
//                ]
//            );
            LogPricing::create(
                [
                    'group_id'          => $pricing->group_id,
                    'period_id'         => $pricing->period_id,
                    'price'             => $pricing->price,
                    'price_cretan'      => $pricing->price_cretan,
                    'old_price'         => $pricing->getOriginal('price'),
                    'old_price_cretan'  => $pricing->getOriginal('price_cretan'),
                ]
            );
        });
    }
}
