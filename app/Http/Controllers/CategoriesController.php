<?php namespace App\Http\Controllers;

use App\Category;
use App\Services\Validation\ValidationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;
use Exception;

class CategoriesController extends Controller {
    // TODO: move to Hodor, It is an admin only controller

	/**
	 * Display a listing of the resource.
	 *
	 */
	public function index()
	{

		$this->view_data['categories'] = Category::orderBy('id', 'DESC')->paginate(20);

		$this->view_data['content_group_selected'] = 'current';
        $this->view_data['categories_selected'] = 'current';

		return View::make('admin.categories.index', $this->view_data);
	}


	/**
	 * Show the form for creating a new resource.
	 *
	 */
	public function create()
	{
        $this->view_data['content_group_selected'] = 'current';
        $this->view_data['categories_selected'] = 'current';

        return View::make('admin.categories.create', $this->view_data);
	}


	/**
	 * Store a newly created resource in storage.
	 *
	 */
	public function store(Request $request)
	{
        try
        {
            $category = Category::create($request->all());

            // return to the groups list
            return Redirect::route('admin.categories.index', $category->id)->with('success', 'Category ' . $category->name . ' created');
        } catch (ValidationException $e)
        {
            return Redirect::back()->withInput()->withErrors($e->getErrors());
        } catch (Exception $e)
        {
            return Redirect::back()->withInput()->withErrors($e->getMessage());
        }
	}



	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  int  $id
	 *
	 */
	public function edit($id)
	{
		$category = Category::findOrFail($id);
		$this->view_data['category'] = $category;

		return View::make('admin.categories.edit', $this->view_data)->with('category', $category);
	}

	/**
	 * Update the specified resource in storage.
	 * PUT /groups/{id}
	 *
	 * @param  int $id
	 * @param Request $request
	 */
	public function update(Request $request, $id)
	{
		$category = Category::findOrFail($id);
		if($category->locked){
			return Redirect::route('admin.categories.index')->withErrors('Category ' . $category->name . ' cannot be edited');
		}

		if($request->get('name')){
			$category->name = $request->get('name');
		}
		if($request->get('description')){
			$category->description = $request->get('description');
		}
        try
        {
        	$category->save();
        } catch (Exception $e)
        {
        	return Redirect::back()->withInput()->withErrors($e->getMessage());
        }

        return Redirect::route('admin.categories.edit', $category->id)->with('success', 'Category ' . $category->id . ' saved');
	}


	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 */
	public function destroy($id)
	{
		$category = Category::findOrFail($id);

		if($category->locked){
			return Redirect::route('admin.categories.index')->withErrors('Category ' . $category->name . ' cannot be deleted');
		}

        $category->delete();

        return Redirect::route('admin.categories.index')->with('success', 'Category ' . $category->name . ' deleted');
	}

}
