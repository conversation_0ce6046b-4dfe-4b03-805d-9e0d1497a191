<?php namespace App\Http\Controllers;

use App\Customer;
use App\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class CustomersController extends Controller {

	/**
	 * Display a listing of the resource.
	 *
	 */
	public function index(Request $request)
	{
        $filter = $request->get('filter');
        $this->view_data['filter'] = $filter;

        $this->view_data['sites'] = Site::all()->pluck('name','id')->toArray();
        $this->view_data['siteSelected'] = $request->get('siteSelected');
        if(!in_array($this->view_data['siteSelected'], array_keys($this->view_data['sites']))){
        	$this->view_data['siteSelected'] = key($this->view_data['sites']);
        }

		$this->view_data['customers'] = Customer::where('site', '=', $this->view_data['siteSelected'])->orderBy('id', 'DESC')->paginate(20);

		$this->view_data['reservations_group_selected'] = 'current';
        $this->view_data['customers_selected'] = 'current';

		return View::make('admin.customers.index', $this->view_data);
	}


}
