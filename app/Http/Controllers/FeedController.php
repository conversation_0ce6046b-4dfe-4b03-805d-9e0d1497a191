<?php namespace App\Http\Controllers;

use App\Listing;
use App\Repositories\Contracts\ListingRepositoryInterface;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Request;

class FeedController extends Controller {

    private $listingRepo;

    public function __construct(ListingRepositoryInterface $listingRepo) {
        $this->listingRepo = $listingRepo;
        parent::__construct();

        $this->view_data['canonical_url'] = Request::url();
    }


    /**
     * Provide the cars csv export
     *
     * @return mixed
     */
	public function exportCarsCsv()
	{
	    App::setLocale('en');

	    $fileName = 'eurodollar-cars-feed.csv';
        $listings = Listing::where('published_eurodollar', '1')
            ->get();

//        foreach ($listings as $listing) {
//            // get similar listings
//            $suggested = $this->listingRepo->getSuggested($listing->id, $listing->group_id);
//            $suggetsed_ids_string = '';
//            if( $listing->id == 2)
//            {
//                dd($suggested->implode('id', ','));
//
//            }
//        }

//        dd($listings);

        $headers = array(
            "Content-type"        => "text/csv; charset=UTF-8",
            "Content-Encoding"    => "UTF-8",
            "Content-Disposition" => "attachment; filename=$fileName",
            "Pragma"              => "no-cache",
            "Cache-Control"       => "must-revalidate, post-check=0, pre-check=0",
            "Expires"             => "0"
        );

        $columns = array(
            'Property ID',
            'Property name',
            'Final URL',
            'Image URL',
            'Destination Name',
            'Description',
            'Price',
            'Sale price',
            'Formatted price',
            'Formatted sale price',
            'Star rating',
            'Category',
            'Contextual keywords',
            'Address',
            'Tracking template',
            'Custom parameter',
            'Destination URL',
            'Final mobile URL',
            'Similar property IDs',
            'Android app link',
            'iOS app link',
            'iOS app store ID',
        );

        $callback = function() use($listings, $columns) {
            $file = fopen('php://output', 'w');
//            fputcsv($file, $columns);

            foreach ($listings as $listing)
            {
                // get similar/suggested listings
                $suggested = $this->listingRepo->getSuggested($listing->id, $listing->group_id);
                $suggested_ids_string = '';

                if($suggested->count() > 0)
                {
                    $suggested_ids_string = $suggested->implode('id', ',');
                }

                $row['Property ID']             = $listing->id;
                $row['Property name']           = $listing->title;
                $row['Final URL']               = route('listings.show', $listing->slug);
                $row['Image URL']               = empty($listing->image) ? '' : asset($listing->image);
                $row['Destination name']        = '';
                $row['Description']             = '';
                $row['Price']                   = ''; // todo find price logic
                $row['Sale price']              = ''; // todo find sale price logic
                $row['Formatted price']         = '';
                $row['Formatted sale price']    = '';
                $row['Star rating']             = '';
                $row['Category']                = $listing->group->description;
                $row['Contextual keywords']     = '';
                $row['Address']                 = '';
                $row['Tracking template']       = '';
                $row['Custom parameter']        = '';
                $row['Destination URL']         = '';
                $row['Final mobile URL']        = '';
                $row['Similar property IDs']    = $suggested_ids_string;
                $row['Android app link']        = '';
                $row['iOS app link']            = '';
                $row['iOS app store ID']        = '';

                fputcsv($file, array(
                    $row['Property ID'],
                    $row['Property name'],
                    $row['Final URL'],
                    $row['Image URL'],
                    $row['Destination name'],
                    $row['Description'],
                    $row['Price'],
                    $row['Sale price'],
                    $row['Formatted price'],
                    $row['Formatted sale price'],
                    $row['Star rating'],
                    $row['Category'],
                    $row['Contextual keywords'],
                    $row['Address'],
                    $row['Tracking template'],
                    $row['Custom parameter'],
                    $row['Destination URL'],
                    $row['Final mobile URL'],
                    $row['Similar property IDs'],
                    $row['Android app link'],
                    $row['iOS app link'],
                    $row['iOS app store ID'],
                ));
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
	}

}
