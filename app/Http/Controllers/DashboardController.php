<?php namespace App\Http\Controllers;


use App\Listing;
use App\Site;
use App\Reservation;
use Illuminate\Support\Facades\View;

class DashboardController extends Controller {

	/**
	 * Admin dash board page
	 *
	 * @return mixed
	 */
	public function getIndex()
	{
		// total listings in db
		$this->view_data['nr_listings'] = Listing::all()->count();

		// total pending reservations in db
		$this->view_data['nr_reservations'] = Reservation::pending()->count();

		// upcoming pending reservations to be serviced
		$this->view_data['reservations_upcoming'] = Reservation::upcoming()->orderBy('id', 'DESC')->get();

		// site
        $this->view_data['reservations_site_dropdown_options'] = Site::all()->pluck('name','id')->toArray();

        // selected menu item
        $this->view_data['dashboard_selected'] = 'current';

		return View::make('admin.dashboard', $this->view_data);
	}

}
