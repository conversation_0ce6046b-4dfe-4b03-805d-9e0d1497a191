<?php namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;

class AuthController extends Controller {

	/**
	 * Shows the login form
	 */
	public function showLogin()
	{
		// check if the user is already logged in
		if ( Auth::check() )
		{
			// redirect to the dashboard
			return Redirect::route('admin.dashboard')->with('success', 'You are already logged in');
		}

		return View::make('frontend.login_form', $this->view_data);
	}

	/**
	 * Handles the post request to log a user in
	 */
	public function handleLogin(Request $request)
	{

		// cache input vars
		$username = $request->get('username');
		$password = $request->get('password');

		// set rules for data validation
		 $validator = Validator::make(
		    array(
		    	'username' => $username,
		    	'password' => $password
			),
		    array(
		    	'username' => array('required'),
		    	'password' => array('required')
			)
		 );

		// perform the validation
		if ( $validator->fails() )
	    {
	        return Redirect::route('login')->withErrors($validator)->withInput();
	    }

		// perform the actual authentication
		// uses laravel's auth
		// gather fiedls for validation
		$credentials = $request->only('username', 'password');
		// check if a remember me token exists
		$remember = $request->has('remember');

		if (Auth::attempt($credentials, $remember))
		{
		    return Redirect::intended('/admin/dashboard');
		}

		return Redirect::route('login')->withErrors(array('credentials' => 'Username and password combination invalid'))->withInput();
	}

}
