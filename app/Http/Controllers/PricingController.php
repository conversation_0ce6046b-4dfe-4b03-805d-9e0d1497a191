<?php

namespace App\Http\Controllers;

use App\Group;
use App\Http\Requests\AvailabilityRequest;
use App\Http\Requests\PricingRequest;
use App\Period;
use App\Pricing;
use Carbon\Carbon;
use Illuminate\Http\Request;

class PricingController extends Controller {

    /**
     * Display a listing of the resource.
     *
     *
     */
    public function index()
    {
//        $this->view_data['groups'] = $this->groupRepo->all();
        $this->view_data['groups'] = Group::all();

        $this->view_data['content_group_selected'] = 'current';
        $this->view_data['pricing_selected'] = 'current';

        return view('admin.pricing.index', $this->view_data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $groupId
     *
     */
    public function edit($groupId)
    {
//        $this->view_data['group'] = $this->groupRepo->findOrFail($groupId);
        $this->view_data['group'] = Group::findOrFail($groupId);

//        $prices = $this->pricingRepo->findBy('group_id', $groupId);
        $prices = Pricing::where('group_id', $groupId)->get();

        // get period model for each price
        foreach ($prices as $price)
        {
//            $price['period'] = $this->periodRepo->find($price->period_id);
            $price['period'] = Period::find($price->period_id);
        }

        // group pricing periods by month and generate title from month
        $this->view_data['prices'] = $prices->groupBy(function ($price)
        {
            $date = Carbon::parse($price->period->start_date);
            $price['period']['title'] = date('F', strtotime($date));
            return $date->month;
        });

        $this->view_data['content_group_selected'] = 'current';
        $this->view_data['pricing_selected'] = 'current';

        return view('admin.pricing.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PricingRequest $request
     * @return array
     */
    // manually removed the second argument ($id) of the "normal" method signature
    public function update(PricingRequest $request): array
    {
//        $pricing = $this->pricingRepo
        $pricing = Pricing::where('group_id', $request->input('groupId'))
            ->where('period_id', $request->input('periodId'))
            ->first();

        if ($pricing)
        {
        	$pricingField = 'price' . ($request->input('site') === 'cretanrentals' ? '_cretan' : '');
//            $pricingUpdated = $this->pricingRepo->update([$pricingField => $request->input('newValue')], $pricing->id);
            $pricing_model  = Pricing::find($pricing->id);
            $pricingUpdated = $pricing_model->update([$pricingField => $request->input('newValue')]);

            if ($pricingUpdated)
            {
                return [
                    'status'  => true,
                    'message' => 'Updated',
                ];
            }else{
                return [
                    'status'  => false,
                    'message' => 'An Error Occurred',
                ];
            }
        }
        return [
            'status'  => false,
            'message' => 'An Error Occurred',
        ];
    }

    /**
     * Update the specified resource in storage.
     *
     * @param AvailabilityRequest $request
     * @param  int $id
     *
     */
    public function availabilityUpdate(AvailabilityRequest $request)
    {
//        $pricing = $this->pricingRepo
        $pricing = Pricing::where('group_id', $request->input('groupId'))
            ->where('period_id', $request->input('periodId'))
            ->first();

        if ($pricing)
        {
        	$availableField = 'available' . ($request->input('site') === 'cretanrentals' ? '_cretan' : '');
//            $pricingUpdated = $this->pricingRepo->update([$availableField => $request->input('newValue')], $pricing->id);
            $pricing_model  = Pricing::find($pricing->id);
            $pricingUpdated = $pricing_model->update([$availableField => $request->input('newValue')]);

            if ($pricingUpdated)
            {
                return [
                    'status'  => true,
                    'message' => 'Updated',
                ];
            }else{
                return [
                    'status'  => false,
                    'message' => 'An Error Occurred',
                ];
            }
        }
        return [
            'status'  => false,
            'message' => 'An Error Occurred',
        ];
    }
}
