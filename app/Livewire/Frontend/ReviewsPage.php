<?php

namespace App\Livewire\Frontend;

use App\GoogleReview;
use App\Location;
use Livewire\Component;
use Livewire\WithPagination;

class ReviewsPage extends Component
{
    use WithPagination;


    public $locations;
    public $location;
    public $amount = 15;

    public function loadMore()
    {
        $this->amount += 15;
    }

    public function render()
    {
        $locations = Location::whereHas('reviews')->get();

        if($this->location == NULL) {
            $reviews = GoogleReview::showable()
                ->orderBy('review_datetime', 'desc')
                ->take($this->amount)
                ->get();
        } else {
            $reviews = GoogleReview::showable()
                ->where('location_id', $this->location)
                ->orderBy('review_datetime', 'desc')
                ->take($this->amount)
                ->get();
        }

        if($this->location == NULL) {
            $totalReviews = GoogleReview::showable()->get();
        } else {
            $totalReviews = GoogleReview::showable()->where('location_id', $this->location)->get();
        }

        $amount = $this->amount;
        $this->locations = $locations;

        return view('livewire.frontend.reviews-page', compact('reviews', 'locations', 'amount', 'totalReviews'));
    }
}
