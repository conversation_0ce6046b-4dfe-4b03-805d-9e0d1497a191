<?php namespace App;

use App\Services\ImageHandling\ImageableInterface;
use App\Services\ImageHandling\ImageableTrait;
use App\Traits\AccessorsMutators\LocationAccessorsMutators;
use App\Traits\Relationships\LocationRelationships;
use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class Location extends BaseModel implements ImageableInterface, TranslatableContract {

    use ImageableTrait;
	use Translatable,
        LocationAccessorsMutators,
        LocationRelationships;

	public $translatedAttributes = [
	    'name',
        'description',
        'area',
        'pickup_description',
        'dropoff_description',
        ];

    protected $fillable = [
        'area',
        'name',
        'description',
        'remote_location',
        'remote_location_short',
        'pickup_description',
        'dropoff_description',
    ];

	/**
	 * Overrides the parent method to add image handling functionality
	 *
	 * @param array $attributes
	 *
	 * @return void
	 */
	protected function performCustomCreationTasks(array $attributes = [])
	{
        // handle image data
        if (! empty($attributes['image']))
        {
            $this->storeImage('img/locations/');
        }

		return $this;
	}

	/**
	 * Overrides the parent method to add image handling functionality
	 *
	 * @param array $attributes
	 *
	 * @return void
	 */
	protected function performCustomUpdateTasks(array $attributes = [])
	{
        // handle image data
        if (! empty($attributes['image']))
        {
            $this->deleteImage()
                ->storeImage('img/locations/');
        }

        $imageFilenames = isset($attributes['imageFilename']) ? $attributes['imageFilename'] : array();
        $this->images()->detach();
        if (!empty($imageFilenames))
        {
            foreach($imageFilenames as $imageFilename){
                $img = Image::where('filename', '=', $imageFilename)->first();
                if(!empty($img)){
                    $this->images()->attach($img->id);
                }
            }
        }
		return $this;
	}

    /**
     * Location relationship with reviews
     */

    public function reviews ()
    {
        return $this->hasMany(GoogleReview::class);
    }

    /**
     * Overrides the parent method to add image handling functionality
     *
     * @param array $attributes
     *
     * @return void
     */
    protected function performCustomPostCreationTasks(array $attributes = [])
    {
        $imageFilenames = isset($attributes['imageFilename']) ? $attributes['imageFilename'] : array();
        if (!empty($imageFilenames))
        {
            foreach($imageFilenames as $imageFilename){
                $img = Image::where('filename', '=', $imageFilename)->first();
                if(!empty($img)){
                    $this->images()->attach($img->id);
                }
            }
        }
        return $this;
    }

    /**
     * Deletes image data and then calls the delete method of (grand)parent to delete the model
     * @return mixed
     */
    public function delete()
    {
        // delete image data
        $this->deleteImage();

        // proceed with actual model deletion
        return parent::delete();
    }
}
