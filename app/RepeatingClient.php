<?php namespace App;

use App\Traits\AccessorsMutators\RepeatingClientAccessorsMutators;

class RepeatingClient extends BaseModel
{
    use RepeatingClientAccessorsMutators;

	protected $table = 'repeating_clients';
	
	protected $fillable = [
        'listing_id',
        'email',
		'pickup_date',
		'pickup_location',
		'pickup_time',
		'dropoff_date',
		'dropoff_location',
		'dropoff_time',
		'notes',
        'site'
	];


	protected function performCustomCreationTasks(array $attributes = [])
	{
        // We need to store the site id
        // Fetch site based on current portal
        $site = Site::where('name', config('appportal.name'))->first();
        $this->site = $site? $site->id : null;
		return $this;
	}

	protected function performCustomUpdateTasks(array $attributes = [])
	{
		return $this;
	}
}