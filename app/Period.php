<?php

namespace App;

use App\Traits\Relationships\PeriodRelationships;
use Illuminate\Database\Eloquent\Model;

class Period extends Model
{
    use PeriodRelationships;

    protected $fillable = [
        'start_date',
        'end_date',
        'alias',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'start_date'    => 'datetime:Y-m-d',
        'end_date'      => 'datetime:Y-m-d',
    ];
}
