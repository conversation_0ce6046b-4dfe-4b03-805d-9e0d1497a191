<?php namespace App;

use App\Services\Offer\StaticDiscount;
use App\Services\Offer\CouponDiscount;
use App\Services\Offer\DateRange;
use App\Services\Validation\ValidationException;
use App\Traits\AccessorsMutators\PickupDropoffLocationAccessorsMutators;
use App\Traits\AccessorsMutators\ReservationAccessorsMutators;
use App\Traits\Relationships\ReservationRelationships;
use App\Traits\Scopes\ReservationScopes;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Lang;
use Illuminate\Support\Facades\App;

class Reservation extends BaseModel {

    use ReservationAccessorsMutators,
        PickupDropoffLocationAccessorsMutators,
        ReservationRelationships,
        ReservationScopes,
        SoftDeletes;

	protected $fillable = [
	'pickup_date',
	'pickup_location',
	'pickup_time',
	'dropoff_date',
	'dropoff_location',
	'dropoff_time',
	'flight_details',
	'comment',
	'customer_id',
	'discount_coupon',
    'site',
    'reference',
    'people_count',
    'remote_location_charge',
    'remote_location_charge_price',
    'show',
    'possibly_duplicated',
    'show_verified',
	];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'unduplicated_at' => 'datetime:Y-m-d',
        'verify_reminded_at' => 'datetime:Y-m-d',
    ];


	/**
	 * Reservations that start in the next 4 weeks
	 *
	 * @param $query
	 */
	public function scopeUpcoming($query)
	{
		$query->whereBetween('pickup_date', [Carbon::yesterday(), Carbon::now()->addWeeks(4)]);
	}

	/**
	 * Reservations that are in the 'pending' status
	 *
	 * @param $query
	 */
	public function scopePending($query)
	{
		$query->where('status', '=', 'pending');
	}

	/**
	 * Reservations that are in the 'scheduled' status
	 *
	 * @param $query
	 */
	public function scopeScheduled($query)
	{
		$query->where('status', '=', 'scheduled');
	}

	protected function performCustomCreationTasks(array $attributes = [])
	{

        Log::info('went into perform custom creation');
	    // in case the 'repeater' checkbox is enabled, we mark the reservation
        // as returning (regardless of input given in dropdown 'reference')
        if(isset($attributes['repeater']) && $attributes['repeater'] == 'on')
        {
            // we give the 'returning' value of the enum db field
            $this->reference = 'returning';
        }

        // check country
        if (!isset($attributes['customer_country_id']) || empty(Country::find($attributes['customer_country_id'])))
        {
            throw new ValidationException(['customer_country_id' => 'Please select country']);
        }

	    // check if the reservation originates from a commercial offer
        if (isset($attributes['commercial_offer_hash']))
        {
            // fetch commercial offer
            $commercial_offer = Offer::where('slug', '=', $attributes['commercial_offer_hash'])
                ->where('enabled', true)
                ->first();

            if (empty($commercial_offer))
            {
                throw new ValidationException(['Offer not found']);
            }

            // ...fetch listing
            $listing = Listing::find($commercial_offer->listing_id);

            // get listing group
            $group = Group::find($listing->group_id);

            // We need to store the site id (reservation & customer)
            // Fetch site based on current portal
            $site = Site::where('name', config('appportal.name'))->first();
            $this->site = $site? $site->id : null;

            // get rental offer
            $pickup_date_time = $attributes['pickup_date'] . ' ' . $attributes['pickup_time'];
            $dropoff_date_time = $attributes['dropoff_date'] . ' ' . $attributes['dropoff_time'];
            $date_range = new DateRange($pickup_date_time, $dropoff_date_time);


            // ...listing attributes needed for reservation
            $this->listing_id           = $listing->id;
            $this->listing_group        = $group->name;
            $this->listing_manufacturer = $listing->manufacturer;
            $this->listing_model        = $listing->model;
            $this->listing_transmission = $listing->transmission;
            $this->listing_fuel         = $listing->fuel;

            // ...total price and total discount price
            $this->total_price_initial      = $commercial_offer->offered_price;
            $this->total_discount_amount    = 0;
            $this->final_price              = $commercial_offer->offered_price;

            // ...total days
            $this->total_days = $date_range->getTotalDays();

            // ...extra day charge
            $this->extra_day_charge             = 0;
            // ...extra miles charge
            $this->extra_miles_charge           = 0;
            $this->extra_miles_charge_price     = 0;
            // ...after hours charge
            $this->after_hours_charge           = 0;
            $this->after_hours_charge_price     = 0;
            // ...fuel plan charge
            $this->fuel_plan_charge             = 0;
            $this->fuel_plan_charge_price       = 0;
            // ...remote location charge
            $this->remote_location_charge       = 0;
            $this->remote_location_charge_price = 0;

            // we saves here the customer in order to get an id to use for the customer_id field
            $customer = $this->handleCustomer($attributes);
            $this->customer_id = $customer->id;

            $this->language_id = DB::table('languages')->where('locale', App::getLocale())->value('id');

            // ...flag from commercial offer
            $this->from_commercial_offer    = true;
            $this->offer_id                 = $commercial_offer->id;

            // we saves here the reservation in order to get an id to use for the reservation_to_accessory table
            $this->save();

            // after saving the reservation, we calculate a hash
            // in order to be used for the feedback page slug
            $this->feedback_hash = md5( rand(0, 1000) );

            // and create the uuid too
            $this->uuid = (string) Str::orderedUuid();

            // and save again
            $this->save();

            // then grab accessories and create appropriate DB rows
            $accessories = $commercial_offer->accessories;

            foreach($accessories as $accessory)
            {
                // prepare the data to insert to the reservation_to_accessory row
                $rta_attributes = [
                    'reservation_id'    => $this->id,
                    'accessory_id'      => $accessory->id
                ];

                $reservationToAccessory = ReservationToAccessory::create($rta_attributes);

                $reservationToAccessory->accessory_name     = $accessory->name;
                $reservationToAccessory->accessory_price    = 0;
                // commercial offers have only not a configurable accessory occurence
                $reservationToAccessory->accessory_amount   = 1;

                $reservationToAccessory->save();
            }

            return $this;
        }

        // this part is executed for "original" reservations (not originating from a commercial offer)

		// firstly check the flight details input versus the pickup location
		// if the pickup location is from an airport then the flight details is mandatory

        // we commented it out, we do not want it at this time
//		$pickup = Location::find($attributes['pickup_location']);
//
//		if ( $pickup->isAirport() )
//		{
//			if ( empty($attributes['flight_details']) )
//			{
//				throw new ValidationException(['flight_details' => 'The Flight Details field is required.']);
//			}
//		}

		// get listing
        // if site is cretanrentals, we have only group, so fetch the first listing of this group
        if(config('appportal.name') == 'cretanrentals')
        {
            $listing = Listing::where('group_id',$attributes['group'])->first();
        }
        else
        {
            $listing = Listing::find($attributes['listing_id']);
        }

		if (!empty($listing))
		{
            // We need to store the site id (reservation & customer)
            // Fetch site based on current portal
            $site = Site::where('name', config('appportal.name'))->first();
		    $this->site = $site? $site->id : null;

			// get rental offer
			$pickup_date_time = $attributes['pickup_date'] . ' ' . $attributes['pickup_time'];
			$dropoff_date_time = $attributes['dropoff_date'] . ' ' . $attributes['dropoff_time'];
			$date_range = new DateRange($pickup_date_time, $dropoff_date_time);
			$discount = new StaticDiscount($date_range->getTotalDays(), $date_range->getDaysIndex(), $listing);
			// check whether we have coupon discount
			if ( ! empty($attributes['discount_coupon']) )
			{
				// instantiate new discount for coupon
				$discount = new CouponDiscount($discount, $attributes['discount_coupon']);

				// ... and log the use of the coupon
				$coupon = Coupon::where('text', '=', $attributes['discount_coupon'])->first();
				if ( ! empty($coupon) && $coupon->valid == true )
				{
					$coupon->usage = $coupon->usage + 1;
					$coupon->save();
				}
			}

			// get accessories if present
			$accessories = [];
			if (!empty($attributes['accessory']))
			{
				$accessories = $attributes['accessory'];
			}

			$fuel_plan_charge_submitted = $attributes['fuel_plan_value'];

			$offer = $listing->getOffer($date_range, $discount, $attributes['pickup_location'], $attributes['dropoff_location'], $accessories, $fuel_plan_charge_submitted);

			// get listing group
			$group = config('appportal.name') == 'eurodollar' ? Group::find($listing->group_id) : Group::find($attributes['group']);

			// ...listing attributes needed for reservation
			$this->listing_id           = $listing->id;
			$this->listing_group        = $group->name;
			$this->listing_manufacturer = $listing->manufacturer;
			$this->listing_model        = $listing->model;
			$this->listing_transmission = $listing->transmission;
			$this->listing_fuel         = $listing->fuel;

			// ...total price and total discount price
			$this->total_price_initial      = $offer->getRangePrice();
			$this->extra_miles_charge_price = $offer->getExtraMilesCharge();
			$this->after_hours_charge_price = $offer->getAfterHoursCharge();
			$this->fuel_plan_charge_price   = $offer->getFuelPlanCharge();
			$this->total_discount_amount    = $offer->getDiscountAmount();
			$this->final_price              = $offer->getTotalDiscountPrice();

			// ...total days
			$this->total_days = $date_range->getTotalDays();

			// reservations with total_days less than 3 aren't allowed (it used to be 4)
			if ($this->total_days < 3)
			{
				throw new ValidationException(['Minimum reservation duration is 3 days']);
			}
			// If dropoff date is not 3 days after pickup throw exception
			if (Carbon::createFromFormat('d-m-Y', $attributes['pickup_date'])->addDays(3)->timestamp > Carbon::createFromFormat('d-m-Y', $attributes['dropoff_date'])->timestamp)
			{
				throw new ValidationException(['Please verify the pickup - dropoff dates']);
			}

			// ...extra day charge
			$this->extra_day_charge = $offer->hasExtraDayCharge();

			// ..extra miles charge
			$this->extra_miles_charge = $offer->hasExtraMilesCharge();

			// ..after hours charge
			$this->after_hours_charge = $offer->hasAfterHoursCharge();

			// ..fuel plan charge
			$this->fuel_plan_charge = $offer->hasFuelPlanCharge();

			// ..remote location charge (triggered fer either pickup or dropoff)
            $this->remote_location_charge = $offer->hasRemoteLocationPickupCharge() || $offer->hasRemoteLocationDropoffCharge();
            $this->remote_location_charge_price = $offer->getRemoteLocationCharge();

		}

        // we saves here the customer in order to get an id to use for the customer_id field
        $customer = $this->handleCustomer($attributes);
        $this->customer_id = $customer->id;

		$this->language_id = DB::table('languages')->where('locale', App::getLocale())->value('id');

		// we saves here the reservation in order to get an id to use for the reservation_to_accessory table
		$this->save();

        // after saving the reservation, we calculate a hash
        // in order to be used for the feedback page slug
        $this->feedback_hash = md5( rand(0, 1000) );

        // and create the uuid too
        $this->uuid = (string) Str::orderedUuid();

        // and save again
        $this->save();

		// then grab accessories and create appropriate DB rows
		if (!empty($accessories))
		{
			foreach($accessories as $id => $occurences)
			{
				if ($occurences > 0)
				{
					// load the corresponding accessory
					$accessory = Accessory::findOrFail($id);

					// prepare the data to insert to the reservation_to_accessory row
					$rta_attributes = [
					'reservation_id' => $this->id,
					'accessory_id' => $id
					];

					$reservationToAccessory = ReservationToAccessory::create($rta_attributes);

					$reservationToAccessory->accessory_name = $accessory->name;
					$reservationToAccessory->accessory_price = $accessory->price;
					$reservationToAccessory->accessory_amount = $occurences;

					$reservationToAccessory->save();
				}
			}
		}

		return $this;
	}

	protected function performCustomUpdateTasks(array $attributes = [])
	{
		return $this;
	}


	/**
	 * Check if a reservation should receive a reminder
	 */
	public function shouldSendReminder($reminderPeriod = 7)
	{
		// If the pickup date is within a specific period (in the next 7 days)
		// and a reminder has not been sent during this period
		$pickupDate = Carbon::createFromTimestamp(strtotime($this->pickup_date->format('Y-m-d') . " " . $this->pickup_time));
		$remindedDate = Carbon::createFromTimestamp(strtotime($this->reminded_at));

		return Carbon::now()->addDays($reminderPeriod)->gte($pickupDate)
            && $pickupDate->subDays(7)->gte($remindedDate)
            && $this->show === 'show';
	}

    private function handleCustomer(array $attributes)
    {
        $customer = Customer::updateOrCreate(
            [
                'name'          => $attributes['customer_name'],
                'email'         => $attributes['customer_email'],
                'address'       => $attributes['customer_address'],
                'country_id'    => $attributes['customer_country_id'],
                'telephone'     => $attributes['customer_telephone'],
                'site'          => $this->site,
            ],
            [
                'updated_at'    => Carbon::now(),
            ]
        );

        return $customer;
    }

    /**
     * Get aggregated data from reservation for specific column
     *
     * @param string $aggregateBy
     * @param string|null $analytics_year
     * @return mixed
     */
    public static function getReservationShowDataAggregatedBy(string $aggregateBy, string $analytics_year = null, string $analytics_month = null, array $extra = []): mixed
    {
        $start_month        = 1;
        $end_month          = 12;
        $calculate          = 'count(*) as count';
        $pluck              = 'count';
        if(!empty($analytics_year) && !empty($analytics_month))
        {
            $start_month    = $analytics_month;
            $end_month      = $analytics_month;
        }
        if(isset($extra['sum']))
        {
            $calculate      = 'sum(' . $extra['sum'] . ') as sum';
            $pluck          = 'sum';
        }
        // Base query
        $data = self::select($aggregateBy)
            ->selectRaw($calculate)
            ->where('show', 'show')
            ->groupBy($aggregateBy);
        // Add year constraint
        if(!empty($analytics_year))
        {
            $start_date = Carbon::createFromFormat('Y-m', $analytics_year . '-' . $start_month);
            $end_date   = Carbon::createFromFormat('Y-m', $analytics_year . '-' . $end_month);
            $data->whereBetween('pickup_date', [$start_date->firstOfMonth(), $end_date->lastOfMonth()->addDay()]);
        }
        // Return data
        return $data
            ->get()
            ->pluck($pluck, $aggregateBy);
    }

    /**
     * Get aggregated data from reservation for specific column
     * @param string $aggregateBy
     * @param string|null $analytics_year
     * @return mixed
     */
    public static function getReservationShowDataMonthlyAggregatedBy(string $aggregateBy, string $analytics_year = null, string $analytics_month = null, array $extra = []): mixed
    {
        $start_month        = 1;
        $end_month          = 12;
        $select_format      = '%M';
        $groupBy_format     = '%M';
        $orderBy_format     = '%m';
        $calculate          = 'count(*) as count';
        $pluck              = 'count';
        if(!empty($analytics_year) && !empty($analytics_month))
        {
            $start_month    = $analytics_month;
            $end_month      = $analytics_month;
            $select_format  = '%e';
            $groupBy_format = '%d';
            $orderBy_format = '%d';
        }
        if(isset($extra['sum']))
        {
            $calculate      = 'sum(' . $extra['sum'] . ') as sum';
            $pluck          = 'sum';
        }
        // Base query
        $data = self::selectRaw("DATE_FORMAT({$aggregateBy}, '" . $select_format . "') as date, {$calculate}")
            ->where('show', 'show')
            ->groupBy(DB::raw("DATE_FORMAT({$aggregateBy}, '" . $groupBy_format . "')"))
            ->orderBy(DB::raw("DATE_FORMAT({$aggregateBy}, '" . $orderBy_format . "')"));
        // Add year constraint
        if(!empty($analytics_year))
        {
            $start_date = Carbon::createFromFormat('Y-m', $analytics_year . '-' . $start_month);
            $end_date   = Carbon::createFromFormat('Y-m', $analytics_year . '-' . $end_month);
            $data->whereBetween($aggregateBy, [$start_date->firstOfMonth(), $end_date->lastOfMonth()->addDay()]);
        }
        // Return data
        return $data
            ->get()
            ->pluck($pluck, 'date');
    }
}
