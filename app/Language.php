<?php namespace App;

use App\Traits\Relationships\LanguageRelationships;
use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class Language extends Model implements TranslatableContract {
	
	use Translatable,
        LanguageRelationships;

    public $timestamps = false;

    protected $fillable = [
        'locale',
        'language',
        'enabled',
    ];

    public $translatedAttributes = [
        'text',
    ];

}