<?php

namespace App\Traits\AccessorsMutators;

use App\Location;
use App\ReservationToAccessory;
use DateTime;

trait PickupDropoffLocationAccessorsMutators
{

    /**
     * Get the name of the pickup location
     *
     * @return mixed
     */
    public function getPickupLocation()
    {
        return Location::find($this->pickup_location)->name;
    }

    /**
     * Get the name and description of the pickup location, used for email
     *
     * @return mixed
     */
    public function getPickupLocationDescription()
    {
        return Location::find($this->pickup_location)->pickup_description;
    }

    /**
     * Get the name of the pickup location images
     *
     * @return mixed
     */
    public function getPickupLocationImages()
    {
        return Location::find($this->pickup_location)->images;
    }

    /**
     * Get the name of the dropoff location
     *
     * @return mixed
     */
    public function getDropoffLocation()
    {
        return Location::find($this->dropoff_location)->name;
    }

    /**
     * Get the name and description of the dropoff location, used for email
     *
     * @return mixed
     */
    public function getDropoffLocationDescription()
    {
        return Location::find($this->dropoff_location)->dropoff_description;
    }

    /**
     * Get the name of the dropoff location images
     *
     * @return mixed
     */
    public function getDropoffLocationImages()
    {
        return Location::find($this->dropoff_location)->images;
    }

}
