<?php

namespace App\Traits\AccessorsMutators;

use App\Location;
use DateTime;

trait QuoteAccessorsMutators
{
    /**
     * Get the name of the pickup location
     * Returns string or "-" if not given
     *
     * @return mixed
     */
    public function getPickupLocation()
    {
        return empty($this->pickup_location) ? '-' : Location::find($this->pickup_location)->name;
    }

    /**
     * Get the name of the dropoff location
     * Returns string or "-" if not given
     *
     * @return mixed
     */
    public function getDropoffLocation()
    {
        return empty($this->dropoff_location) ? '-' : Location::find($this->dropoff_location)->name;
    }

    /**
     * Includes the custom model attributes to the list of the attributes that are automatically mutated as dates
     *
     * @return mixed
     */
    public function getDates()
    {
        // get the dates as defined by the parent method
        $dates = parent::getDates();
        // inject the custom reservation attributes that need to be handled as dates
        array_push($dates, 'pickup_date', 'dropoff_date');

        return $dates;
    }

    /**
     * Stores the attribute in the correct format in the DB (Y-m-d)
     * @param $value
     */
    public function setPickupDateAttribute($value)
    {
        $dt = new DateTime($value);
        $this->attributes['pickup_date'] = $dt->format('Y-m-d');
    }

    /**
     * Stores the attribute in the correct format in the DB (Y-m-d)
     * @param $value
     */
    public function setDropoffDateAttribute($value)
    {
        $dt = new DateTime($value);
        $this->attributes['dropoff_date'] = $dt->format('Y-m-d');
    }

    /**
     * Get the model/manufacturer/transmission combo in one go
     *
     * @return string
     */
    public function getTitleAttribute()
    {
        return $this->site == 1 ?
            $this->listing_manufacturer . ' ' . $this->listing_model . ' (' . $this->listing_transmission . ')' :
            'GROUP '.$this->listing_group;
    }

}