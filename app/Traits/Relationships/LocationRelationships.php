<?php

namespace App\Traits\Relationships;

use App\GoogleReview;

trait LocationRelationships
{

    public function getImageAttribute($image)
    {
        if($this->images->count() === 0){
            return $image;
        }
        return $this->images->first()->getPath(false);
    }

    public function images()
    {
        return $this->belongsToMany('App\Image')->orderBy('image_location.id', 'asc');
    }

    /**
     * Get the google reviews for the location.
     */
    public function googleReviews()
    {
        return $this->hasMany(GoogleReview::class);
    }
}