<?php

namespace App\Traits\Relationships;

use App\Group;
use App\Listing;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

trait SupergroupRelationships
{
    /**
     * Get the groups for the supergroup.
     */
    public function groups(): HasMany
    {
        return $this->hasMany(Group::class);
    }

    /**
     * Get all of the listings for the supergroup.
     */
    public function listings(): HasManyThrough
    {
        return $this->hasManyThrough(Listing::class, Group::class);
    }

}
