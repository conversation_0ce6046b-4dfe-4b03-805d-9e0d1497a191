<?php

namespace App\Traits\Relationships;

use App\LogPricing;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait PeriodRelationships
{

    /**
     * Get all groups for the period
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function groups()
    {
        return $this->belongsToMany('App\Group', 'group_period', 'period_id', 'group_id')->withPivot('price')->withTimestamps();
    }

    /**
     * Get the changes for the period.
     */
    public function changes(): HasMany
    {
        return $this->hasMany(LogPricing::class, 'period_id');
    }

    /**
     * Get the changes for the period for a specific group.
     */
    public function changesGroup($group_id): HasMany
    {
        return $this->changes()
            ->where('group_id', $group_id);
    }

    /**
     * Get the changes for the period for a specific group for a given year.
     */
    public function changesGroupYear($group_id, $year): HasMany
    {
        return $this->changesGroup($group_id)
            ->whereYear('created_at', '=', $year);
    }

}
