<?php namespace App\Repositories\Contracts;

interface ListingRepositoryInterface extends RepositoryInterface{
    
    /**
     * @param $slug
     * @return mixed
     */
    public function findBySlug($slug);

    /**
     * @param $id
     * @param $group_id
     * @param $size
     * @return mixed
     */
    public function findListings($attribute, $value, $fields);

    /**
     * @param $id
     * @param $group_id
     * @param $size
     * @return mixed
     */
    public function getRelated($id, $group_id, $size = 4);

    /**
     * @param $id
     * @param $group_id
     * @param $size
     * @return mixed
     */
    public function getSuggested($id, $group_id, $size = 4);
    
    /**
     * @param $groups
     * @return mixed
     */
    public function findByGroups($groups, $site = false);
    
    /**
     * @param $where
     * @param $category
     * @return mixed
     */
    public function getListings($where = array(), $category = false);
}