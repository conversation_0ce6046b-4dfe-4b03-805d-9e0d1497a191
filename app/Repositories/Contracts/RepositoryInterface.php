<?php 
namespace App\Repositories\Contracts;

use Illuminate\Database\Query\Builder;

interface RepositoryInterface {
    /**
     * @param array $columns
     * @return mixed
     */
    public function all($columns = array('*'));
    
    /**
     * @param  string $value
     * @param  string $key
     * @return array
     */
    public function lists($value, $key = null);
    
    /**
     * @param $perPage
     * @param array $columns
     * @return mixed
     */
    public function paginate($perPage = 1, $columns = array('*'));

    /**
     * @param array $data
     * @return mixed
     */
    public function create(array $data);

    /**
     * @param array $data
     * @param $id
     * @return mixed
     */
    public function update(array $data, $id);

    /**
     * @param $id
     * @return mixed
     */
    public function delete($id);

    /**
     * @param $id
     * @param array $columns
     * @return mixed
     */
    public function find($id, $columns = array('*'));

    /**
     * @param $field
     * @param $value
     * @param array $columns
     * @return mixed
     */
    public function findBy($field, $value, $columns = array('*'));

    /**
     * @param array $fields
     * @return mixed
     */
    public function orderBy($fields);
    
    /**
     * @param $id
     * @return mixed
     */
    public function findOrFail($id);
    
    /**
     * @param $column
     * @param null $operator
     * @param null $value
     * @param string $boolean
     * @return Builder
     */
    public function where($column, $operator = null, $value = null, $boolean = 'and');
}
