<?php 

namespace App\Repositories;

use App\Group;
use App\Listing;
use App\Repositories\Contracts\ListingRepositoryInterface;
use App\Repositories\Eloquent\Repository;


class ListingRepository extends Repository implements ListingRepositoryInterface {

    public function __construct(Listing $model)
    {
        $this->model = $model;
    }
    
    /**
     * @param $slug
     * @return mixed
     */
    public function findBySlug($slug) {
        return $this->model->where('slug', '=', $slug)->first();
    }

    /**
     * Sort Listings based on the group_order column of the groups table
     *
     * @param $attribute
     * @param $value
     * @param array $fields
     * @return mixed
     */
    public function findListings($attribute, $value, $fields)
    {
    	$sorting = array("`groups`.`group_order` ASC");
        foreach($fields as $field => $direction){
            $sorting[] = "`{$field}` {$direction}";
        }
        
        return $this->model->select('listings.*')->leftJoin('groups', 'listings.group_id', '=', 'groups.id')->where($attribute, '=', $value)->orderByRaw(implode(',', $sorting))->get();
    }
    
    /**
     * @param $id
     * @param $group_id
     * @param $size
     * @return mixed
     */
    public function getRelated($id, $group_id, $size = 6)
    {

        return $this->model->where('group_id', '=', $group_id)
                            ->where('id', '!=', $id)
                            ->where('published_'.config('appportal.name'), '=', '1')
                            ->take($size)->get();
    }
    
    /**
     * @param $id
     * @param $group_id
     * @param $size
     * @return mixed
     */
    public function getSuggested($id, $group_id, $size = 6)
    {
    	$groups = Group::find($group_id)->relatedGroups()->get()->toArray();
    	// PHP >= 5.5
//     	$suggestedGroups = array_column($groups, 'id');
    	
    	// PHP < 5.5
    	$suggestedGroups = array_map(function($element){
					    		return $element['id'];
					    	}, $groups);
    	
        return $this->model->whereIn('group_id',$suggestedGroups)
                            ->where('id', '!=', $id)
                            ->where('published_'.config('appportal.name'), '=', '1')
                            ->take($size)->get();
    }
    
    /**
     * @param $groups
     * @return mixed
     */
    public function findByGroups($groups, $site = false) {

        $query = ($site) ?
            $this->model->whereIn('group_id', $groups)
                ->where('published_'.$site, '1')
                ->orderBy(Group::select('group_order')->whereColumn('groups.id', 'listings.group_id'), 'asc')
            :
            $this->model->whereIn('group_id', $groups)
                ->orderBy('order', 'ASC');
        return $query->get();
    }

    /**
     * @param $groups
     * @return mixed
     */
    public function findByTransmission($transmission, $site = false)
    {
        // failsafe validation
        if($transmission != 'automatic' && $transmission != 'manual')
        {
            $transmission = 'manual';
        }

        $query = ($site) ?
            $this->model->where('transmission', $transmission)
                ->where('published_'.$site, '1')
                ->orderBy(Group::select('group_order')->whereColumn('groups.id', 'listings.group_id'), 'asc')
        :
            $this->model->where('transmission', $transmission)
                ->orderBy('order', 'ASC');

        return $query->get();
    }

    /**
     * @param $groups
     * @return mixed
     */
    public function findByFuel($fuel, $site = false)
    {
        // failsafe validation
        if($fuel != 'diesel' && $fuel != 'petrol')
        {
            $fuel = 'petrol';
        }

        $query = ($site) ?
            $this->model->where('fuel', $fuel)
            ->where('published_'.$site, '1')
            ->orderBy(Group::select('group_order')->whereColumn('groups.id', 'listings.group_id'), 'asc')
        :
            $this->model->where('fuel', $fuel)
            ->orderBy('order', 'ASC');

        return $query->get();
    }

    /**
     * @param $groups
     * @return mixed
     */
    public function findBySeats($seats, $site = false)
    {
        // failsafe validation
        if($seats != '0_5' && $seats != '5_9')
        {
            $seats = '0_5';
        }

        $seats_limits = explode ('_', $seats);

        $query = ($site) ?
            $this->model->where('seats', '>', $seats_limits[0])
                ->where('seats', '<=', $seats_limits[1])
                ->where('published_'.$site, '1')
                ->orderBy(Group::select('group_order')->whereColumn('groups.id', 'listings.group_id'), 'asc')
        :
            $this->model->where('seats', '>', $seats_limits[0])
                ->where('seats', '<=', $seats_limits[1])
                ->orderBy('order', 'ASC');

        return $query->get();
    }
    
    /**
     * @param $where
     * @param $category
     * @return mixed
     */
    public function getListings($where = array(), $category = false)
    {
        $where = implode(' AND ', $where);
        if(!empty($where) && empty($category))
        {
	        return $this->model->whereRaw($where)->orderBy('id', 'DESC')->get();
        }
        elseif(!empty($where) && !empty($category))
        {
        	return $this->model->whereHas('categories', function($q) use ($category)
        	{
        		$q->where('categories.id', '=', $category);
        	
        	})->whereRaw($where)->orderBy('id', 'DESC')->get();
        }
        else
        {
        	return $this->model->whereHas('categories', function($q) use ($category)
        	{
        		$q->where('categories.id', '=', $category);
        	
        	})->orderBy('id', 'DESC')->get();
        }
    }
}