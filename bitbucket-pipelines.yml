image: rastasheep/ubuntu-sshd
pipelines:
  branches:
    develop:
      - step:
          name: Staging - Build & Deploy
          script:
            # just execute the deployment script on remote server
            - echo $remote_user_pass | ssh -tt foufos@************* 'sudo ./deploy_lara_rentals'
    master:
      - step:
          name: Production - Build & Deploy
          script:
            # just execute the deployment script on remote server
            - echo $remote_user_pass | ssh -tt foufos@*************** 'sudo ./deploy_lara_rentals'