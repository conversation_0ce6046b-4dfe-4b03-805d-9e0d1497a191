<?php namespace Modules\CretanRentals\Http\Controllers;

use Nwidart\Modules\Routing\Controller;
use Illuminate\Support\Facades\App;
use App\Language;
use App\Group;

class CretanRentalsBaseController extends Controller {

    protected $view_data = [];

    public function __construct()
    {
        // get the enabled langs for the menu dropdown
        $enabledLanguages = Language::whereIn('locale', config('cretanrentals.enabled_languages'))->get();
        $this->view_data['enabledLanguages'] = [];
        foreach ($enabledLanguages as $language)
        {
            if($language->locale == App::getLocale()){
                $this->view_data['enabledLanguages']['current'] = $language;
            }
            else{
                $this->view_data['enabledLanguages']['other'][] = $language;
            }
        }
        //
        $this->view_data['groups_menu_seo'] = Group::orderByRaw('group_order ASC, id ASC')->get()->pluck('fullNameSEOCR', 'id');

        // initialize url_variables to empty
        $this->view_data['url_variables'] = '';
    }

}