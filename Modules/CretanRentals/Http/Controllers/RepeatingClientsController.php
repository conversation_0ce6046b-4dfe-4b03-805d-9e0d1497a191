<?php namespace Modules\CretanRentals\Http\Controllers;

use Illuminate\Http\Request;
use <PERSON>widart\Modules\Routing\Controller;
use App\Listing;
use App\Services\Validation\ValidationException;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Response;
use App\RepeatingClient;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\App;

class RepeatingClientsController extends Controller {

    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store(Request $request)
    {

        try
        {
            // listing_id is required but in cretan we have group_id, fetch the first listing of this group
            $group_id = $request->get('group');
            if(!$group_id){
                return Response::json(['status' => 'error', 'msg' => 'group is required']);
            }
            $listing = Listing::where('group_id',$group_id)->first(); // Any of the group should do
            $request->merge(['listing_id' => $listing->id]);

            $repeatingClient = RepeatingClient::create($request->all());


           if (!empty($repeatingClient))
           {
               $listing = Listing::find($repeatingClient->listing_id);
               // Change local to en
               $currentLocale = App::getLocale();
               Lang::setLocale('en');
               // ...and to admin
               Mail::send('cretanrentals::emails.repeatingClient_admin', ['repeatingClient' => $repeatingClient, 'listing' => $listing], function($message) use ($repeatingClient)
               {
               		$message
               			->to(config('mail_addresses.info_cr'), 'CretanRentals Admins')
                        ->cc([config('mail_addresses.argiry'), config('mail_addresses.kiritsis'), '<EMAIL>'])
               			->subject('New Repeating Client #' . $repeatingClient->id);
               });

               // Reset locale
               Lang::setLocale($currentLocale);
           }

            return Response::json(['status' => 'success', 'msg' => Lang::get('forms.repeating_customer_success'), 'repeatingClientId' => $repeatingClient->id]);
        }
        catch(ValidationException $e)
        {
            return Response::json(['status' => 'error', 'msg' => $e->getErrors()]);
        }
    }

}
