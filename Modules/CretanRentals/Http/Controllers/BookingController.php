<?php namespace Modules\CretanRentals\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\Validator;
use Modules\CretanRentals\Util\CretanDefaultValues;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Mail;
use App\Group;
use App\Coupon;
use App\Location;
use App\Listing;
use App\Country;
use App\Accessory;
use App\Site;
use App\Reservation;
use Illuminate\Support\Facades\App;
use App\Services\Offer\DateRange;
Use Illuminate\Support\Facades\Request;
Use Illuminate\Support\Facades\Response;
use App\Services\Offer\StaticDiscount;
use App\Services\Offer\CouponDiscount;
use App\Services\Validation\ValidationException;
use Illuminate\Support\Arr;

class BookingController extends CretanRentalsBaseController {

    private $validStepsNormal  = [1, 2]; // non- ajax requests
    private $validStepsAjax    = [3,4,5];
    private $step;

    // reservation attributes
    private $pickup_location;
    private $pickup_date;
    private $pickup_time;
    private $dropoff_location;
    private $dropoff_date;
    private $dropoff_time;
    private $group;
    private $dateRange;

    private $accessories = array();
    private $fuel_plan = 0;
    private $discount_coupon;

    private $customer_name;
    private $customer_email;
    private $customer_address;
    private $customer_telephone;
    private $customer_country_id;
    private $comment = '';
    private $flight_details;

    private $default_data; //default dates, locations e.t.c

    public function __construct(\Illuminate\Http\Request $request) {

        parent::__construct();
        $this->view_data['booking_selected'] = 'active'; // for the menu item (all booking steps)

        //special handle for dropoff location (case same as pickup which comes with value 0)
        if(!$request->get('dropoff_location')){
            $request->merge(['dropoff_location' => $request->get('pickup_location')]);
        }

        // query params for links to step 1 & 2  ('change search options' link & 'change car group' links in steps>1)
        $urlparams_for_step_1 = array_merge(['step'=>1], Arr::only($request->all(), [
            'pickup_location',
            'pickup_date',
            'pickup_time',
            'dropoff_location',
            'dropoff_date',
            'dropoff_time',
            'group'
        ]) );

        $urlparams_for_step_2 = array_merge($urlparams_for_step_1, ['step'=>2]);
        $this->view_data['url_variables_to_step_1'] = '?'.http_build_query($urlparams_for_step_1);
        $this->view_data['url_variables_to_step_2'] = '?'.http_build_query($urlparams_for_step_2);

    }

    /**
     * All step requests
     */
	public function index(\Illuminate\Http\Request $request)
	{

	    // validate step: seperate validation for ajax & non ajax steps
        $this->step  = $request->get('step');
        if(Request::ajax())
        {
            // validate step for ajax requests
            if(!in_array($request->get('step'), $this->validStepsAjax)){
                // return to step 1 with any other url params
                $request->merge(['step' => 1]);
                return [
                    'status'   => false,
                    'redirect' => route('booking.index', Arr::except($request->all(), '_token'))
                ];
            }
        }
        else // validate non-ajax step
        {
            // Redirect to step 1 if step is invalid
            if(!in_array($this->step, $this->validStepsNormal))
            {
                $request->merge(['step' => 1]);
                return Redirect::route('booking.index', Arr::except($request->all(), '_token'));
            }

        }

        // step is good
        $this->step              = (int)$this->step;
        $this->view_data['step'] = $this->step;

        // process input data
        // (validate and set default values to any missing or invalid reservation params that we have in the url)
        $this->processUrlParams();
        // process post params sent with ajax
        $response = $this->processAjaxParams();
        if(!is_null($response)) {
            return $response; // a redirect to step 1 most likely
        }

        // prepare the data for the view
        $stepHandled = $this->handleStep($this->step);

        // return the response
        if(Request::ajax())
        {
            if(!$stepHandled){ // if for any reason there was an error back to step 1
                $request->merge(['step' => 1]);
                return [
                    'status'   => false,
                    'redirect' => route('booking.index', Arr::except($request->all(), '_token'))
                ];
            }
            // this will replace the current step view content
            $view     = View::make('cretanrentals::booking.step_'.$this->step,  $this->view_data)->render();
            return [
                'status'    => 1,
                'contents'  => $view,
                'step'      => $this->step,
                'target_id' => 'step-wrapper' , // that will be raplced with 'contents'
            ];
        }
        // response for non-ajax steps (1,2)
        $view = 'cretanrentals::booking.step_'.$this->step;
        return View::make($view, $this->view_data);
	}

	/*
	 * Process the input data (url params only: dates,locations,hours,group)
	 * Sets default values for any invalid param
	 * Assigns values to all attributes even if missing from request
	 * Sets some other data needed in various steps (e.g. daterange)
	 */
	private function processUrlParams()
    {
        // Get the request
        $request = request();
        //get the default data
        $getDropdowns = $this->step === 1 ? true : false; //if in step 1 we need the dropdowns for the search form
        $this->default_data = CretanDefaultValues::getSearchBucketDefaultValues($getDropdowns);

        // we set all as required to set any absent fields to its default values
        $validator = \Validator::make($request->all(), [
            'pickup_location'  => 'required|numeric|exists:locations,id',
            'dropoff_location' => 'required|numeric|exists:locations,id',
            'pickup_date'      => 'required|date_format:d/m/Y|after:'.Carbon::now()->addDays(1)->format('d/m/Y'),
            'dropoff_date'     => 'required|date_format:d/m/Y|after:'.Carbon::now()->addDays(4)->format('d/m/Y'),
            'pickup_time'      => 'required|date_format:H:i',
            'dropoff_time'     => 'required|date_format:H:i',
            'group'            => 'required|numeric|exists:groups,id'
        ]);

        // init class attrributes to input's values and change according to the validator's results
        $this->pickup_location  = $request->get('pickup_location');
        $this->pickup_date      = $request->get('pickup_date');
        $this->pickup_time      = $request->get('pickup_time');
        $this->dropoff_location = $request->get('dropoff_location') == 0 ? $request->get('pickup_location') : $request->get('dropoff_location');
        $this->dropoff_date     = $request->get('dropoff_date');
        $this->dropoff_time     = $request->get('dropoff_time');
        $this->group            = $request->get('group');

        // and set the default value for each invalid attrribute (*group's default value is '')
        if ($validator->fails())
        {
            foreach($validator->messages()->keys() as $attribute)
            {
                if(array_key_exists($attribute, $this->default_data)){
                    $this->{$attribute} = $this->default_data[$attribute];
                }
            }
        }

        // set the daterange also (used in all steps except first to get the offer)
        $this->dateRange = new DateRange(str_replace('/', '-', $this->pickup_date) . ' ' . $this->pickup_time,
            str_replace('/', '-', $this->dropoff_date) . ' ' . $this->dropoff_time);
    }

    // accessories, personal info & other data that are set in ajax steps. The
    private function processAjaxParams()
    {
        // Get the request
        $request = request();

        $this->accessories              = $request->get('accessory', []); // selected by the user if any , else empty array
        $this->view_data['accessories'] = Accessory::all()->toArray(); // all from db
        $this->fuel_plan                = $request->get('fuel_plan_value') === 1 || $request->get('fuel_plan_value') === '1' ? 1 : 0;
        $this->discount_coupon          = $request->get('discount_coupon'); //code or null

        //if request is for final step 5 (review and book), validate required personal details data
        if($this->step == 5)
        {
            $validator = \Validator::make($request->all(), [
                'customer_name'       => 'required',
                'customer_email'      => 'required|email',
                'customer_address'    => 'required',
                'customer_country_id' => 'required|numeric',
                'customer_telephone'  => 'required',
            ]);

            if ($validator->fails())
            {
                return [
                    'status'   => false,
                    'message_popup'  => [
                        'title' => trans('cretanrentals::booking.warning_title'),
                        'text'  => trans('cretanrentals::booking.warning_message'),
                    ],
                    'errors'   => $validator->messages()
                ];
            }
        }
        // set class attributes (we want them validated only for 4 -> 5 step sequence)
        $this->customer_name       = $request->get('customer_name');
        $this->customer_email      = $request->get('customer_email');
        $this->customer_address    = $request->get('customer_address');
        $this->customer_telephone  = $request->get('customer_telephone');
        $this->customer_country_id = $request->get('customer_country_id');
        $this->comment             = $request->get('comment');
        $this->flight_details      = $request->get('flight_details');
    }

    /**
     * @param $step
     * Sets the view data for each step
     */
	private function handleStep($step)
    {
        switch($step)
        {
            // step 1
            case 1:
                //we only need the search form data
                $this->view_data['searchBucket'] = [
                    'pickup_location'  => $this->pickup_location,
                    'pickup_date'      => $this->pickup_date,
                    'pickup_time'      => $this->pickup_time,
                    'dropoff_location' => $this->dropoff_location,
                    'dropoff_date'     => $this->dropoff_date,
                    'dropoff_time'     => $this->dropoff_time,
                    'group'            => $this->group,
                    'locations_dropdown_pickup'  => $this->default_data['locations_dropdown_pickup'],
                    'locations_dropdown_dropoff' => $this->default_data['locations_dropdown_dropoff'],
                    'group_list'                 => $this->default_data['group_list'],
                    'hours_dropdown'             => $this->default_data['hours_dropdown'],
                ];
                $this->view_data['canonical_url'] = route('booking.index');
                break;
            //step 2  (group selection)
            case 2:
                $this->selectedGroup = $this->group; // group or null

                // view data for the reservation details widget
                $this->view_data['reservation_details'] = $this->getReservationDetails();

                // get the groups that we'll show as results in the list
                // (for now all groups todo decide for unavailable groups)
                $groups = Group::orderByRaw('group_order ASC, id ASC')->get();

                $availableCounter = 0;
                foreach ($groups as $group)
                {
                    if($groupData = $this->getGroupData($group)){
                        $this->view_data['group_items'][$group->id] = $groupData;
                        if($groupData['isAvailable']){
                            $availableCounter++;
                        }
                    }
                }
                // a text to show above the results
                $this->view_data['results_title'] = sprintf(trans('cretanrentals::booking.results_title'), $availableCounter);

                // if there is a selected group, move it in the first place of the array
                if(array_key_exists($this->selectedGroup, $this->view_data['group_items']))
                {
                    $this->view_data['group_items'] = [$this->selectedGroup => $this->view_data['group_items'][$this->selectedGroup]] + $this->view_data['group_items'];
                }

                $this->view_data['formToStep3'] = $this->getHiddenForm();

                //rental policy modal
                $this->view_data['policy_modal'] = view('cretanrentals::booking._policy_modal', [
                    'insuranceItems'      => \Lang::get('cretanrentals::policy.insurance.items'),
                    'policyItems'         => \Lang::get('cretanrentals::policy.policy.items'),
                    'accidentReportItems' => \Lang::get('cretanrentals::policy.accidentReportItems')
                ])->render();

                $this->view_data['canonical_url'] = route('booking.index',['step' => 2]);

                break;
            // === AJAX steps === //
            case 3: //accessories & extras & payment details
                $group = Group::find($this->group);
                if(!$group){
                    return false; //caller method will redirect to step 1
                }

                $this->view_data['group'] = $group;

                // view data for the reservation details widget
                $this->view_data['reservation_details'] = $this->getReservationDetails();

                //get the offer as we display and finalize price in this step
                $this->view_data['offer'] = $this->getGroupOffer($group->id);
                // availability should be fine but check just to make sure
                if(!$this->view_data['offer']->hasAvailability()){
                    return false; //caller method will redirect to step 1
                }

                // add count info, if selected
                foreach ($this->view_data['accessories'] as &$accessory)
                {
                        $isSelected = array_key_exists($accessory['id'], $this->accessories)
                                      && $this->accessories[$accessory['id']] > 0; // if accessory count >0
                        $accessory['picked']    = $isSelected ? 'picked' : '';
                        $accessory['count']       = $isSelected ? $this->accessories[$accessory['id']] : 0;
                        $accessory['total_price'] = $isSelected ? $accessory['price']*$this->dateRange->getTotalDays()*$accessory['count'] : 0;
                }
                $this->view_data['selected_accessories'] = $this->accessories;
                $this->view_data['fuel_plan'] = $this->fuel_plan;
                $this->view_data['discount_coupon'] = $this->discount_coupon;
                // hidden form for step 4
                $this->view_data['formToStep4'] = $this->getHiddenForm();
                break;
            case 4: // personal details step
                $group = Group::find($this->group);
                if(!$group){
                    return false; //caller method will redirect to step 1
                }
                $this->view_data['group'] = $group;
                $this->view_data['offer'] = $this->getGroupOffer($group->id);
                // view data for the reservation details widget
                $this->view_data['reservation_details'] = $this->getReservationDetails();
                // get all countries
                $this->view_data['countries'] = Country::all()->pluck('name', 'id')->toArray();
                asort($this->view_data['countries']);
                $this->view_data['countries'] = array('' => trans('cretanrentals::forms.select_country')) + $this->view_data['countries'];
                //hidden inputs with all reservation data until now from previous steps
                $this->view_data['hiddenInputs'] = $this->getHiddenForm(true);
                // data to prefill (visible) the perosnal info fields of the form
                $this->view_data['personal_info'] = [
                    'customer_name'           => $this->customer_name,
                    'customer_email'          => $this->customer_email,
                    'customer_address'        => $this->customer_address,
                    'customer_telephone'      => $this->customer_telephone,
                    'customer_country_id'     => $this->customer_country_id,
                    'comment'                 => $this->comment,
                    'flight_details'          => $this->flight_details,
                ];
                break;
            case 5:
                $group = Group::find($this->group);
                if(!$group){
                    return false; //caller method will redirect to step 1
                }

                $this->view_data['group'] = $group;
                $this->view_data['offer'] = $this->getGroupOffer($group->id);

                $this->view_data['reservation_details'] = $this->getReservationDetails();
                //hidden inputs with all reservation data until now from previous steps
                $this->view_data['hiddenInputs'] = $this->getHiddenForm();
                break;
        }
        return true;
    }

    /**
     * For the reservation widget (_reservationDetailsWidget partial view)
     * @return array
     */
    private function getReservationDetails()
    {
        $pickupDateObj   = Carbon::createFromFormat('d/m/Y', $this->pickup_date);
        $dropoffDateObj  = Carbon::createFromFormat('d/m/Y', $this->dropoff_date);
        $pickupLocation  = Location::find($this->pickup_location);
        $dropoffLocation = Location::find($this->dropoff_location);

        if(empty($dropoffLocation)) { $dropoffLocation = $pickupLocation; }
        // we need special handling for greek months strings
        if(App::getLocale() == 'el'){
            $months = CretanDefaultValues::getMonthsEl();
        }
        $reservationDetails = [
            'pdate'     =>  $pickupDateObj->day.' '. (isset($months) ? $months['genitiveCase'][$pickupDateObj->month] : $pickupDateObj->format('F')).' '.$pickupDateObj->year,
            'ddate'     =>  $dropoffDateObj->day.' '. (isset($months) ? $months['genitiveCase'][$dropoffDateObj->month] : $dropoffDateObj->format('F')).' '.$dropoffDateObj->year,
            'ptime'     => sprintf(trans('cretanrentals::booking.reservation_detail.time'), strtolower(date('H:i ', strtotime($this->pickup_time)))),
            'dtime'     => sprintf(trans('cretanrentals::booking.reservation_detail.time'), strtolower(date('H:i ', strtotime($this->dropoff_time)))),
            'plocation' => sprintf(trans('cretanrentals::booking.reservation_detail.pickup_location_prefix'), $pickupLocation->name ),
            'dlocation' => sprintf(trans('cretanrentals::booking.reservation_detail.dropoff_location_prefix'), $dropoffLocation->name ),
        ];

        // accessories
        if(!empty($this->accessories && $this->step > 3)){
            foreach ($this->accessories as $id => $count)
            {
                if($count>0)
                {
                    $accessory = Accessory::find($id);
                    if($accessory)
                    {
                        $reservationDetails['accessories'][] = [
                            'name' => $accessory->name,
                            'count' => $count,
                            'price' => $accessory->price
                        ];
                    }
                }

            }
        }
        //fuel plan
        if($this->fuel_plan && $this->step > 3)
        {
            $reservationDetails['fuel_plan'] = $this->fuel_plan;
        }

        //discount coupon
        if($this->discount_coupon && $this->step > 4){
            $site   = Site::where('name', '=', config('appportal.name'))->first();
            $coupon = Coupon::where('text', '=', $this->discount_coupon)->where('site', '=', $site->id)->first();
            if($coupon){
                $reservationDetails['discount_coupon'] = $this->discount_coupon;
            }
        }

        //personal info
        if($this->step === 5){
            $country = Country::find($this->customer_country_id);
            $reservationDetails['personal_info'] = [
                'customer_name'           => $this->customer_name,
                'customer_email'          => $this->customer_email,
                'customer_address'        => $this->customer_address,
                'customer_telephone'      => $this->customer_telephone,
                'customer_country'        => $country ? $country->name : '',
                'comment'                 => $this->comment,
                'flight_details'          => $this->flight_details,
            ];
        }

        return $reservationDetails;
    }

    /**
     * @param $group
     * @return array|bool
     * Prepares the data of a group for the view (step 2 group list item, _groupListItem partial view)
     */
    private function getGroupData($group)
    {
        $title      = $group->getGroupCarsTitle(); // All group's cars manuf/rer & model in a string
        if($title == '')
        {
            return false; // group has no listings
        }

        // get the offer for this group, for the selected dates (we need total & perDay price for each group)
        $offer = $this->getGroupOffer($group->id);

        $pricePerDay = str_replace(",00", "", (string)number_format ($offer->getBasePrice()/$this->dateRange->getTotalDays(), 2, ",", ""));
        $extra_miles_charge = '';
        if($offer->getExtraMilesCharge() > 0) {
            $extra_miles_charge = sprintf(trans('cretanrentals::booking.group_extra_miles_charge'), $offer->getExtraMilesCharge());
        }
        $after_hours_charge = '';
        if($offer->getAfterHoursCharge() > 0) {
            $after_hours_charge = sprintf(trans('cretanrentals::booking.group_after_hours_charge'), $offer->getAfterHoursCharge());
        }
        $remote_location_charge = '';
        if($offer->getRemoteLocationCharge() > 0) {
            $remote_location_charge = sprintf(trans('cretanrentals::booking.group_remote_location_charge'), $offer->getRemoteLocationCharge());
        }
        $priceTexts = [
            'perDay' => sprintf(trans('cretanrentals::booking.group_price_per_day'), $pricePerDay),
            'total'  => sprintf(trans('cretanrentals::booking.group_total_price'), $offer->getTotalDiscountPrice(), $this->dateRange->getTotalDays()),
            'extra_miles' => $extra_miles_charge,
            'after_hours' => $after_hours_charge,
            'remote_location' => $remote_location_charge,
        ];

        // view data for the group list item
        $data = [
            'id'           => $group->id,
            'title'        => $title,
            'name'         => 'Group '.$group->name.' '.$group->description,
            'priceTexts'   => $priceTexts,
            'image'        => $group->getGroupImage(),
            'isAvailable'  => $offer->hasAvailability(),
            'selected'     => $group->id == $this->selectedGroup,
            'engine'       => $group->engine,
            'luggage'      => $group->capacity,
            'transmission' => ucfirst($group->transmission),
            'doors'        => $group->doors,
            'seats'        => $group->seats,
            'fuel'         => ucfirst($group->fuel)

        ];
        return $data;
    }

    /**
     * Returns the markup of a hidden form containing all reservation data (steps>1)
     */
    private function getHiddenForm($inputsOnly  = false)
    {
        $form = $inputsOnly ? '' :
            \Form::open(array('class'=>'main','id'=>'bookingDataForm','method' => 'post', 'url' => route('booking.index') ));

        // data needed in every step
        $form .= \Form::hidden('pickup_location', $this->pickup_location);
        $form .= \Form::hidden('dropoff_location', $this->dropoff_location);
        $form .= \Form::hidden('pickup_date', $this->pickup_date);
        $form .= \Form::hidden('dropoff_date', $this->dropoff_date);
        $form .= \Form::hidden('pickup_time', $this->pickup_time);
        $form .= \Form::hidden('dropoff_time', $this->dropoff_time);

        // step related data //
        // group, if set
        $groupId = $this->group? $this->group : '';
        $form .= \Form::hidden('group', $groupId);
        //step (where the form will be submitted. Set by js before submit)
        $form .= \Form::hidden('step', '');

        // accessories & fuel plan , and if step >2 (step 2 will always reset all booking data that we not pass in the url)
        if($this->step > 2) {
            foreach ($this->view_data['accessories'] as $accessory) {
                $count = isset($this->accessories[$accessory['id']]) ? $this->accessories[$accessory['id']] : 0 ;
                $form .= \Form::hidden('accessory[' . $accessory['id'] . ']', $count, ['id' => 'accessory_' . $accessory['id'], 'class' => '', 'data-price' => $accessory['price']]);
            }
            $form .= \Form::hidden('fuel_plan_value', $this->fuel_plan, ['id' => 'fuel_plan_value']);
            $form .= \Form::hidden('discount_coupon', $this->discount_coupon, ['id' => 'discount_coupon']);
            // personal info hidden inputs for ajax steps (except step 4 where the inputs are visible)
            if($this->step !== 4)
            {
                $form .= \Form::hidden('customer_name', $this->customer_name);
                $form .= \Form::hidden('customer_email', $this->customer_email);
                $form .= \Form::hidden('customer_telephone', $this->customer_telephone);
                $form .= \Form::hidden('customer_country_id', $this->customer_country_id);
                $form .= \Form::hidden('comment', $this->comment);
                $form .= \Form::hidden('flight_details', $this->flight_details);
                $form .= \Form::hidden('customer_address', $this->customer_address);
            }
        }

        if(!$inputsOnly){ // close the form if asked
            $form .= \Form::close();
        }
        return $form;

    }
    /*
     * Calculate booking price
     */
    public function getPrice(){

        $this->processUrlParams();
        $this->processAjaxParams();
        $offer = $this->getGroupOffer($this->group); //todo handle invalid/missing group
        $data['total_price_initial'] = $offer->getBasePrice();
        $data['extra_miles_charge'] = $offer->getExtraMilesCharge();
        $data['after_hours_charge'] = $offer->getAfterHoursCharge();
        $data['fuel_plan_charge'] = $offer->getFuelPlanCharge();

        $data['final_price'] = $offer->getTotalDiscountPrice();
        $data['total_days'] = $this->dateRange->getTotalDays();

        // check availability of listing
        $data['available'] = $offer->hasAvailability() ? 1 : 0; //todo if 0 redirect to step 1


        foreach($this->accessories as $accessory_id => $counter)
        {
            $data['accessories'][$accessory_id]['price'] = $offer->getAccessoryPrice($accessory_id);
            $data['accessories'][$accessory_id]['count'] = $counter;
        }
        $data['status'] = 1;
        return $data;
    }

    /*
     * Validates discount option and return the getPrice data if valid
     */
    public function checkDiscountCoupon(\Illuminate\Http\Request $request){
        $discount_coupon = $request->get('discount_coupon');
        //check if valid/invalid (to show appropriate msg) or empty (to hide any existing messages in coupon field)
        if(empty($discount_coupon)){
            $msg = '';
        }
        else{
            $site   = Site::where('name', '=', config('appportal.name'))->first();
            $coupon = Coupon::where('text', '=', $discount_coupon)->where('site', '=', $site->id)->first();
            $msg = $coupon ? '<span class="text-green"><i class="fa fa-check"></i> '.trans('cretanrentals::booking.valid_coupon').'</span>':
                             '<span class="text-red">' .trans('cretanrentals::booking.invalid_coupon').'</span>';
        }
        $data = $this->getPrice(); //always

        return \Response::json([
            'status'   => 1,
            'msg'      => $msg,
            'offer'    => $data
        ]);

    }

    /**
     * @param groupId
     * Get the offer for a group
     * Calculation is made based on a group's listing and
     * the dates,locations etx as set in the processUrlParams() function
     */
    private function getGroupOffer($groupid)
    {
        // Get the request
        $request = request();
        // get a listing for this group (StaticDiscount & RentalOffer need a listing TODO maybe refactor them a bit to work with group obj also? )
        $listing = Listing::where('group_id',$groupid)->first(); // Any of the group should do

        $discount = new StaticDiscount($this->dateRange->getTotalDays(), $this->dateRange->getDaysIndex(),$listing);
        if($this->discount_coupon){
            $discount = new CouponDiscount($discount, $request->get('discount_coupon'));
        }
        $offer         = $listing->getOffer(
            $this->dateRange,
            $discount,
            $this->pickup_location,
            $this->dropoff_location,
            $this->accessories,
            $this->fuel_plan);

        return $offer;
    }

    /**
     * Store a reservation.
     *
     * @return Response
     */
    public function store(\Illuminate\Http\Request $request)
    {
        try
        {
            //special handle for dropoff location (case same as pickup which comes with value 0)
            if(!$request->get('dropoff_location')){
                $request->merge(['dropoff_location' => $request->get('pickup_location')]);
            }

            $request->merge([
                'dropoff_date' => str_replace('/','-',$request->get('dropoff_date')),
                'pickup_date'  => str_replace('/','-',$request->get('pickup_date')),
            ]);
            // Comment cannot be null
            if(!$request->get('comment')){
                $request->merge(['comment' => '']);
            }
            // flight_details cannot be null
            if(!$request->get('flight_details')){
                $request->merge(['flight_details' => '']);
            }

            $reservation = Reservation::create($request->all());

            // send email messages
            if (!empty($reservation))
            {
               $listing = Listing::find($reservation->listing_id);
               $customer = $reservation->getCustomer();
               $group = Group::find($request->get('group'));
               // ...to client
               Mail::send('cretanrentals::emails.reservation_client', ['reservation' => $reservation, 'listing' => $listing, 'customer' => $customer, 'group' => $group], function($message) use ($reservation, $customer)
               {
               		$message
               			->to($customer->email, $customer->name)
               			->subject('CretanRentals.com: Reservation Confirmation #' . $reservation->id);
               });
               // Change local to en
               $currentLocale = \Illuminate\Support\Facades\App::getLocale();
               \Illuminate\Support\Facades\App::setLocale('en');

               // ...and to admin
               Mail::send('cretanrentals::emails.reservation_admin', ['reservation' => $reservation, 'listing' => $listing, 'customer' => $customer, 'group' => $group], function($message) use ($reservation)
               {
               		$message
               			->to(config('mail_addresses.info_cr'), 'CretanRentals Admins')
               			->cc([config('mail_addresses.argiry'), config('mail_addresses.kiritsis'), '<EMAIL>'])
                   		->subject('New reservation #' . $reservation->id);
               });

                //Reset local
               \Illuminate\Support\Facades\App::setLocale($currentLocale);

                $customer = $reservation->getCustomer();
                $success_text = sprintf(trans('cretanrentals::booking.confirmation_success_text'),$customer->name, $reservation->total_days, $reservation->final_price);
                $heading_text = trans('cretanrentals::booking.reservation_success_heading');
                return Response::json(['status' => 'success', 'msg' => 'Reservation saved', 'notify_text' => $success_text, 'heading_text' => $heading_text]);
            }

            return Response::json(['status' => 'error', 'msg' => '']);
        }
        catch(ValidationException $e)
        {
            report($e);
            $msg = "";
            foreach ($e->getErrors() as $error){
                $msg .= $error."\n";
            }
            return Response::json(['status' => 'error', 'msg' => $msg]);
        }
        catch(Exception $e)
        {
            report($e);
        }
    }
	
}