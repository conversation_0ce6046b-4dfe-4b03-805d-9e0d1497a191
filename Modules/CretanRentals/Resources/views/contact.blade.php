@extends('cretanrentals::layouts.master')

@section('title'){!! Lang::get('cretanrentals::contact.page_title') !!}@stop

@section('meta_description'){!! Lang::get('cretanrentals::contact.meta_description') !!}@stop

@section('head_extra')

@stop


@section('content')
    <!-- CONTACT US
================================================== -->

    <!-- PAGE TITLE -->
    <section class="page-section breadcrumbs text-right stripes">
        <div class="container">
            <div class="page-header">
                <h1>{!! Lang::get('cretanrentals::contact.title') !!}</h1>
                <h2>{!! Lang::get('cretanrentals::contact.subheading') !!}</h2>
            </div>
        </div>
    </section>

    <!-- /PAGE TITLE -->

    <!-- PAGE -->
    <section class="page-section color">
        <div class="container">

            <div class="row">

                <div class="col-md-6">
                    <div class="contact-info">
                        <div class="media-list">
                            <div class="media">
                                <i class="pull-left fa fa-home"></i>
                                <div class="media-body">
                                    <strong>{!! Lang::get('cretanrentals::contact.address_label') !!}</strong><br>
                                    {!! Lang::get('cretanrentals::contact.address_text') !!}
                                </div>
                            </div>
                            <div class="media">
                                <i class="pull-left fa fa-phone"></i>
                                <div class="media-body">
                                    <strong>{!! Lang::get('cretanrentals::contact.phone_label') !!}</strong><br>
                                    {!! Lang::get('cretanrentals::contact.phone_text') !!}
                                </div>
                            </div>
                            <div class="media">
                                <i class="pull-left fa fa-envelope-o"></i>
                                <div class="media-body">
                                    <strong>{!! Lang::get('cretanrentals::contact.email_label') !!}</strong><br>
                                    {!! Lang::get('cretanrentals::contact.email_text') !!}
                                </div>
                            </div>
                        </div>
                        <div class="media">
                            <div class="media-body">
                                {!! Lang::get('cretanrentals::contact.text') !!}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 text-left">

                    <!-- Contact form -->
                    <!-- Success-Messages -->
                    @if ($message = Session::get('success'))
                        <div class="alert alert-success alert-block">
                            {!! $message !!}
                        </div>
                    @endif
                    {!! Form::open(['route' => 'contact.store', 'class' => 'contact-form', 'id' => 'contact-form']) !!}
                        <div class="outer">
                            <div class="form-group af-inner">
                                {!! Form::label('name', Lang::get('cretanrentals::contact.your_name'), ['class' => 'sr-only']) !!}
                                {!! Form::text('name','',['class' => 'form-control placeholder', 'placeholder' => Lang::get('cretanrentals::contact.your_name')]) !!}
                                <span class="error">{!! $errors->first('name') !!}</span>
                            </div>
                        </div>

                        <div class="outer">
                            <div class="form-group af-inner">
                                {!! Form::label('email', Lang::get('cretanrentals::contact.your_email'), ['class' => 'sr-only']) !!}
                                {!! Form::text('email','',['class' => 'form-control placeholder', 'placeholder' => Lang::get('cretanrentals::contact.your_email')]) !!}
                                <span class="error">{!! $errors->first('email') !!}</span>
                            </div>
                        </div>
                        <div class="outer">
                            <div class="form-group af-inner">
                                {!! Form::label('phone', Lang::get('cretanrentals::contact.your_phone'), ['class' => 'sr-only']) !!}
                                {!! Form::text('phone','',['class' => 'form-control placeholder', 'placeholder' => Lang::get('cretanrentals::contact.your_phone')]) !!}
                                <span class="error">{!! $errors->first('phone') !!}</span>
                            </div>
                        </div>

                        <div class="form-group af-inner">
                            {!! Form::label('comment', Lang::get('cretanrentals::contact.your_comment'), ['class' => 'sr-only']) !!}
                            {!! Form::textarea('comment','',['class' => 'form-control placeholder', 'placeholder' => Lang::get('cretanrentals::contact.your_comment')]) !!}
                            <span class="error">{!! $errors->first('comment') !!}</span>
                        </div>

                        <div class="outer">
                            <div class="form-group af-inner">
                                <div class="pull-left captcha-holder">
                                    {!! NoCaptcha::renderJs(App::getLocale()) !!}
                                    {!! NoCaptcha::display() !!}
                                    <span class="error">{!! $errors->first('g-recaptcha-response') !!}</span>
                                </div>
                                {!! Form::submit(Lang::get('cretanrentals::contact.submit'), ['class' => 'form-button form-button-submit btn btn-theme pull-right']) !!}
                                <div class="clearfix"></div>
                            </div>
                        </div>

                    {!! Form::close() !!}
                    <!-- /Contact form -->

                </div>

            </div>

        </div>
    </section>
    <!-- /PAGE -->
@stop
