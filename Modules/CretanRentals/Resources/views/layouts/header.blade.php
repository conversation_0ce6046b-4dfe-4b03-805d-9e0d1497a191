<!-- HEADER -->
    <header class="header fixed">
        <div class="header-wrapper">
            <div class="container">

                <!-- Logo -->
                <div class="logo">
                    <a href="{!! route('home') !!}"><img src="{!! asset('cretan/img/cretanrentals_logo_RGB.png') !!}" alt="Cretanrentals"/></a>
                </div>
                <!-- /Logo -->

                <!-- Mobile menu toggle button -->
                <a href="#" class="menu-toggle btn btn-theme-transparent"><i class="fa fa-bars"></i></a>
                <!-- /Mobile menu toggle button -->

                <!-- Navigation -->
                <nav class="navigation closed clearfix">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <!-- navigation menu -->
                            <a href="#" class="menu-toggle-close btn"><i class="fa fa-times"></i></a>
                            <ul class="nav sf-menu">
                                @if (isset($errorPage) || isset($exception))
                                    <li><a class="main-menu-link" href="{!! route('listings.index') !!}">{!! Lang::get('cretanrentals::common.listings_list_text') !!}</a></li>
                                @else
                                    <li class="dropdown fleet {!! @$fleet_selected !!}"><span class="main-menu-noLinkItem" >{!! Lang::get('cretanrentals::common.listings_list_text') !!}<span class="caret"></span></span>
                                        <ul class="padding-top-small">
                                            <li class="condensed">{!! link_to_route('listings.index', Lang::get('cretanrentals::common.all_groups')) !!}</li>
                                            @foreach ($groups_menu_seo as $groupKey => $groupName)
                                                <li class="condensed">{!! link_to_route('listings.index', $groupName, ['group' => $groupKey], ['class' => ' ']) !!}</li>
                                            @endforeach
                                        </ul>
                                    </li>
                                @endif
                                <li class="{!! @$booking_selected !!}"><a class="main-menu-link" href="{!! route('booking.index') !!}">{!! Lang::get('cretanrentals::common.booking_text') !!}</a></li>
                                <li class="{!! @$services_selected !!}"><a class="main-menu-link" href="{!! route('services') !!}">{!! Lang::get('cretanrentals::common.services_list_text') !!}</a></li>
                                <li class="{!! @$policy_selected !!}"><a class="main-menu-link" href="{!! route('policy') !!}">{!! Lang::get('cretanrentals::common.policy_list_text') !!}</a></li>
                                <li class="{!! @$contact_selected !!}"><a class="main-menu-link" href="{!! route('contact') !!}">{!! Lang::get('cretanrentals::common.contact_us') !!}</a></li>
                                @if(isset($enabledLanguages))
                                <li class="dropdown lang-dropdown">
                                    <a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" href="javascript:;" title="{!! $enabledLanguages['current']->translate($enabledLanguages['current']->locale)->text !!}">
                                        <img src="{!! asset('cretan/img/'.$enabledLanguages['current']->locale.'.png') !!}" title="{!! $enabledLanguages['current']->translate($enabledLanguages['current']->locale)->text !!}" class=""/> <span class="caret"></span>
                                    </a>
                                    <ul class="lang-items">
                                        @foreach ($enabledLanguages['other'] as $enabledLanguage)
                                            <?php
                                            $link = empty($route) ? LaravelLocalization::getLocalizedURL($enabledLanguage->locale) : LaravelLocalization::getURLFromRouteNameTranslated($enabledLanguage->locale, "routes.{$route}", array_filter(Route::getCurrentRoute()->parameters()));
                                            ?>
                                            <li>
                                                <a href="{!! $link !!}">
                                                    <img src="{!! asset('cretan/img/'.$enabledLanguage->locale.'.png') !!}" title="{!! $enabledLanguage->translate($enabledLanguage->locale)->text !!}" class=""/>
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>
                                </li>
                                @endif
                            </ul>
                            <!-- /navigation menu -->
                        </div>
                    </div>
                    <!-- Add Scroll Bar -->
                    <div class="swiper-scrollbar"></div>
                </nav>
                <!-- /Navigation -->

            </div>
        </div>

    </header>
    <!-- /HEADER -->
