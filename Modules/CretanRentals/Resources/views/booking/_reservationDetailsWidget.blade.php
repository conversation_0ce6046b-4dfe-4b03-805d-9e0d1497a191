<div class="widget shadow widget-details-reservation">
    <h4 class="widget-title">{!! Lang::get('cretanrentals::booking.reservation_detail.title') !!}</h4>

    <div class="widget-content">
        @if($step > 2 && $group)
            <div class="groupData">
                <h5 class="widget-title-sub">{!! Lang::get('cretanrentals::booking.reservation_detail.car_info') !!}</h5>
                <div class="img-text margin-top">
                    <img class="img-responsive img-rounded" src="{!! asset($group->getGroupImage()) !!}" alt="" >
                    <p class="text-center hover-top"><small>{!! 'Group '.$group->name.' '.$group->description !!}</small></p>
                    <div class="extras hover-bottom">
                        {{--EXTRAS & OPTIONS SELECTED--}}
                        @if(isset($reservation_details['accessories']))
                                @foreach($reservation_details['accessories'] as $accessory)
                                    @if($accessory['count']>0)
                                        <p class="pull-left padding-right-tiny"><small><i class="fa fa-check"></i> {!! $accessory['name'] !!} <span class="">(x{!! $accessory['count'] !!})</span></small></p>
                                    @endif
                                @endforeach
                        @endif
                        @if(isset($reservation_details['fuel_plan']))
                            <p class="pull-left padding-right-tiny"><small><i class="fa fa-check"></i> {!! Lang::get('cretanrentals::booking.fuel_plan_option_1') !!}</small></p>
                         @endif
                            <div class="clearfix"></div>
                    </div>
                </div>
                <p class=""><small>{!! $group->getGroupCarsTitle() !!}</small></p>
            </div>
            <a href="{{route('booking.index')}}{!! $url_variables_to_step_2 !!}" class="block"><i class="fa fa-edit" aria-hidden="true"></i>
                {!! Lang::get('cretanrentals::booking.reservation_detail.change_car') !!}</a>

        @endif
            <div class="clearfix"></div>
        <div class="pickupData">
            <h5 class="widget-title-sub">{!! Lang::get('cretanrentals::booking.reservation_detail.pickup_details') !!}</h5>
            <div class="media">
                <span class="media-object pull-left"><i class="fa fa-calendar"></i></span>
                <div class="media-body"><p>{!! $reservation_details['pdate'] !!}</p></div>
            </div>
            <div class="media">
                <span class="media-object pull-left"><i class="fa fa-clock-o"></i></span>
                <div class="media-body"><p>{!! $reservation_details['ptime'] !!}</p></div>
            </div>
            <div class="media">
                <span class="media-object pull-left"><i class="fa fa-location-arrow"></i></span>
                <div class="media-body"><p>{!! $reservation_details['plocation'] !!}</p></div>
            </div>
        </div>
        <div class="dropoffData">
            <h5 class="widget-title-sub">{!! Lang::get('cretanrentals::booking.reservation_detail.dropoff_details') !!}</h5>
            <div class="media">
                <span class="media-object pull-left"><i class="fa fa-calendar"></i></span>
                <div class="media-body"><p>{!! $reservation_details['ddate'] !!}</p></div>
            </div>
            <div class="media">
                <span class="media-object pull-left"><i class="fa fa-clock-o"></i></span>
                <div class="media-body"><p>{!! $reservation_details['dtime'] !!}</p></div>
            </div>
            <div class="media">
                <span class="media-object pull-left"><i class="fa fa-location-arrow"></i></span>
                <div class="media-body"><p>{!! $reservation_details['dlocation'] !!}</p></div>
            </div>
        </div>

        <div class="clearfix"></div>


        <a href="{{route('booking.index')}}{!! $url_variables_to_step_1 !!}" class=""><i class="fa fa-edit margin-top" aria-hidden="true"></i>
             {!! Lang::get('cretanrentals::booking.reservation_detail.change_search') !!}</a>


    </div>
</div>