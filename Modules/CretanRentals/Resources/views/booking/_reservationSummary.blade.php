<div class="widget shadow margin-bottom" id="reservation-summary">
    <h4 class="widget-title">{!! Lang::get('cretanrentals::booking.reservation_detail.title') !!}</h4>
    <div class="clearfix"></div>
    <div class="widget-content">
        {{--car info--}}
        <div class="summary-section border-bottom-dash">
            <h5 class="text bold ">{!! Lang::get('cretanrentals::booking.reservation_detail.car_info') !!}</h5>
            <p class="">{!! 'Group '.$group->name.' '.$group->description !!} : {!! $group->getGroupCarsTitle() !!}</p>
            {{--<img src="{!! asset($group->getGroupImage()) !!}" alt="" class="">--}}
        </div>
        {{--pickup--}}
        <div class="summary-section border-bottom-dash">
            <h5 class="text bold margin-top">{!! Lang::get('cretanrentals::booking.reservation_detail.pickup_details') !!}</h5>
            <p class="">{!! $reservation_details['pdate'] !!}, {!! $reservation_details['plocation'] !!}, {!! $reservation_details['ptime'] !!}</p>
        </div>
        {{--dropoff--}}
        <div class="summary-section border-bottom-dash">
            <h5 class="text bold margin-top">{!! Lang::get('cretanrentals::booking.reservation_detail.dropoff_details') !!}</h5>
            <p class="text">{!! $reservation_details['ddate'] !!}, {!! $reservation_details['dlocation'] !!}, {!! $reservation_details['dtime'] !!}</p>
        </div>
        {{--comments--}}
        @if($reservation_details['personal_info']['comment'])
        <div class="summary-section border-bottom-dash">
            <h5 class="text bold">{!! Lang::get('cretanrentals::booking.comments') !!}</h5>
            <p>{{$reservation_details['personal_info']['comment']}}</p>
        </div>
        {{--personal info--}}
        <div class="summary-section border-bottom-dash">
            <h5 class="text bold">{!! Lang::get('cretanrentals::booking.personal_details') !!}</h5>
            <p>{{$reservation_details['personal_info']['customer_name']}},
                {{$reservation_details['personal_info']['customer_address']}},
                @if(!empty($reservation_details['personal_info']['customer_country']) )
                {{$reservation_details['personal_info']['customer_country']}}
                @endif
            </p>
            <p>
                {{$reservation_details['personal_info']['customer_telephone']}},
                {{$reservation_details['personal_info']['customer_email']}}
            </p>

        </div>
        @endif
        {{--flight--}}
        @if($reservation_details['personal_info']['flight_details'])
        <div class="summary-section border-bottom-dash">
            <h5 class="text bold">{!! Lang::get('cretanrentals::booking.flight_number') !!}</h5>
            <p>{{$reservation_details['personal_info']['flight_details']}}</p>
        </div>
        @endif
        {{--discount coupon--}}
        @if(isset($reservation_details['discount_coupon']) )
            <div class="summary-section border-bottom-dash">
                <h5 class="text bold">{!! Lang::get('cretanrentals::forms.discount_coupon') !!}</h5>
                <p>{{$reservation_details['discount_coupon']}} <small class="text-green pull-right">(<i class="fa fa-check"></i>)</small></p>
            </div>
        @endif
        @if(isset($reservation_details['accessories']) && !empty($reservation_details['accessories']))
        <div class="summary-section border-bottom-dash">
            <h5 class="text bold margin-top">{!! Lang::get('cretanrentals::booking.accessories') !!}</h5>
                <table class="table table-sm">
                    <tbody>
                @foreach($reservation_details['accessories'] as $accessory)
                    @if($accessory['count']>0)
                        <tr>
                            <td><span class="t">{!! $accessory['name'] !!}</span></td>
                            <td><span class="text-light-blue">(x{!! $accessory['count'] !!}) </span></td>
                            <td class="text-right"><span class="">€{{$accessory['price']*$offer->getTotalDays()*$accessory['count']}}</span></td>
                        </tr>
                    @endif
                @endforeach
                    </tbody>
                </table>
            </div>
        @endif
        <div class="summary-section">
            <h5 class="text bold margin-top">
                {!! Lang::get('cretanrentals::booking.payment_details_title') !!} </h5>
                <table class="table table-sm">
                    <tbody>
                    <tr>
                        <td>{!! Lang::get('cretanrentals::booking.initial_price') !!}</td>
                        <td class="text-right">€{!! $offer->getBasePrice() !!}</td>
                    </tr>
                    @if ( $offer->getExtraMilesCharge() > 0 )
                        <tr>
                            <td>{!! Lang::get('cretanrentals::booking.extra_miles') !!}</td>
                            <td class="text-right">€{!! $offer->getExtraMilesCharge() !!}</td>
                        </tr>
                    @endif
                    @if ( $offer->getAfterHoursCharge() > 0 )
                        <tr>
                            <td>{!! Lang::get('cretanrentals::booking.after_hours') !!}</td>
                            <td class="text-right">€{!! $offer->getAfterHoursCharge() !!}</td>
                        </tr>
                    @endif
                    @if ( $offer->getRemoteLocationCharge() > 0 )
                        <tr>
                            <td>{!! Lang::get('cretanrentals::booking.remote_location') !!}</td>
                            <td class="text-right">€{!! $offer->getRemoteLocationCharge() !!}</td>
                        </tr>
                    @endif
                    @if ( $offer->getFuelPlanCharge() > 0 )
                        <tr>
                            <td>{!! Lang::get('cretanrentals::booking.fuel_plan') !!}</td>
                            <td class="text-right">€{!! $group->fuel_plan !!}</td>
                        </tr>
                    @endif
                    <?php /*
                    @if ( $offer->hasExtraDayCharge() )
                        <tr>
                            <td>{!! Lang::get('cretanrentals::booking.extra_day') !!}: 1 {!! Lang::get('reservation.day') !!}</td>
                            <td class=""></td>
                        </tr>
                    @endif
                    */?>
                    </tbody>
            </table>
            <table class="table">
                <tbody>
                <tr id="summary-final-price">
                    <td class=""><strong>{!! Lang::get('cretanrentals::booking.total_days') !!}: {{$offer->getTotalDays()}}</strong></td>
                    <td class="text-right"><strong>{!! Lang::get('cretanrentals::booking.total_cost') !!}: €{!! $offer->getTotalDiscountPrice()!!} </strong></td>
                </tr>
                </tbody>
            </table>
            </h5>

        </div>






    </div>
</div>