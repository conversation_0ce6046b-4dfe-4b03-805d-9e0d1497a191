<div class="group-list-item <?php if($group['selected']) echo 'selected' ?>">
    <div class="row">
        <div class="col-sm-5">
            <img class="responsive" src="{{ $group['image'] ? asset($group['image']) : asset('cretan/img/car-pl-img.png') }}" alt=""/>
        </div>
        <div class="col-sm-7">
            <div class="group-info">
                {{--item title--}}
                <div class="group-title margin-bottom text-dark border-bottom">
                    {!! $group['title'] !!}
                </div>
                {{--group price info--}}
                <div class="price-info pull-right clear-xxs text-right">
                    {!! $group['priceTexts']['perDay'] !!}

                    <div class="condensed price-total">{!! $group['priceTexts']['total'] !!}</div>
                    @if(!empty($group['priceTexts']['after_hours']))
                        <div class="condensed theme-color" style="font-size: 12px">{!! $group['priceTexts']['after_hours'] !!}</div>
                    @endif
                    @if(!empty($group['priceTexts']['extra_miles']))
                    <div class="condensed theme-color" style="font-size: 12px">{!! $group['priceTexts']['extra_miles'] !!}</div>
                    @endif
                    @if(!empty($group['priceTexts']['remote_location']))
                    <div class="condensed theme-color" style="font-size: 12px">{!! $group['priceTexts']['remote_location'] !!}</div>
                    @endif
                </div>
                {{--group name & availability--}}
                <div class="pull-left clear-xxs">
                    <h5 class="group-caption-title-sub">{!! $group['name'] !!}</h5>
                    @if($group['isAvailable'])
                        <p class="margin-right text-success "><small><i class="fa fa-check"></i> {!! trans('cretanrentals::common.available') !!}</small></p>
                    @else
                        <p class="margin-right text-red ">
                            <small><i class="fa fa-warning"></i> {!! trans('cretanrentals::common.not_available') !!}</small>
                        </p>
                    @endif

                    <p class="text-info"><small>
                            <i class="fa fa-info-circle"></i>
                            <a target="_blank" href="{!! route('policy') !!}" class="policy_modal">
                                {!! Lang::get('cretanrentals::common.policy_text_footer') !!}
                            </a>
                        </small></p>
                    <div class="clearfix"></div>
                </div>
                <div class="clearfix"></div>
                <div class="booking-info margin-top">
                    <ul class="">
                        <li>{!! trans('cretanrentals::services.inclusive_rates') !!}</li>
                        <li>{!! trans('cretanrentals::services.kilometers') !!}</li>
                        <li>{!! trans('cretanrentals::services.insurance') !!}</li>
                        <li>{!! trans('cretanrentals::services.no_credit_card') !!}</li>
                        <div class="clearfix"></div>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="">
        {{--group characteristics--}}
        <table class="table">
            <tr>
                <td>
                    <div data-toggle="tooltip" data-placement="bottom" title="{!! trans('cretanrentals::common.engine') !!}">
                        <i class="fa fa-car"></i> {!! $group['engine'] !!}
                    </div>
                </td>
                <td>
                    <div data-toggle="tooltip" data-placement="bottom" title="{!! trans('cretanrentals::common.doors') !!}">
                        <img src="{{ asset('cretan/img/car-doors-open2.png') }}" alt="" height="14"> {!! $group['doors'] !!}
                    </div>
                </td>
                <td>
                    <div data-toggle="tooltip" data-placement="bottom" title="{!! trans('cretanrentals::common.fuel') !!}">
                        <i class="fa fa-tint"></i> {!! $group['fuel'] !!}
                    </div>
                </td>
                <td>
                    <div data-toggle="tooltip" data-placement="bottom" title="{!! trans('cretanrentals::common.gearbox') !!}">
                        <i class="fa fa-cogs"></i> {!! $group['transmission'] !!}
                    </div>
                </td>

                <td>
                    <div data-toggle="tooltip" data-placement="bottom" title="{!! trans('cretanrentals::common.seats') !!}">
                        <i class="fa fa-users"></i> {!! $group['seats'] !!}
                    </div>
                </td>
                <td>
                    <div data-toggle="tooltip" data-placement="bottom" title="{!! trans('cretanrentals::common.luggage') !!}">
                        <i class="fa fa-suitcase"></i> {!! $group['luggage'] !!}
                    </div>
                </td>
                @if($group['isAvailable'])
                    <td class="buttons hidden-xs">
                        <a class="btn btn-theme select-group ajaxStep" data-groupid="{{ $group['id'] }}" data-step=3 href="javascript:;">
                            {!! trans('cretanrentals::booking.select_group_button') !!}
                        </a>
                    </td>
                @else
                    <td class="buttons hidden-xs" data-toggle="tooltip" data-placement="top" title="{!! trans('cretanrentals::booking.select_group_button_not_available_tooltip') !!}">
                        <a class="btn btn-muted quote-btn" href="javascript:;" data-groupid="{{ $group['id'] }}">
                            <i class="fa fa-envelope-o"></i> {!! trans('cretanrentals::booking.select_group_button_not_available') !!}
                        </a>
                    </td>
                @endif
            </tr>
        </table>
        <div class="visible-xxs visible-xs hidden">
            @if($group['isAvailable'])
                <a class="btn btn-theme btn-block select-group ajaxStep" data-step=3 data-groupid="{{ $group['id'] }}" href="javascript:;" style="max-width:100%">
                    {!! trans('cretanrentals::booking.select_group_button') !!}
                </a>
            @else
                <a class="btn btn-muted btn-block quote-btn" href="javascript:;" data-groupid="{{ $group['id'] }}">
                    <i class="fa fa-envelope-o" style="max-width:100%"></i> {!! trans('cretanrentals::booking.select_group_button_not_available') !!}
                </a>
            @endif
        </div>
    </div>
</div>