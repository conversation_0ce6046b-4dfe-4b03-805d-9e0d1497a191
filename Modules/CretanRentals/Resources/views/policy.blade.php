@extends('cretanrentals::layouts.master')

@section('title'){!! Lang::get('cretanrentals::policy.page_title') !!}@stop

@section('meta_description'){!! Lang::get('cretanrentals::policy.meta_description') !!}@stop

@section('head_extra')

@stop


@section('content')
    <!-- POLICY
================================================== -->

        <!-- PAGE TITLE -->
        <section class="page-section breadcrumbs text-right">
            <div class="container">
                <div class="page-header">
                    <h1>{!! Lang::get('cretanrentals::policy.title') !!}</h1>
                    <h2>{!! Lang::get('cretanrentals::policy.subtitle') !!}</h2>
                </div>
            </div>
        </section>
        <!-- /PAGE TITLE -->
        <!-- PAGE - Insurance section -->
        <section class="page-section color insurance-section">
            <div class="container">
                <h3 class="margin-top-small">{!! Lang::get('cretanrentals::policy.insurance.title') !!}</h3>
                <p class="mb-15 lead">{!! Lang::get('cretanrentals::policy.insurance.sub_text') !!}</p>
                <div class="row">
                    <div class="col-md-9">
                        @foreach($insuranceItems as $item)
                            <div class="insurance-item">
                                <img class="pull-left" src="{{asset('cretan/img/shield.png')}}" alt="">
                                <h3>{!! $item['title'] !!}</h3>
                                {{$item['text']}}
                                <div class="clearfix"></div>
                            </div>
                            <hr>
                        @endforeach
                        <p>{!! Lang::get('cretanrentals::policy.insurance.note') !!}</p>
                    </div>
                </div>
            </div>
        </section>
        <!-- /PAGE - Insurance section -->
    <section class="page-section image policy">
        <div class="container">
            <h2 class="section-title text-left">
                {!! Lang::get('cretanrentals::policy.policy.title') !!}
            </h2>
        </div>
    </section>
    <section class="page-section color policy-section">
        <div class="container">
        @foreach($policyItems as $item)
            <div class="policy-item">
                <h3><i class="fa fa-info-circle text-light-blue"></i> {!! $item['title'] !!}</h3>
                {!! $item['text'] !!}
                <div class="clearfix"></div>
            </div>
            <hr>
        @endforeach
        </div>
    </section>
    {{--accident report section--}}
    @if(($accidentReportItems))
    <section class="page-section border-top accident-report-section bg-white">
        <div class="container">
            {!! $accidentReportItems !!}
        </div>
    </section>
    @endif
@stop