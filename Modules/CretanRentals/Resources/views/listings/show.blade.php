@extends('cretanrentals::layouts.master')

@section('title'){!! $page_title !!}@stop

@section('meta_description'){!! $meta_description !!}@stop

@section('head_extra')
    <link href="{!! asset('cretan/plugins/fotorama/fotorama.css') !!}" rel="stylesheet">
@stop


@section('content')
    <!-- LISTING DETAIL
================================================== -->
    <!-- PAGE TITLE -->
    <section class="page-section breadcrumbs text-right">
        <div class="container">
            <div class="page-header">
                <h1>{!! $markup_title !!}</h1>
            </div>
        </div>
    </section>
    <!-- /PAGE TITLE -->

    <!-- PAGE WITH SIDEBAR -->
    <section class="page-section with-sidebar sub-page">
        <div class="container">
            <div class="row">
                <!-- ΜΑΙΝ CONTENT -->
                <div class="col-md-8 content" id="content">
                    <!----- IMAGES ------->
                    @if (($imgCount = $listing->images->count()) > 0)
                    	@if ($imgCount > 1)
                        <div class="fotorama" data-nav="thumbs" data-allowfullscreen="true" data-width="100%" data-maxheight="550px">
                            @foreach($listing->images as $imageKey => $image)
                                <a href="<?php echo $image->getPath(false); ?>"><img src="<?php echo $image->getPath(false); ?>" width="144" height="96"></a>
                            @endforeach
                        </div>
                        @else
	                    <div class="">
	                    	<img class="responsive" src="<?php echo $listing->images->first()->getPath(false); ?>" alt="{!! $listing->title !!}"/>
	                    </div>
                        @endif
                    @elseif (!empty($listing->image))
                    <div class="">
                        <img class="responsive" src="{!! asset($listing->image) !!}"  alt="{!! $listing->title !!}"/>
                    </div>
                    @else
                        <div>
                            <img class="responsive" src="{!! asset('cretan/img/car-pl-img.png') !!}"  alt=""/>
                        </div>
                    @endif
                    <!----- ./IMAGES ------->
<div class="white-wrapper">
    <div class="car-header <?php echo $listing->images->count() > 1 ? 'alt_fotormama' : 'alt_single' ?>">
        <h3 class="pull-left">{!! $listing->manufacturer !!} {!! $listing->model !!}  <span class="text condensed book">{!! Lang::get('cretanrentals::common.or_similar')  !!}</span></h3>
        <h3 class="pull-right text book condensed"><span class="small condensed theme-color">Group {!! $listing->group->name !!}. {!! $listing->group->description !!}</span> </h3>
        <div class="clearfix"></div>
    </div>
    <!----- DESCRIPTION ------->
    @if($listing->description_cr)
        <div class="alert alert-theme text-center">
            <i class="fa fa-quote-left padding-right-tiny opaque"></i> {!! $listing->description_cr !!} <i class="fa fa-quote-right padding-left-tiny opaque"></i>
        </div>
@endif
<!----- ./DESCRIPTION ------->

    <!----- VEHICLE INFO ------->
    <div class="row padding-top-small vehicle-info">
        {{--FEATURES--}}
        <div class="col-md-12 vehicle-features">
            <h4 class="margin-bottom padding-bottom-tiny border-bottom">{!! trans('cretanrentals::listings.show.features_title') !!}:</h4>
            @if($listing->engine)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-car"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.engine')!!}:</span>  {!! $listing->engine !!}cc
                </p>
            @endif
            @if($listing->transmission)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-cogs"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.gearbox')!!}:</span>  {!! ucfirst($listing->transmission) !!}
                </p>
            @endif
            @if ($listing->fuel)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-tint"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.fuel') !!}:</span>  {!! $listing->fuel == 'petrol' ? Lang::get('cretanrentals::common.petrol') : Lang::get('cretanrentals::common.diesel') !!}
                </p>
            @endif
            @if ($listing->consumption)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-road"></i>
                    {!! $listing->consumption !!} <small class="text-muted">l/100km</small>
                </p>
            @endif
            @if ($listing->doors)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-car"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.doors')  !!}:</span>  {!! $listing->doors !!}
                </p>
            @endif
            @if ($listing->seats)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-users"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.seats') !!}:</span>  {!! $listing->seats !!}
                </p>
            @endif
            @if ($listing->capacity)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-suitcase"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.luggage')  !!}:</span>  {!! $listing->capacity !!}</p>
            @endif
            @if ($listing->airbags)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-circle-thin"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.airbags') !!}:</span>  {!! $listing->airbags !!}</p>
            @endif
            @if ($listing->driver_age)
                <p class="info-item margin-bottom pull-left">
                    <i class="fa fa-user"></i>
                    <span class="info-item-title">{!! Lang::get('cretanrentals::common.driver_age')  !!}:</span>  {!! $listing->driver_age !!}</p>
            @endif
            <div class="clearfix"></div>
        </div>
        {{--DEFAULT EQUIPMENT--}}
        <div class="col-md-12 margin-none">
            <div class="vehicle-default-equipment">
                <h4 class="margin-bottom margin-top padding-bottom-tiny border-bottom">{!! trans('cretanrentals::listings.show.equipment_title') !!}:</h4>
                @if ($listing->abs)
                    <p class="info-item margin-bottom pull-left"><i class="fa fa-check text-light-blue"></i> {!! trans('cretanrentals::common.abs') !!}</p>
                @endif

                @if ($listing->radio_cd)
                    <p class="info-item margin-bottom pull-left"><i class="fa fa-check text-light-blue"></i> {!! trans('cretanrentals::common.radio_cd') !!}</p>
                @endif

                @if ($listing->clima)
                    <p class="info-item margin-bottom pull-left"><i class="fa fa-check text-light-blue"></i> {!! trans('cretanrentals::common.ac') !!}</p>
                @endif

                @if ($listing->four_wheeled)
                    <p class="info-item margin-bottom pull-left"><i class="fa fa-check text-light-blue"></i> {!! trans('cretanrentals::common.4x4') !!}</p>
                @endif

                @if ($listing->sunroof)
                    <p class="info-item margin-bottom pull-left"><i class="fa fa-check text-light-blue"></i> {!! Lang::get('cretanrentals::common.sunroof')  !!}</p>
                @endif
            </div>
        </div>
    </div>
    <!----- ./VEHICLE INFO ------->
</div>


                    <!----- INFO BLOCKS ------->
                    <div class="row blocks info-banners">
                        <div class="col-sm-6">
                            <div class="block">
                                <div class="media">
                                    <div class="pull-left"><img src="{!!asset('cretan/img/icons/airplane.png')!!}" alt=""></div>
                                    <div class="media-body">
                                        <h4 class="media-heading">{!! trans('cretanrentals::listings.show.teaser_1_title') !!}</h4>
                                        {!! trans('cretanrentals::listings.show.teaser_1_text') !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="block">
                                <div class="media">
                                    <div class="pull-left"><img src="{!!asset('cretan/img/icons/odometer.png')!!}" alt=""></div>
                                    <div class="media-body">
                                        <h4 class="media-heading">{!! trans('cretanrentals::listings.show.teaser_2_title') !!}</h4>
                                        {!! trans('cretanrentals::listings.show.teaser_2_text') !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!----- ./INFO BLOCKS ------->

                    <!----- GROUP DESCRIPTION ------->
                    @if($listing->group->seo_text_cr)
                        <div class="alert alert-theme text-justify margin-top-large group-description">
                            <p>{!! str_replace('\r\\n','',$listing->group->seo_text_cr) !!}</p>
                        </div>
                    @endif
                    <!----- ./GROUP DESCRIPTION ------->
                </div>
                <!-- /ΜΑΙΝ CONTENT -->

                <!-- SIDEBAR -->
                <aside class="col-md-4 sidebar" id="sidebar">
                    <!-- SEARCH FORM WIDGET-->
                    <div class="widget shadow widget-find-car" id="widget-find-car">

                        <h4 class="widget-title">{!! trans('cretanrentals::listings.show.reservation_widget_title') !!}</h4>
                        <div class="widget-content">
                            <!-- Search form -->
                            <div class=" form-search light">
                                {!!  Form::open(array('class'=>'main', 'method' => 'get', 'url' => route('booking.index') )) !!}
                                {!! Form::hidden('step', '2') !!}
                                {!! Form::hidden('group', $listing->group->id) !!}
                                    <div class="row row-inputs">
                                        {{--pickup location--}}
                                        <div class="col-md-12">
                                            <div class="form-group has-icon has-label selectpicker-wrapper">
                                                {!! Form::label('pickup_location', Lang::get('forms.pickup_location_full')) !!}
                                                {!! Form::select('pickup_location', $searchBucket['locations_dropdown_pickup'], null, ['class' => 'selectpicker', 'data-live-search' => 'true', 'data-size'=>'10' ,'data-width'=>'100%', 'data-container'=>'body']) !!}
                                                <span class="form-control-icon"><i class="fa fa-map-marker"></i></span>
                                            </div>
                                        </div>
                                        {{--pickup date--}}
                                        <div class="col-lg-6 col-md-12 col-sm-6 col-xs-12">
                                            <div class="form-group has-icon has-label">
                                                {!! Form::label('pickup_date', Lang::get('forms.pickup_date_full')) !!}
                                                {!! Form::text('pickup_date', $searchBucket['pickup_date'], ['class' => 'form-control datepicker', 'readonly']) !!}
                                                <span class="form-control-icon"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                        {{--pickup hour--}}
                                        <div class="col-lg-6 col-md-12 col-sm-6 col-xs-12">
                                            <div class="form-group has-icon has-label selectpicker-wrapper">
                                                {!! Form::label('pickup_time', Lang::get('forms.pickup_time_full')) !!}
                                                {!! Form::select('pickup_time', $searchBucket['hours_dropdown'], $searchBucket['pickup_time'], ['class' => 'selectpicker', 'data-live-search' => 'true', 'data-size'=>'6' ,'data-width'=>'100%', 'data-container'=>'body']) !!}
                                                <span class="form-control-icon"><i class="fa fa-clock-o"></i></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row row-inputs mt-10">
                                        {{--dropoff location--}}
                                        <div class="col-md-12">
                                            <div class="form-group has-icon has-label selectpicker-wrapper">
                                                {!! Form::label('dropoff_location', Lang::get('forms.dropoff_location_full')) !!}
                                                {!! Form::select('dropoff_location', $searchBucket['locations_dropdown_dropoff'], null,
                                                                ['class' => 'selectpicker', 'data-live-search' => 'true', 'data-size'=>'10' ,'data-width'=>'100%', 'data-container'=>'body']) !!}
                                                <span class="form-control-icon"><i class="fa fa-map-marker"></i></span>
                                            </div>
                                        </div>
                                        {{--dropoff date--}}
                                        <div class="col-lg-6 col-md-12 col-sm-6 col-xs-12">
                                                <div class="form-group has-icon has-label">
                                                    {!! Form::label('dropoff_date', Lang::get('forms.dropoff_date_full')) !!}
                                                    {!! Form::text('dropoff_date', $searchBucket['dropoff_date'], ['class' => 'form-control datepicker', 'readonly']) !!}
                                                    <span class="form-control-icon"><i class="fa fa-calendar"></i></span>
                                                </div>
                                            </div>
                                        {{--dropoff time--}}
                                        <div class="col-lg-6 col-md-12 col-sm-6 col-xs-12">
                                            <div class="form-group has-icon has-label selectpicker-wrapper">
                                                {!! Form::label('dropoff_time', Lang::get('forms.dropoff_time_full')) !!}
                                                {!! Form::select('dropoff_time', $searchBucket['hours_dropdown'], $searchBucket['dropoff_time'], ['class' => 'selectpicker', 'data-live-search' => 'true', 'data-size'=>'6' ,'data-width'=>'100%', 'data-container'=>'body']) !!}
                                                <span class="form-control-icon"><i class="fa fa-clock-o"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="submit" id="formSearchSubmit3" class="btn btn-submit btn-theme btn-block">{!! trans('cretanrentals::listings.show.reservation_widget_action') !!}</button>
                                </form>
                            </div>
                            <!-- /Search form -->
                        </div>
                    </div>
                    <!-- ./SEACRH FORM WIDGET-->

                    <!----- PREDEFINED OFFERS WIDGET ------->
                    @if($predefinedOffers)
                        <div class="widget shadow widget-offers">
                            <h4 class="widget-title text-center">{!! $predefinedOffers['title'] !!}</h4>
                            <div class="widget-content">
                                <ul>
                                    @foreach($predefinedOffers['offers'] as $offer)
                                    <li class="border-bottom">
                                        <p> <span class="text book condensed">{!! $offer['offerPeriodText'] !!}</span></p>
                                        <p class="pull-left"><i class="fa fa-map-marker text-muted"></i> <span class="text book condensed">{!! $offer['offerLocationPriceText'] !!} </span></p>
                                        <a href="{!! $offer['offerUrl'] !!}" class=" pull-right text-center bookit condensed">{!! Lang::get('cretanrentals::listings.bookit') !!} <i class="fa fa-caret-right"></i> </a>
                                        <div class="clearfix"></div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif
                    <!----- ./PREDEFINED OFFERS WIDGET ------->

                    @if( count($related)>0)
                        <!----- RERLATED CARS WIDGET ------->
                        <div class="widget shadow widget-related">
                            <div class="widget-title">{!! Lang::get('cretanrentals::listings.show.similar_title') !!}</div>
                            <div class=" padding-top-small padding-bottom-small">
                                @foreach($related as $relatedListing)
                                    <div class="media">
                                        @if (!empty($relatedListing->image))
                                        <a class="media-left" href="{!! route('listings.show', $relatedListing->slug) !!}" >
                                            <div style="background-image: url('{!! asset($relatedListing->image) !!}');" class="image-small margin-left"></div>
                                        </a>
                                        @else
                                            <a class="media-left" href="{!! route('listings.show', $relatedListing->slug) !!}" >
                                                <div style="background-image: url('{!! asset('cretan/img/car-pl-img.png') !!}');" class="image-small margin-left"></div>
                                            </a>
                                        @endif
                                        <div class="media-body">
                                            <h4 class="media-heading ">
                                                <a class="condensed" href="{!! route('listings.show', $relatedListing->slug) !!}">{!! $relatedListing->manufacturer !!} {!! $relatedListing->model !!}</a>
                                            </h4>
                                            <span class="text-muted small">Group {!! $relatedListing->group->name !!}. {!! $relatedListing->group->description !!}</span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <!----- ./RERLATED CARS WIDGET ------->
                    @endif
                </aside>
                <!-- /SIDEBAR -->

                @if(count($suggested)>0)
                    <!-- SUGGESTED CARS SECTION-->
                <div class="container">
                    <div class="hidden-lg hidden-md margin-top-large"></div>
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="border-bottom padding-bottom-tiny margin-top text semibold">{!! trans('cretanrentals::listings.show.related_title') !!}</h4>
                            <div class="suggested-carousel">
                                <div class="owl-carousel" id="suggested">
                                    @foreach($suggested as $suggestedListing)
                                        <div class="item">
                                            <a href="{!! route('listings.show', $suggestedListing->slug) !!}">
                                                @if(!empty($suggestedListing->image))
                                                    <img src="{!! asset($suggestedListing->image) !!}" alt="" class="margin-bottom-small"/>
                                                @else
                                                    <img src="{!! asset('cretan/img/car-pl-img.png') !!}" alt="" class="margin-bottom-small"/>
                                                @endif
                                                <span class="suggested-model">{!! $suggestedListing->manufacturer !!} {!! $suggestedListing->model !!}</span>
                                                <span class="suggested-group text-muted small">Group {!! $suggestedListing->group->name !!}. {!! $suggestedListing->group->description !!}</span>
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    <!-- ./SUGGESTED CARS SECTION-->
                @endif
            </div>
        </div>
    </section>
    <!-- /PAGE WITH SIDEBAR -->
@stop

@section('footer_extra')
    <script src="{!! asset('cretan/plugins/fotorama/fotorama.js') !!}"></script>
    <script src="{!! asset('cretan/js/jquery.images.js') !!}"></script>
    <script src="{!! asset('cretan/js/searchBucket.js') !!}"></script>
@stop