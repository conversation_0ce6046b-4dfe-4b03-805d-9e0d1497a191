@extends('cretanrentals::layouts.master')

@section('title'){!! $page_title !!}@stop

@section('meta_description'){!! $page_description !!}@stop

@section('head_extra')

@stop


@section('content')
    <!-- Listing Index
================================================== -->

    <!-- PAGE TITLE -->
    <section class="page-section breadcrumbs text-right">
        <div class="container">
            <div class="page-header">
                <h1>{!! $page_heading !!}</h1>
            </div>
        </div>
    </section>
    <!-- /PAGE TITLE -->

    <!-- PAGE DESCRIPTION-->
    <section class="page-section color padding-bottom-tiny padding-top margin-top">
        <div class="container">
            @if($sidebar_text)
                <div class="alert alert-theme">{!! str_replace('\r\\n','',$sidebar_text) !!} </div>
            @endif
        </div>
    </section>
    <!-- /PAGE DESCRIPTION -->

    <!-- PAGE MAIN CONTENT-->
    <section class="page-section padding-top-tiny">
        <div class="container margin-bottom-small">
            {{--results text--}}
            <p class=" pull-left padding-top-tiny clear-xs"><i class="fa fa-caret-right text-orange padding-right-tiny"></i> {!! $results_text !!}</p>
            {{--change category dropdown--}}
            <form class="form-inline pull-right clear-xs">
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-addon"><i class="fa fa-car text-muted"></i> </div>
                        {!! Form::select('categories', $group_list, $group, ['class' => 'selectpicker categories no-icon', 'data-live-search' => 'false', 'data-size'=>'8' ,'data-width'=>'100%', 'data-container'=>'body']) !!}
                        <div class="input-group-addon"><i class="fa fa-caret-down text-muted"></i></div>
                    </div>
                </div>
            </form>

            <div class="clearfix"></div>
            <hr class="page-divider hidden-xs" style="margin-bottom: 10px;"/>
            <div class="row margin-top-tiny ">
                <!-- CONTENT -->
                <div class="col-md-12 content car-listing " id="content">
                    <!-- LISTING RESULTS -->
                    <div class="row margin-top-small">
                    @foreach($listings as $listing)
                        <div class="col-md-4 col-sm-6 col-xs-12">
                            <div class="thumbnail no-padding thumbnail-car-card clearfix fleet-item">
                                {{--car image--}}
                                @if (!empty($listing->image))
                                    <a class="media-link" href="{!! route('listings.show', $listing->slug) !!}">
                                        <div style="background-image:url('{!! asset($listing->image) !!}')"
                                             class="fleet_list_image"></div>
                                    </a>
                                @else
                                    <div style="background-image:url('{!! asset('cretan/img/car-pl-img.png') !!}')"
                                         class="fleet_list_image"></div>
                                @endif
                                {{--car name & group--}}
                                <div class="caption">
                                    <h4 class="caption-title text-center"><a href="{!! route('listings.show', $listing->slug) !!}">{!! $listing->SEOshortTitle !!}</a></h4>
                                    <div class="text-muted text-center padding-bottom-tiny">
                                        <small>Group {{$listing->group->name}} ({{$listing->group->description}})</small>
                                    </div>
                                    {{--<div class="caption-text">--}}
                                    {{--<h5>from €13</h5>--}}
                                    {{--</div>--}}
                                    <table class="table listing-features">
                                        <tr>
                                            <td>
                                                <span data-toggle="tooltip" data-placement="right" title="{{Lang::get('cretanrentals::common.seats')}}">
                                                    <i class="fa fa-male"></i> {{$listing->seats}}
                                                </span>
                                            </td>
                                            <td>
                                                <span data-toggle="tooltip" data-placement="top" title="{{Lang::get('cretanrentals::common.doors')}}">
                                                    <i class="fa fa-car"></i> {{$listing->doors}}
                                                </span>
                                            </td>
                                            <td>
                                                <div data-toggle="tooltip" data-placement="top" title="{!! trans('cretanrentals::common.luggage') !!}">
                                                    <i class="fa fa-suitcase"></i> {{$listing->capacity}}
                                                </div>
                                            </td>
                                            <td>
                                                <span data-toggle="tooltip" data-placement="top" title="{{Lang::get('cretanrentals::common.gearbox')}}">
                                                    <i class="fa fa-cogs"></i> {{ucfirst($listing->transmission)}}
                                                </span>
                                            </td>
                                            <td>
                                                <span data-toggle="tooltip" data-placement="left" title="{{Lang::get('cretanrentals::common.fuel')}}">
                                                    <i class="fa fa-tint"></i> {{ucfirst($listing->fuel)}}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                    <table class="table">
                                        <tr>
                                            <td class="buttons buttons-green">
                                                <a href="{!! route('listings.show', $listing->slug) !!}" class="btn bottom-green">{{Lang::get('cretanrentals::listings.details')}}</a>
                                            </td>
                                            <td class="buttons buttons-orange">
                                                <a href="{!! route('booking.index',['step' => 2, 'group' => $listing->group]) !!}" class="btn bottom-orange">{{Lang::get('cretanrentals::listings.bookit')}}</a>
                                            </td>
                                        </tr>
                                    </table>

                                </div>
                            </div>
                        </div>
                    @endforeach
                    </div>
                    <!-- LISTINGS RESULTS -->
                </div>
                <!-- /CONTENT -->
            </div>
        </div>
    </section>
    <!-- /PAGE MAIN CONTENT -->
    <div id="url" class="hide">{!! route('listings.index')!!}</div>
@stop

@section('footer_extra')
    <script src="{!! asset('cretan/js/jquery.images.js') !!}"></script>
    <script src="{!! asset('cretan/js/searchBucket.js') !!}"></script>
@stop