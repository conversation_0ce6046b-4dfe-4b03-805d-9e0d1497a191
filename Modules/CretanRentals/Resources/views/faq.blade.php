@extends('cretanrentals::layouts.master')

@section('title'){!! Lang::get('cretanrentals::faq.page_title') !!}@stop

@section('meta_description'){!! Lang::get('cretanrentals::faq.meta_description') !!}@stop

@section('head_extra')

@stop


@section('content')
    <!-- FAQ
================================================== -->

        <!-- PAGE TITLE -->
        <section class="page-section breadcrumbs text-right stripes">
            <div class="container">
                <div class="page-header">
                    <h1>{{Lang::get('cretanrentals::faq.title')}}</h1>
                    <h2>{{Lang::get('cretanrentals::faq.secondary_title')}}</h2>
                </div>
            </div>
        </section>
        <!-- /PAGE TITLE -->
        <!-- PAGE -->
    <section id="faqs" class="page-section">
        <div class="container">
            <div class="row">
                <!-- CONTENT -->
                {{--faq first col--}}
                <div class="col-md-6 content" id="">
                    <!-- col left FAQ items -->
                    <div class="panel-group accordion" id="accordion" role="tablist" aria-multiselectable="true">
                        @for($i=0; $i< ceil(count($faqItems)/2); $i++ )
                            <!-- faq item -->
                                <div class="panel panel-default">
                                    <div class="panel-heading" role="tab" id="heading{{$i}}">
                                        <h4 class="panel-title">
                                            <a class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapse{{$i}}" aria-expanded="false" aria-controls="collapse{{$i}}">
                                                <span class="dot"></span>{!! $faqItems[$i]['q'] !!}
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapse{{$i}}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="heading{{$i}}">
                                        <div class="panel-body">
                                            {!! $faqItems[$i]['a'] !!}
                                        </div>
                                    </div>
                                </div>
                                <!-- /faq item -->
                        @endfor
                    </div>
                </div>
                <!-- /FAQ -->
                {{--faq first col--}}
                <div class="col-md-6 content" id="content">
                    <!-- col right FAQ items -->
                    <div class="panel-group accordion" id="accordion" role="tablist" aria-multiselectable="true">
                        @for($i=ceil(count($faqItems)/2); $i< (count($faqItems)); $i++ )
                        <!-- faq item -->
                            <div class="panel panel-default">
                                <div class="panel-heading" role="tab" id="heading{{$i}}">
                                    <h4 class="panel-title">
                                        <a class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapse{{$i}}" aria-expanded="false" aria-controls="collapse{{$i}}">
                                            <span class="dot"></span>  {!! $faqItems[$i]['q'] !!}
                                        </a>
                                    </h4>
                                </div>
                                <div id="collapse{{$i}}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="heading{{$i}}">
                                    <div class="panel-body">
                                        {!! $faqItems[$i]['a'] !!}
                                    </div>
                                </div>
                            </div>
                            <!-- /faq item -->
                        @endfor
                    </div>
                    <!-- /col right FAQ items -->
                </div>
            </div>
        </div>
    </section>
        <!-- /PAGE -->
@stop