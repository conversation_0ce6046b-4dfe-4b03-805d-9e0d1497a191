@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col text-right mb-3">
                <a href="{{ route('hodor.groups.edit', $group->id) }}" class="btn btn-outline-info">Edit group info</a>
            </div>
        </div>
        <div class="card card-default">
            @include('hodor::common.alert')
            {!! Form::model($group, array('method' => 'PUT', 'files' => false, 'class' => 'main', 'route' => array('hodor.groups.texts.update', $group->id))) !!}
            {!! Form::token() !!}
                <div class="card-body">
                    @include('hodor::groups._texts_form')
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Group Texts
                    </button>
                    <a href="{{ route('hodor.groups.edit', $group->id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Group
                    </a>
                </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection
