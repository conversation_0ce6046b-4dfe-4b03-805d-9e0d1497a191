<div class="row">
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('model', 'Model*') !!}
            {!! Form::text('model',null,['class' => 'form-control' . ($errors->has('model') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('manufacturer', 'Manufacturer*') !!}
            {!! Form::text('manufacturer',null,['class' => 'form-control' . ($errors->has('manufacturer') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('group_id', 'Group') !!}
            {!! Form::select('group_id', $groups, null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('order', 'Order* [0-20]') !!}
            {!! Form::text('order',null,['class' => 'form-control' . ($errors->has('order') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('popular', 'Popular*') !!}
            {!! Form::select('popular', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('group_flagship', 'Group Flagship (fleet booking)') !!}
            {!! Form::select('group_flagship', array('0' => 'No', '1' => 'Yes'), null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-6">
        <div class="form-group">
            {!! Form::label('categories', 'Categories') !!}
            {!! Form::select('categories[]', $categories, null, ['multiple' => true, 'class' => 'form-control custom-select select2']) !!}
        </div>
    </div>
    <div class="col-lg-6">
        <div class="form-group">
            {!! Form::label('motifs', 'Motifs*') !!}
            {!! Form::select('motifs[]', $motifs, null, ['multiple' => true, 'class' => 'form-control custom-select select2']) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-2">
        <div class="form-group">
            <label for="engine">
                <i class="fas fa-car-alt"></i>
                Engine
            </label>
            {!! Form::text('engine',null,['class' => 'form-control' . ($errors->has('engine') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="transmission">
                <i class="fas fa-cogs"></i>
                Transmission
            </label>
            {!! Form::select('transmission', ['manual' => 'Manual', 'automatic' => 'Automatic'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="fuel">
                <i class="fas fa-gas-pump"></i>
                Fuel
            </label>
            {!! Form::select('fuel', ['petrol' => 'Petrol', 'diesel' => 'Diesel'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="consumption">
                <i class="fas fa-burn"></i>
                Consumption
            </label>
            {!! Form::text('consumption',null,['class' => 'form-control' . ($errors->has('consumption') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="four_wheeled">
                <i class="fas fa-truck-monster"></i>
                4x4
            </label>
            {!! Form::select('four_wheeled', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="abs">
                <i class="far fa-hand-paper"></i>
                ABS
            </label>
            {!! Form::select('abs', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-2">
        <div class="form-group">
            <label for="radio_cd">
                <i class="fas fa-music"></i>
                Radio/CD
            </label>
            {!! Form::select('radio_cd', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="clima">
                <i class="fas fa-fan"></i>
                AC/Clima
            </label>
            {!! Form::select('clima', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="sunroof">
                <i class="far fa-sun"></i>
                Sunroof
            </label>
            {!! Form::select('sunroof', ['0' => 'No', '1' => 'Yes'], null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="airbags">
                <i class="fas fa-user-shield"></i>
                Airbags
            </label>
            {!! Form::text('airbags',null,['class' => 'form-control' . ($errors->has('airbags') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-2">
        <div class="form-group">
            <label for="doors">
                <i class="fas fa-door-open"></i>
                Doors
            </label>
            {!! Form::text('doors',null,['class' => 'form-control' . ($errors->has('doors') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="seats">
                <i class="fas fa-user-friends"></i>
                Seats
            </label>
            {!! Form::text('seats',null,['class' => 'form-control' . ($errors->has('seats') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="capacity">
                <i class="fas fa-suitcase"></i>
                Luggage
            </label>
            {!! Form::text('capacity',null,['class' => 'form-control' . ($errors->has('capacity') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
    <div class="col-lg-2">
        <div class="form-group">
            <label for="driver_age">
                <i class="fas fa-user-check"></i>
                Driver Age
            </label>
            {!! Form::text('driver_age',null,['class' => 'form-control' . ($errors->has('driver_age') ? ' is-invalid' : null)]) !!}
        </div>
    </div>
</div>
<hr>
<div class="row">
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('published_eurodollar', 'Published ED*') !!}
            {!! Form::select('published_eurodollar', array('0' => 'No', '1' => 'Yes'), null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
    <div class="col-lg-3">
        <div class="form-group">
            {!! Form::label('published_cretanrentals', 'Published CR*') !!}
            {!! Form::select('published_cretanrentals', array('0' => 'No', '1' => 'Yes'), null, ['class' => 'form-control custom-select']) !!}
        </div>
    </div>
</div>
