@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col text-right mb-3">
                <a href="{{ route('hodor.listings.edit', $listing->id) }}" class="btn btn-outline-info">Edit car info</a>
                <a href="{{ route('hodor.listings.photos.index', $listing->id) }}" class="btn btn-outline-info">Edit car photos</a>
            </div>
        </div>
        <div class="card card-default">
            @include('hodor::common.alert')
            {!! Form::model($listing, array('method' => 'PUT', 'files' => true, 'class' => 'main', 'route' => array('hodor.listings.texts.update', $listing->id))) !!}
            {!! Form::token() !!}
                <div class="card-body">
                    @include('hodor::listings._texts_form')
                </div>
                <div class="card-footer">
                    <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
                </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection