@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Car Details</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::open(array('method' => 'POST', 'class' => 'main', 'route' => array('hodor.listings.store'))) !!}
            {!! Form::token() !!}
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group">
                            {!! Form::label('model', 'Model*') !!}
                            {!! Form::text('model',null,['class' => 'form-control' . ($errors->has('model') ? ' is-invalid' : null)]) !!}
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            {!! Form::label('manufacturer', 'Manufacturer*') !!}
                            {!! Form::text('manufacturer',null,['class' => 'form-control' . ($errors->has('manufacturer') ? ' is-invalid' : null)]) !!}
                        </div>
                    </div>
                    <div class="form-group col-md-4">
                        <label>Group</label>
                        <select class="form-control custom-select" name="group_id">
                            @foreach($groups as $id => $name)
                                <option value="{!! $id !!}">
                                    {!! $name !!}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
            <input type="hidden" value="0" name="published_eurodollar">
            <input type="hidden" value="0" name="published_cretanrentals">
            <div class="card-footer">
                <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection
