<!-- Fleet Utilization by Group -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Utilization by Car Group</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped utilization-table">
                        <thead>
                            <tr>
                                <th>Car Group</th>
                                <th>Fleet Size</th>
                                <th>Rental Days</th>
                                <th>Max Possible</th>
                                <th>Utilization Rate</th>
                                <th>Visual</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($analytics_data['utilization'] as $group => $data)
                                <tr>
                                    <td><strong>{{ $group }}</strong></td>
                                    <td>{{ number_format($data['car_count']) }}</td>
                                    <td>{{ number_format($data['actual_rental_days']) }}</td>
                                    <td>{{ number_format($data['max_possible_days']) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $data['utilization_rate'] >= 80 ? 'success' : ($data['utilization_rate'] >= 60 ? 'warning' : 'danger') }}">
                                            {{ number_format($data['utilization_rate'], 1) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <div class="utilization-bar">
                                            <div class="utilization-fill" 
                                                 style="width: {{ $data['utilization_rate'] }}%; 
                                                        background: {{ $data['utilization_rate'] >= 80 ? '#28a745' : ($data['utilization_rate'] >= 60 ? '#ffc107' : '#dc3545') }};">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Utilization Chart</h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="utilizationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
