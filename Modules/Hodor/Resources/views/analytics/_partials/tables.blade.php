<div class="row">
    @if(isset($analytics_data['tables']['listing_model']))
    <div class="col-12 col-md-6">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-fw fa-list mr-1"></i>Reservations / Listing model <i class="fas fa-fw fa-car ml-1"></i> (based on pickup dates)</h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body bg-white">
                <table class="data-table table table-bordered table-striped"
                       data-paging='true' data-searching='false' data-ordering='true'
                       data-autoWidth='false' data-responsive='true' data-order='[[ 1, "desc" ]]'>
                    <thead>
                    <tr>
                        <th>Listing</th>
                        <th>Reservation count</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($analytics_data['tables']['listing_model'] as $listing_model => $count)
                        <tr>
                            <td>{!! $listing_model !!}</td>
                            <td><a href="{!! route('admin.reservations.index', ['listing_model' => $listing_model, 'show' => 'show', 's_form' => 'Search']) !!}" target="_blank">@number_present($count)</a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if(isset($analytics_data['tables']['listing_group']))
    <div class="col-12 col-md-6">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-fw fa-list mr-1"></i>Reservations / Listing group <i class="fas fa-fw fa-layer-group ml-1"></i> (based on pickup dates)</h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body bg-white">
                <table class="data-table table table-bordered table-striped"
                       data-paging='true' data-searching='false' data-ordering='true'
                       data-autoWidth='false' data-responsive='true' data-order='[[ 1, "desc" ]]'>
                    <thead>
                    <tr>
                        <th>Listing</th>
                        <th>Reservation count</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($analytics_data['tables']['listing_group'] as $listing_group => $count)
                        <tr>
                            <td>{!! \App\Group::where('name', $listing_group)->first() ? \App\Group::where('name', $listing_group)->first()->full_name : $listing_group . ': deleted'!!}</td>
                            <td><a href="{!! route('admin.reservations.index', ['listing_group' => $listing_group, 'show' => 'show', 's_form' => 'Search']) !!}" target="_blank">@number_present($count)</a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if(isset($analytics_data['tables']['listing_model_sum']))
    <div class="col-12 col-md-6">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-fw fa-list mr-1"></i>Revenue / Listing model <i class="fas fa-fw fa-car ml-1"></i> (based on pickup dates)</h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body bg-white">
                @if(count($analytics_data['tables']['listing_model_sum']) <= 30)
                    <div class="card-body bg-white">
                        <canvas id="pieChartListing" style="min-height: 400px; height: 400px; max-height: 400px; max-width: 100%;"></canvas>
                    </div>
                @endif
                <table class="data-table table table-bordered table-striped"
                       data-paging='true' data-searching='false' data-ordering='true'
                       data-autoWidth='false' data-responsive='true' data-order='[[ 1, "desc" ]]'>
                    <thead>
                    <tr>
                        <th>Listing</th>
                        <th>Revenue</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($analytics_data['tables']['listing_model_sum'] as $listing_model => $revenue)
                        <tr>
                            <td>{!! $listing_model !!}</td>
                            <td><a href="{!! route('admin.reservations.index', ['listing_model' => $listing_model, 'show' => 'show', 's_form' => 'Search']) !!}" target="_blank">@money($revenue)</a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if(isset($analytics_data['tables']['listing_group_sum']))
    <div class="col-12 col-md-6">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-fw fa-list mr-1"></i>Revenue / Listing group <i class="fas fa-fw fa-layer-group ml-1"></i> (based on pickup dates)</h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body bg-white">
                @if(count($analytics_data['tables']['listing_group_sum']) <= 30)
                    <div class="card-body bg-white">
                        <canvas id="pieChartGroup" style="min-height: 400px; height: 400px; max-height: 400px; max-width: 100%;"></canvas>
                    </div>
                @endif
                <table class="data-table table table-bordered table-striped"
                       data-paging='true' data-searching='false' data-ordering='true'
                       data-autoWidth='false' data-responsive='true' data-order='[[ 1, "desc" ]]'>
                    <thead>
                    <tr>
                        <th>Listing</th>
                        <th>Revenue</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($analytics_data['tables']['listing_group_sum'] as $listing_group => $revenue)
                        <tr>
                            <td>{!! \App\Group::where('name', $listing_group)->first() ? \App\Group::where('name', $listing_group)->first()->full_name : $listing_group . ': deleted'!!}</td>
                            <td><a href="{!! route('admin.reservations.index', ['listing_group' => $listing_group, 'show' => 'show', 's_form' => 'Search']) !!}" target="_blank">@money($revenue)</a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if(isset($analytics_data['tables']['pickup_location']))
    <div class="col-12 col-md-6">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-fw fa-list mr-1"></i>Reservations / Pickup location <i class="fas fa-fw fa-map-marked-alt ml-1"></i></h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body bg-white">
                <table class="data-table table table-bordered table-striped"
                       data-paging='true' data-searching='false' data-ordering='true'
                       data-autoWidth='false' data-responsive='true' data-order='[[ 1, "desc" ]]'>
                    <thead>
                    <tr>
                        <th>Pickup location</th>
                        <th>Reservation count</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($analytics_data['tables']['pickup_location'] as $location_id => $count)
                        <tr>
                            <td>{!! \App\Location::find($location_id)->name !!}</td>
                            <td><a href="{!! route('admin.reservations.index', ['pickup_location' => $location_id, 'show' => 'show', 's_form' => 'Search']) !!}" target="_blank">@number_present($count)</a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if(isset($analytics_data['tables']['dropoff_location']))
    <div class="col-12 col-md-6">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title"><i class="fas fa-fw fa-list mr-1"></i>Reservations / Dropoff location <i class="fas fa-fw fa-map-marked-alt ml-1"></i></h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body bg-white">
                <table class="data-table table table-bordered table-striped"
                       data-paging='true' data-searching='false' data-ordering='true'
                       data-autoWidth='false' data-responsive='true' data-order='[[ 1, "desc" ]]'>
                    <thead>
                    <tr>
                        <th>Dropoff location</th>
                        <th>Reservation count</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($analytics_data['tables']['dropoff_location'] as $location_id => $count)
                        <tr>
                            <td>{!! \App\Location::find($location_id)->name !!}</td>
                            <td><a href="{!! route('admin.reservations.index', ['dropoff_location' => $location_id, 'show' => 'show', 's_form' => 'Search']) !!}" target="_blank">@number_present($count)</a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
</div>
