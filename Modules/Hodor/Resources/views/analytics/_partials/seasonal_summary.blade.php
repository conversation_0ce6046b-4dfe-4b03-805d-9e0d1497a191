<!-- Seasonal Performance Summary -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Seasonal Performance Summary - {{ $analytics_year }}</h3>
            </div>
            <div class="card-body">
                <!-- Yearly Totals Section -->
                @php
                    $yearlyTotals = [
                        'revenue' => array_sum(array_column($analytics_data['seasons'], 'revenue')),
                        'reservations' => array_sum(array_column($analytics_data['seasons'], 'reservations')),
                        'total_days' => array_sum(array_column($analytics_data['seasons'], 'total_days'))
                    ];
                    $yearlyTotals['avg_revenue_per_day'] = $yearlyTotals['total_days'] > 0 ? $yearlyTotals['revenue'] / $yearlyTotals['total_days'] : 0;
                @endphp

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card bg-primary text-white">
                            <div class="card-header">
                                <h4 class="card-title mb-0"><i class="fas fa-chart-line"></i> Yearly Totals - {{ $analytics_year }}</h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="yearly-total-metric">
                                            <h3>@money($yearlyTotals['revenue'])</h3>
                                            <p>Total Revenue</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="yearly-total-metric">
                                            <h3>{{ number_format($yearlyTotals['reservations']) }}</h3>
                                            <p>Total Reservations</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="yearly-total-metric">
                                            <h3>{{ number_format($yearlyTotals['total_days']) }}</h3>
                                            <p>Total Days</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="yearly-total-metric">
                                            <h3>@money_decimal($yearlyTotals['avg_revenue_per_day'])</h3>
                                            <p>Avg Revenue/Day</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seasonal Breakdown -->
                <div class="row">
                    <div class="col-md-12">
                        <h5 class="mb-3"><i class="fas fa-calendar-alt"></i> Seasonal Breakdown</h5>
                    </div>
                </div>
                <div class="row">
                    @foreach($analytics_data['seasons'] as $season => $data)
                        <div class="col-md-3">
                            <div class="seasonal-metric {{ strtolower($season) }}-season">
                                <h4>{{ $season }}</h4>
                                <h3>@money($data['revenue'])</h3>
                                <p>{{ number_format($data['reservations']) }} Reservations</p>
                                <p>{{ number_format($data['total_days']) }} Total Days</p>
                                <p><strong>@money_decimal($data['avg_revenue_per_day'])/day</strong></p>
                                @php
                                    $seasonPercentage = $yearlyTotals['revenue'] > 0 ? ($data['revenue'] / $yearlyTotals['revenue']) * 100 : 0;
                                @endphp
                                <p class="text-muted"><small>{{ number_format($seasonPercentage, 1) }}% of yearly revenue</small></p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
