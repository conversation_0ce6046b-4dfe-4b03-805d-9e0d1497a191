<ul class="nav nav-tabs mb-3" id="custom-content-below-tab" role="tablist">
    @foreach($languages as $locale => $language)
        <li class="nav-item">
            <a class="nav-link @if($locale === array_key_first($languages)) active @endif" id="tab-{!! $locale !!}-tab" data-toggle="pill" href="#tab-{!! $locale !!}" role="tab" aria-controls="tab-{!! $locale !!}" aria-selected="true">{!! $language !!}</a>
        </li>
    @endforeach
</ul>
<div class="tab-content">
    @foreach($languages as $locale => $language)
        <div class="tab-pane @if($locale === array_key_first($languages)) active show @endif fade" id="tab-{!! $locale !!}" role="tabpanel" aria-labelledby="tab-{!! $locale !!}-tab">
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        {!! Form::label('caption', 'Caption (' . $locale . ')') !!}
                        {!! Form::text($locale . '[caption]', $photo->translate($locale)->caption ?? '',['id' => 'caption-' . $locale . '-' . $photo->id, 'class' => 'form-control' . ($errors->has($locale . 'caption') ? ' is-invalid' : null)]) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $photo, 'field' => 'caption', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        {!! Form::label('alt_text', 'Alt Text (' . $locale . ')') !!}
                        {!! Form::text($locale . '[alt_text]', $photo->translate($locale)->alt_text ?? '', ['id' => 'alt_text-' . $locale . '-' . $photo->id, 'class' => 'form-control' . ($errors->has($locale . 'alt_text') ? ' is-invalid' : null)]) !!}
                        @include('hodor::common._translationSuggestCta', ['model' => $photo, 'field' => 'alt_text', 'locale' => $locale, 'wysiwyg' => 'no'])
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    {!! Form::label('description', 'Description (' . $locale . ')') !!}
                    {!! Form::textarea($locale . '[description]', $photo->translate($locale)->description ?? '', ['id' => 'description-' . $locale . '-' . $photo->id, 'class' => 'form-control' . ($errors->has($locale . 'description') ? ' is-invalid' : null), 'rows' => '4']) !!}
                </div>
            </div>
        </div>
    @endforeach
</div>

@section('css')
    <link rel="stylesheet" href="/vendor/summernote/summernote-bs4.min.css">
    @parent
@stop

@section('js')
    <script src="/vendor/summernote/summernote-bs4.min.js"></script>
    @parent
@stop
