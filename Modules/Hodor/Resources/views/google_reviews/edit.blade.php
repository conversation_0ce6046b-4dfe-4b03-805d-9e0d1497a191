@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col text-right mb-3">
                <a href="{{ route('hodor.google-reviews.texts.edit', $review->id) }}" class="btn btn-outline-info">Edit review texts</a>
            </div>
        </div>
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Review Details</h3>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Author Name</dt>
                    <dd class="col-sm-8">{{ $review->author_title }}</dd>
                    <dt class="col-sm-4">Review Text</dt>
                    <dd class="col-sm-8">{{ $review->review_text }}</dd>
                    <dt class="col-sm-4">Shown Text</dt>
                    <dd class="col-sm-8">{{ $review->content }}</dd>
                    <dt class="col-sm-4">Rating</dt>
                    <dd class="col-sm-8">{{ $review->review_rating }}</dd>
                    <dt class="col-sm-4">Review Datetime</dt>
                    <dd class="col-sm-8">{{ $review->review_datetime }}</dd>
                </dl>
            </div>
        </div>
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Edit</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::model($review, array('method' => 'PUT', 'class' => 'main', 'route' => array('hodor.google-reviews.update', $review->id))) !!}
            {!! Form::token() !!}
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                {!! Form::label('review_text', 'Original Text') !!}
                                <textarea class="form-control" rows="15" disabled>{{ $review->review_text }}</textarea>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                {!! Form::label('translated_text', 'Shown Text') !!}
                                {!! Form::textarea('translated_text',null,['class' => 'form-control' . ($errors->has('translated_text') ? ' is-invalid' : null), 'rows' => '15']) !!}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                {!! Form::label('author_title', 'Author Name') !!}
                                <input class="form-control" disabled value="{{ $review->author_title }}">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                {!! Form::label('author_shown_name', 'Author Shown Name') !!}
                                {!! Form::text('author_shown_name',null,['class' => 'form-control' . ($errors->has('author_shown_name') ? ' is-invalid' : null)]) !!}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {!! Form::label('reservation_id', 'Reservation Id') !!}
                                {!! Form::text('reservation_id',null,['class' => 'form-control' . ($errors->has('reservation_id') ? ' is-invalid' : null)]) !!}
                                @if($errors->has('reservation_id'))<span class="text-danger">{{ $errors->first('reservation_id') }}</span>@endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {!! Form::label('location_id', 'Location Id') !!}
                                {!! Form::select('location_id', $locations, null, ['placeholder' => 'Select a location...', 'class' => 'form-control custom-select']) !!}

                                @if($errors->has('location_id'))<span class="text-danger">{{ $errors->first('location_id') }}</span>@endif
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                {!! Form::label('published', 'Published') !!}
                                {!! Form::select('published', array('0' => 'No', '1' => 'Yes'), null, ['class' => 'form-control custom-select']) !!}
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                {!! Form::label('testimonialised', 'Testimonialised') !!}
                                {!! Form::select('testimonialised', array('0' => 'No', '1' => 'Yes'), null, ['class' => 'form-control custom-select']) !!}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
                </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection
