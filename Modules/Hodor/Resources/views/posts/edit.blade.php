@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col text-right mb-3">
                <a href="{{ route('hodor.posts.photos.index', $post->id) }}" class="btn btn-outline-info">Photo management</a>
                @if($post->published)
                    <a href="{{ route('posts.show', $post->translate()->slug) }}" target="_blank">Preview post</a>
                @else
                    <span>Post is unpublished</span>
                @endif
            </div>
        </div>
        <div class="card card-default">
            @include('hodor::common.alert')
            {!! Form::model($post, array('method' => 'PUT', 'files' => true, 'class' => 'main', 'route' => array('hodor.posts.update', $post->id))) !!}
            {!! Form::token() !!}
            <div class="card-header">
                <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
            </div>
            <div class="card-body">
                @include('hodor::posts._form')
            </div>
            <div class="card-footer">
                <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</section>
@endsection
