@extends('hodor::layouts.master')

@section('content')
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col text-right mb-3">
                <a href="{{ route('hodor.posts.edit', $post->id) }}" class="btn btn-outline-info">Edit post</a>
                @if($post->published)
                    <a href="{{ route('posts.show', $post->slug) }}" target="_blank">Preview post</a>
                @else
                    <span>Post is unpublished</span>
                @endif
            </div>
        </div>
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Upload new photo</h3>
            </div>
            @include('hodor::common.alert')
            {!! Form::model($post, array('method' => 'POST', 'files' => true, 'class' => 'main', 'route' => array('hodor.posts.photos.store', $post->id))) !!}
                {!! Form::token() !!}
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form-group">
                                {!! Form::label('photo', 'Photo (2200x1200) [Less than 20MB size!!!]') !!}
                                <x-adminlte-input-file name="photo"/>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="col-md-4 btn btn-primary">Submit</button>
                    </div>
                    {!! Form::hidden('maxFileSize', $upload_max_filesize, $attributes = ['id' => 'maxFileSize']); !!}
                    {!! Form::hidden('maxFileSizeShort', $upload_max_filesize_short, $attributes = ['id' => 'maxFileSizeShort']); !!}
                    @section('plugins.BsCustomFileInput', true)
                </div>
            {!! Form::close() !!}
        </div>
        <div class="card card-default">
            <div class="card-header">
                <h3 class="card-title">Already uploaded photos</h3>
            </div>
            <div class="card-body">
            @if(!$photos->isEmpty())
                <section class="content">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body table-responsive p-0">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Main</th>
                                                <th>URL</th>
                                                <th>Created</th>
                                                <th></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @foreach ($photos as $photo)
                                                <tr>
                                                    <td><strong><a href="{{ route('hodor.photos.edit', $photo->id) }}">{{ $photo->id }}</a></strong>
                                                        {{ $photo('thumb') }}
                                                    </td>
                                                    <td>
                                                        @if($photo->main)
                                                            <span class="badge bg-success">YES</span>
                                                        @else
                                                            <span class="badge bg-danger">NO</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ asset($photo->getFullUrl()) }}</td>
                                                    <td>{!! $photo->created_at->format('d-m-Y H:i') !!}</td>
                                                    <td class="text-right project-actions">
                                                        {!! Form::open(array('method' => 'PUT', 'class' => '', 'route' => array('hodor.photos.makeMain', $photo->id))) !!}
                                                        {!! Form::token() !!}
                                                        <button class="btn btn-outline-success" title="Make main photo" onclick="return confirm('Are you sure you want to use this as main photo?');"><i class="fas fa-check"></i></button>
                                                        {!! Form::close() !!}
                                                        <br>
                                                        <p>
                                                            <a class="btn btn-outline-info" title="Edit photo captions" href="{{ route('hodor.photos.edit', $photo->id) }}">
                                                                <i class="fas fa-pen"></i>
                                                            </a>
                                                        </p>
                                                        {!! Form::open(array('method' => 'DELETE', 'class' => '', 'route' => array('hodor.photos.destroy', $photo->id))) !!}
                                                        {!! Form::token() !!}
                                                        <button class="btn btn-outline-danger" title="Delete photo" onclick="return confirm('Are you sure you want to delete this item?');"><i class="fas fa-trash"></i></button>
                                                        {!! Form::close() !!}
                                                    </td>
                                                </tr>
                                            @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            @else
                <p>No photos uploaded</p>
            @endif
            </div>
        </div>
    </div>
</section>
@endsection
