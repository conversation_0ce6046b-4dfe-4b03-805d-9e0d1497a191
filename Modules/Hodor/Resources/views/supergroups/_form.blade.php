<div class="row mt-4 mb-4">
    <div class="col-sm-12">
        <h3 class="card-title"><strong>Name:</strong> {{ $supergroup->name ?? '' }}</h3>
        <br>
        <h3 class="card-title"><strong>Slug:</strong> {{ $supergroup->slug ?? '' }}</h3>
    </div>
</div>
<ul class="nav nav-tabs mb-3" id="custom-content-below-tab" role="tablist">
    @foreach($languages as $locale => $language)
        <li class="nav-item">
            <a class="nav-link @if($locale === array_key_first($languages)) active @endif" id="tab-{!! $locale !!}-tab" data-toggle="pill" href="#tab-{!! $locale !!}" role="tab" aria-controls="tab-{!! $locale !!}" aria-selected="true">{!! $language !!}</a>
        </li>
    @endforeach
</ul>
<div class="tab-content">
    @foreach($languages as $locale => $language)
        <div class="tab-pane @if($locale === array_key_first($languages)) active show @endif fade" id="tab-{!! $locale !!}" role="tabpanel" aria-labelledby="tab-{!! $locale !!}-tab">
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        {!! Form::label('title', 'Title (shown in main menu items)*') !!}
                        {!! Form::text($locale . '[title]', $supergroup->translate($locale)->title ?? '',['class' => 'form-control' . ($errors->has('title' . $locale) ? ' is-invalid' : null)]) !!}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        {!! Form::label('page_heading', 'Page Heading (page H1)') !!}
                        {!! Form::text($locale . '[page_heading]', $supergroup->translate($locale)->page_heading ?? '',['class' => 'form-control' . ($errors->has('page_heading' . $locale) ? ' is-invalid' : null)]) !!}
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="page_subheading">
                        Page Subheading
                    </label>
                    {!! Form::textarea($locale . '[page_subheading]', $supergroup->translate($locale)->page_subheading ?? '', ['class' => 'form-control' . ($errors->has($locale . '.page_subheading') ? ' is-invalid' : null), 'rows' => '3']) !!}
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label for="meta_title">
                            Meta Title
                            <code>50-60 chars</code>
                        </label>
                        {!! Form::text($locale . '[meta_title]', $supergroup->translate($locale)->meta_title ?? '', ['class' => 'form-control' . ($errors->has($locale . '.meta_title') ? ' is-invalid' : null), 'id' => 'seo_title']) !!}
                        <p><span id="seo_title_count">{{ strlen($supergroup->meta_title) }}</span> characters</p>
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="meta_description">
                        Meta Description
                        <code>120-158 chars</code>
                    </label>
                    {!! Form::textarea($locale . '[meta_description]', $supergroup->translate($locale)->meta_description ?? '', ['class' => 'form-control' . ($errors->has($locale . '.meta_description') ? ' is-invalid' : null), 'rows' => '3', 'id' => 'seo_description']) !!}
                    <p><span id="seo_description_count">{{ strlen($supergroup->meta_description) }}</span> characters</p>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label for="description">
                        Description
                    </label>
                    {!! Form::textarea($locale . '[description]', $supergroup->translate($locale)->description ?? '', ['class' => 'summernote form-control' . ($errors->has($locale . '.description') ? ' is-invalid' : null), 'rows' => '15']) !!}
                </div>
            </div>
        </div>
    @endforeach
</div>

@section('css')
    <link rel="stylesheet" href="/vendor/summernote/summernote-bs4.min.css">
    @parent
@stop

@section('js')
    <script src="/vendor/summernote/summernote-bs4.min.js"></script>
    <script type="text/javascript">
        $('#seo_description').keyup(function() {
            var characterCountDescr = $(this).val().length,
                current_count_descr = $('#seo_description_count');
            current_count_descr.text(characterCountDescr);
        });

        $('#seo_title').keyup(function() {
            var characterCountTitle = $(this).val().length,
                current_count_title = $('#seo_title_count');
            current_count_title.text(characterCountTitle);
        });
    </script>
    @parent
@stop
