<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;

class ListingUpdateRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'model'                     => 'required',
            'manufacturer'              => 'required',
            'fuel'                      => 'required',
            'transmission'              => 'required',
            'consumption'               => 'required|numeric',
            'group_id'                  => 'required|integer',
            'group_flagship'            => '',
            'order'                     => 'required|integer',
            'engine'                    => 'required|integer',
            'airbags'                   => 'required|integer',
            'doors'                     => 'required|integer',
            'seats'                     => 'required|integer',
            'capacity'                  => 'required|integer',
            'driver_age'                => 'required|integer',
            'published_eurodollar'      => 'required',
            'published_cretanrentals'   => 'required',
            'four_wheeled'              => '',
            'abs'                       => '',
            'radio_cd'                  => '',
            'clima'                     => '',
            'sunroof'                   => '',
            'popular'                   => '',
            'motifs'                    => 'required',
        ];
    }
}
