<?php

namespace Modules\Hodor\Http\Requests;

use App\Http\Requests\Request;

class GoogleReviewSearchRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id'                    => '',
            'review_text'           => '',
            'review_id'             => '',
            'review_rating'         => '',
            'author_title'          => '',
            'author_shown_name'     => '',
            'reservation_id'        => '',
            'location_id'           => '',
            'has_reservation'       => '',
            'has_location'          => '',
            'published'             => '',
            'testimonialised'       => '',
        ];
    }
}
