<?php

namespace Modules\Hodor\Http\Controllers;

use App\Customer;
use App\Services\Search\CustomerSearch;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Hodor\Http\Requests\CustomerSearchRequest;

class CustomersController extends HodorController
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(CustomerSearchRequest $request)
    {
        $customers = CustomerSearch::apply($request);

        $this->view_data['customers']   = $customers;
        $this->view_data['page_title']  = 'Customers';

        return view('hodor::customers.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        return view('hodor::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        //
    }


    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        return view('hodor::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        //
    }
}
