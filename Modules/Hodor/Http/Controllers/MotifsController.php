<?php

namespace Modules\Hodor\Http\Controllers;

use App\Motif;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Modules\Hodor\Http\Requests\MotifRequest;

class MotifsController extends HodorController
{
    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(Request $request)
    {
        $this->view_data['motifs']      = Motif::orderBy('created_at', 'desc')->paginate(20);
        $this->view_data['page_title']  = 'Motifs';

        return view('hodor::motifs.index', $this->view_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        $this->view_data['page_title']  = 'Create New Motif';

        return view('hodor::motifs.create', $this->view_data);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(MotifRequest $request)
    {
        $motif = Motif::create($request->validated());

        return redirect()->route('hodor.motifs.edit',  $motif->id)
            ->withSuccess('Entity with id: ' . $motif->id . ' was successfully saved!');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        $motif = Motif::findOrFail($id);

        $this->view_data['motif']      = $motif;
        $this->view_data['page_title']  = 'Motif: ' . $id;

        return view('hodor::motifs.edit', $this->view_data);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(MotifRequest $request, $id)
    {
        $motif = Motif::findOrFail($id);

        $motif->update($request->validated());

        return redirect()->route('hodor.motifs.edit',  $motif->id)
            ->withSuccess('Entity with id: ' . $motif->id . ' was successfully saved!');
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        $deleteItem =  Motif::find($id);

        if ($deleteItem->delete()) {
            return redirect()->route('hodor.motifs.index')
                ->withSuccess('Entity with id: ' . $id . ' was successfully deleted!');
        } else {
            return redirect()->route('hodor.motifs.index')
                ->with('error', 'An error occured!');
        }

    }
}
