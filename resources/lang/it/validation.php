<?php

return array(

	/*
	|--------------------------------------------------------------------------
	| Validation Language Lines
	|--------------------------------------------------------------------------
	|
	| The following language lines contain the default error messages used by
	| the validator class. Some of these rules have multiple versions such
	| as the size rules. Feel free to tweak each of these messages here.
	|
	*/

	"accepted"             => "L':attributo deve essere accettato.",
	"active_url"           => "L':attributo non è un URL valido.",
	"after"                => "L':attributo deve essere una data dopo :data.",
	"alpha"                => "L':attributo può contenere solo lettere.",
	"alpha_dash"           => "L':attributo può contenere solo lettere, cifre e trattini.",
	"alpha_num"            => "L':attributo può contenere solo lettere e cifre.",
	"array"                => "L':attributo deve essere un array.",
	"before"               => "L':attributo deve essere una data prima di :data.",
	"between"              => array(
		"numeric" => "L':attributo deve essere compreso tra :min e :max.",
		"file"    => "L':attributo deve essere compreso tra :min e :max kb.",
		"string"  => "L':attributo deve essere compreso tra :min e :max caratteri.",
		"array"   => "L':attributo deve avere tra :min e :max articoli.",
	),
	"boolean"              => "Il campo :attributo deve essere vero o falso.",
	"confirmed"            => "La conferma :attributo non corrisponde.",
	"date"                 => "L':attributo non è una data valida.",
	"date_format"          => "L':attributo non corrisponde al formato :formato.",
	"different"            => "L':attributo e l’:altro devono essere diversi.",
	"digits"               => "L':attributo deve essere di :cifre cifre.",
	"digits_between"       => "L':attributo deve essere compreso tra :min e :max cifre.",
	"email"                => "L’:attributo inserito non è valido",
	"exists"               => "L’:attributo selezionato non è valido.",
	"image"                => "L':attributo deve essere un'immagine.",
	"in"                   => "L’:attributo selezionato non è valido.",
	"integer"              => "L':attributo deve essere un numero intero.",
	"ip"                   => "L':attributo deve essere un indirizzo IP valido.",
	"max"                  => array(
		"numeric" => "L':attributo può non essere superiore a :max.",
		"file"    => "L':attributo può non essere superiore a :max kb.",
		"string"  => "L':attributo può non essere superiore a :max caratteri.",
		"array"   => "L':attributo non può avere più di :max articoli.",
	),
	"mimes"                => "L':attributo deve essere un file di tipo: :valori.",
	"min"                  => array(
		"numeric" => "L':attributo deve essere almeno :min.",
		"file"    => "L':attributo deve essere almeno :min kb.",
		"string"  => "L':attributo deve essere almeno :min caratteri.",
		"array"   => "L':attributo deve avere almeno :min elementi.",
	),
	"not_in"               => "L’:attributo selezionato non è valido.",
	"numeric"              => "L':attributo deve essere un numero.",
	"regex"                => "Il formato dell':attributo non è valido.",
	"required"             => "Il campo :attributo è obbligatorio.",
	"required_if"          => "Il campo :attributo è obbligatorio quando :altro è :valore.",
	"required_with"        => "Il campo :attributo è obbligatorio quando :valori è presente.",
	"required_with_all"    => "Il campo :attributo è obbligatorio quando :valori è presente.",
	"required_without"     => "Il campo :attributo è obbligatorio quando :valori non è presente.",
	"required_without_all" => "Il campo :attributo è obbligatorio quando nessuno dei :valori sono presenti.",
	"same"                 => "L':attributo e: altri devono corrispondere.",
	"size"                 => array(
		"numeric" => "L':attributo deve essere di :dimensioni.",
		"file"    => "L':attributo deve essere di :dimensione kb.",
		"string"  => "L':attributo deve essere di :dimensione caratteri.",
		"array"   => "L':attributo deve contenere :dimensione elementi.",
	),
	"unique"               => "L':l'attributo è già stato preso.",
	"url"                  => "Il formato dell':attributo non è valido.",
	"timezone"             => "L':attributo deve essere una zona valida.",

	/*
	|--------------------------------------------------------------------------
	| Custom Validation Language Lines
	|--------------------------------------------------------------------------
	|
	| Here you may specify custom validation messages for attributes using the
	| convention "attribute.rule" to name the lines. This makes it quick to
	| specify a specific custom language line for a given attribute rule.
	|
	*/

	'custom' => array(
		'attribute-name' => array(
			'rule-name' => "messaggio personalizzato",
		),
	),

	/*
	|--------------------------------------------------------------------------
	| Custom Validation Attributes
	|--------------------------------------------------------------------------
	|
	| The following language lines are used to swap attribute place-holders
	| with something more reader friendly such as E-Mail Address instead
	| of "email". This simply helps us make messages a little cleaner.
	|
	*/

	'attributes' => array(
	
	        "pickup_location" => "LUOGO",
	        "pickup_date" => "DATA",
	        "pickup_time" => "ORA",
	        "dropoff_location" => "LUOGO",
	        "dropoff_date" => "DATA",
	        "dropoff_time" => "ORA",
	        "customer_name" => "NOME",
	        "customer_email" => "E-MAIL",
	        "customer_address" => "INDIRIZZO",
	        "customer_country" => "PAESE",
	        "customer_country_id" => "PAESE",
	        "customer_telephone" => "TELEFONO",
	        "email" => "E-MAIL",
	        "name" => "NOME E COGNOME",
	        "phone" => "TELEFONO",
	        "comment" => "MESSAGGIO",
),

);
