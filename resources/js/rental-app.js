import flatpickr from "flatpickr";
import rangePlugin from 'flatpickr/dist/plugins/rangePlugin';
import { German } from 'flatpickr/dist/l10n//de';
import { French } from 'flatpickr/dist/l10n//fr';
import { Greek } from 'flatpickr/dist/l10n//gr';
import { Italian } from 'flatpickr/dist/l10n//it';
import { Russian } from 'flatpickr/dist/l10n//ru';
import { English } from 'flatpickr/dist/l10n//default';
import { range } from "lodash";


// Localisation based on input data-lang attr
var lang = $('#datepicker').data('lang');

switch (lang) {
    case 'el/':
        lang = 'gr';
        break;
    case 'fr/':
        lang = 'fr';
        break;
    case 'it/':
        lang = 'it';
        break;
    case 'de/':
        lang = 'de';
        break;
    case 'ru/':
        lang = 'ru';
        break;

    default:
        lang = 'default';
        break;
}

// Set default expiration to 7 days for all cookies
Cookies.defaults = {
    expires: 14, // Number of days until the cookie expires
    path: '/',
};

/*** 
 * Delete cookies stored in /lang directory. This was created at 17-06-2024 and can be deleted after a while
 ***/
Cookies.set('pickup_date', '01-01-2024', {path: '/en', 'expires': -1})
Cookies.set('pickup_date', '01-01-2024', {path: '/el', 'expires': -1})
Cookies.set('pickup_date', '01-01-2024', {path: '/fr', 'expires': -1})
Cookies.set('pickup_date', '01-01-2024', {path: '/de', 'expires': -1})
Cookies.set('pickup_date', '01-01-2024', {path: '/it', 'expires': -1})
Cookies.set('pickup_date', '01-01-2024', {path: '/ru', 'expires': -1})

Cookies.set('dropoff_date', '01-01-2024', {path: '/en', 'expires': -1})
Cookies.set('dropoff_date', '01-01-2024', {path: '/el', 'expires': -1})
Cookies.set('dropoff_date', '01-01-2024', {path: '/fr', 'expires': -1})
Cookies.set('dropoff_date', '01-01-2024', {path: '/de', 'expires': -1})
Cookies.set('dropoff_date', '01-01-2024', {path: '/it', 'expires': -1})
Cookies.set('dropoff_date', '01-01-2024', {path: '/ru', 'expires': -1})

// Close calendar when clicking outside it, except if clicking container element
$(document).click(function () {
    // if (!$('body').hasClass('booking-page')) {
        var obj = $(".flatpickr-calendar");
        var container = $('.pickup-fields, .dropoff-fields');
        if (!obj.is(event.target) && !obj.has(event.target).length && !container.is(event.target) && !container.has(event.target).length) {
            obj.hide();
        }
    // }

});


// Start with calendar hidden
$(document).ready(function () {
    // If page is not booking page, the calendar initializes as hidden
    // if (!$('body').hasClass('booking-page')) {
        $('.flatpickr-calendar').hide();
    // }
});

// if (!$('body').hasClass('booking-page')) {
    // Toggle datepicker by clicking pickup or dropoff input
    $('#pickup_date, #dropoff_date, #pickup_date_top, #dropoff_date_top').on('click', function () {

        $('.flatpickr-calendar').toggle();
        $('#pickup-fields, #dropoff-fields').hide();
    });

    // When clicking top search bucket, certain styles are applied to calendar
    $('#pickup_date_top, #dropoff_date_top').on('click', function () {
        $('.flatpickr-calendar').appendTo('#blog-booking-box-top').attr('style', 'left: 0 !important; right: 0 !important; width: 618px; top: 0rem !important;');
    });

    // When clicking middle search bucket or index sidebar search bucket, other styles are applied to calendar
    $('#pickup_date, #dropoff_date').on('click', function () {

        if ($('body').hasClass('blog-post')) {
            if (screen.width < 769) {
                var values = 'width: 307px; bottom: unset !important;';
            } else {
                var values = 'width: 618px;bottom: 7rem !important;';
            }
            var styles = 'position: relative !important; top: unset !important;  z-index: 100; left: 0 !important; right: 0 !important; ' + values;
        } else if ($('body').hasClass('blog')) {

            if (screen.width < 769) {
                var values = 'width: 307px;  left: 0 !important;';
            } else {
                var values = 'width: 618px;  left: unset !important;';
            }
            var styles = 'position: absolute; top: unset !important;  bottom: -12rem !important; z-index: 100;  right: 0 !important; width: 619px;' + values;
        }
        $('.flatpickr-calendar').appendTo('#blog-booking-box').attr('style', styles);
    });
// }



// Sets the first date depending on cookies or default
function firstDate() {
    let today = new Date().fp_incr(2).getTime(); // Current date + 2 days in milliseconds

    if (Cookies.get('pickup_date')) {
        let pickupDateStr = Cookies.get('pickup_date');
        let [day, month, year] = pickupDateStr.split('-').map(Number); // Extract day, month, and year
        let pickupDate = new Date(year, month - 1, day).getTime(); // Create Date object (month is 0-indexed)

        if (!isNaN(pickupDate)) {
            if (pickupDate > today) {
                return new Date(pickupDate); // Return pickup date if it's after today
            }
        } else {
            console.error("Invalid date format for 'pickup_date' cookie:", pickupDateStr);
        }
    }

    return new Date(today); // Return today + 2 days if pickup date is not set or is before today
}


// Sets the last date depending on cookies or default
function lastDate() {

    if (Cookies.get('pickup_date')) {
        let pickupDateStr = Cookies.get('pickup_date');
        let [day, month, year] = pickupDateStr.split('-').map(Number); // Extract day, month, and year
        let pickupDate = new Date(year, month - 1, day).getTime(); // Create Date object (month is 0-indexed)

        if (Cookies.get('dropoff_date') == undefined ) {
            var minDropoffDate = new Date(year, month - 1, day).fp_incr(7).getTime();
        } else {
            var minDropoffDate = new Date(pickupDate).fp_incr(3).getTime();
        }

        

        if (Cookies.get('dropoff_date')) {
            let dropoffDateStr = Cookies.get('dropoff_date');
            let [day, month, year] = dropoffDateStr.split('-').map(Number);
            let dropoffDate = new Date(year, month - 1, day).getTime();

            if (dropoffDate <= minDropoffDate) {

                return new Date(minDropoffDate);
            }

            return new Date(dropoffDate);
        }

        return new Date(minDropoffDate);
    } else {
        return new Date().fp_incr(9);
    }
}



function monthsNumber() {

    if (screen.width < 769) {
        return 1;
    } else if (screen.width < 1020) {
        return 2;
    } else {
        return 2;
    }
}


// Flatpicker datepicker
flatpickr('#datepicker', {
    mode: "range",
    inline: true,
    showMonths: monthsNumber(),
    minDate: new Date().fp_incr(2),
    dateFormat: 'd-m-Y',
    defaultDate: [firstDate(), lastDate()],
    locale: lang,

    // Set the pickup and dropoff input values
    onReady: function () {
        $('#pickup_date').val(this.formatDate(firstDate(), "d-m-Y"));
        $('#pickup_date_top').val(this.formatDate(firstDate(), "d-m-Y"));
        $('#dropoff_date').val(this.formatDate(lastDate(), "d-m-Y"));
        $('#dropoff_date_top').val(this.formatDate(lastDate(), "d-m-Y"));
        Cookies.set('pickup_date', this.formatDate(firstDate(), "d-m-Y"));
        Cookies.set('dropoff_date', this.formatDate(lastDate(), "d-m-Y"));
    },

    onChange: function (dateStr, dateObj) {
        if (this.selectedDates.length < 2) {
            // Only one date selected, exit function
            return;
        }
        var start = this.selectedDates[0].getTime();
        var end = this.selectedDates[1].getTime();

        var diffInDays = (end - start) / 86400000;
        var minNewDate = this.formatDate(new Date(start + 259200000), "d-m-Y");

        // If user has selected less than 2 days...
        if (diffInDays < 3) {
            Cookies.set('dropoff_date', minNewDate);

            $('#dropoff_date').val(minNewDate);
            $('#dropoff_date_top').val(minNewDate);

            this.setDate([this.selectedDates[0], minNewDate]);

        }
    },

    onClose: function () {
        $('.flatpickr-calendar').hide();

        // set new values to pickup/dropoff
        // set new cookies values

        var start = this.formatDate(this.selectedDates[0], 'd-m-Y');
        var end = this.formatDate(this.selectedDates[1], 'd-m-Y');



        $('#pickup_date').val(start);
        $('#pickup_date_top').val(start);
        Cookies.set('pickup_date', start);

        $('#dropoff_date').val(end);
        $('#dropoff_date_top').val(end);
        Cookies.set('dropoff_date', end);

        var daysInRange = document.getElementsByClassName('inRange');
        var daysLengthTotal = daysInRange.length + 1;

        $('.total-rent-days').text(daysLengthTotal);

    }

});

// /****** Booking Page ******/

// flatpickr('#pickup_date_booking', {
//     minDate: new Date().fp_incr(2),
//     dateFormat: 'd-m-Y',
//     defaultDate: firstDate(),
//     locale: lang,

//     onChange: function () {

//         var start = this.formatDate(this.selectedDates[0], 'd-m-Y');

//         Cookies.set('pickup_date', start);
//         Cookies.set('dropoff_date', this.formatDate(lastDate(), 'd-m-Y'));

//         $('#dropoff_date_booking').val(this.formatDate(lastDate(), 'd-m-Y'));

//     },
// });

// flatpickr('#dropoff_date_booking', {
//     minDate: new Date(firstDate()).fp_incr(3),
//     dateFormat: 'd-m-Y',
//     defaultDate: lastDate(),
//     locale: lang,

//     onChange: function () {
//         var end = this.formatDate(this.selectedDates[0], 'd-m-Y');

//         Cookies.set('dropoff_date', end);
//     },
// });


// Initialize checkbox value depending on pickup/dropoff locations

$(document).ready(function () {
    var selectedPickupLocation = Cookies.get('pickup_location');
  
    var selectedDropoffLocation = Cookies.get('dropoff_location');
  

    if (selectedPickupLocation != null && selectedDropoffLocation != null && selectedPickupLocation != selectedDropoffLocation) {
        $('#different-dropoff-location').prop('checked', true);
        $('#pickup-label').prop('style', 'display:block;');
        $('#pickup-label-top').prop('style', 'display:block;');
        $('#pickup-dropoff-label').prop('style', 'display: none;');
        $('#pickup-dropoff-label-top').prop('style', 'display: none;');
    } else {
        $('#different-dropoff-location').prop('checked', false);
        $('#pickup-label').hide();
        $('#pickup-label-top').hide();
        $('#pickup-dropoff-label').show();
        $('#pickup-dropoff-label-top').show();
    }
});

/**
 * Show / hide dropoff location. When unchecked, dropoff location gets the same value as pickup 
 * When 'checked' state changes at the middle module, it also changes at the top 
 **/

$('#different-dropoff-location').on('click', function () {
    $('.dropoff-location').toggle('300');
    $('.dropoff-location-top').toggle('300');
    $('#pickup-label').toggle('300');
    $('#pickup-label-top').toggle('300');
    $('#pickup-dropoff-label').toggle('300');
    $('#pickup-dropoff-label-top').toggle('300');

    // if different dropoff location is not checked at the middle, then it is unchecked at the top and vice versa
    if (!$('#different-dropoff-location').is(":checked")) {

        var pickupLocation = $('#pickup_location').val();

        $('#different-dropoff-location-top').prop("checked", false)
        $('#dropoff_location').val(pickupLocation);
        $('#dropoff_location_top').val(pickupLocation);
        Cookies.set('dropoff_location', pickupLocation);

    } else {
        $('#different-dropoff-location-top').prop("checked", true)
    }

});

/**
 * Same as above but for the checkbox at the top
 */
$('#different-dropoff-location-top').on('click', function () {
    $('.dropoff-location').toggle('300');
    $('.dropoff-location-top').toggle('300');
    $('#pickup-label').toggle('300');
    $('#pickup-label-top').toggle('300');
    $('#pickup-dropoff-label').toggle('300');
    $('#pickup-dropoff-label-top').toggle('300');

    // if different dropoff location is not checked at the top, then it is unchecked at the middle and vice versa
    if (!$('#different-dropoff-location-top').is(":checked")) {

        var pickupLocation = $('#pickup_location').val();

        $('#different-dropoff-location').prop("checked", false)
        $('#dropoff_location').val(pickupLocation);
        $('#dropoff_location_top').val(pickupLocation);
        Cookies.set('dropoff_location', pickupLocation);

    } else {
        $('#different-dropoff-location-top').prop("checked", true)
    }

});

/**
 * Next two methods: When pikcup location changes, it changes in both places (top, middle). 
 */
$('#pickup_location').on('change', function () {

    var pickupLocation = $('#pickup_location').val();

    if (!$('#different-dropoff-location').is(":checked")) {


        $('#dropoff_location').val(pickupLocation);
        $('#dropoff_location_top').val(pickupLocation);
        Cookies.set('dropoff_location', pickupLocation);
    }

    $('#pickup_location_top').val(pickupLocation);

});

$('#pickup_location_top').on('change', function () {

    var pickupLocation = $('#pickup_location_top').val();

    if (!$('#different-dropoff-location').is(":checked")) {

        $('#dropoff_location').val(pickupLocation);
        $('#dropoff_location_top').val(pickupLocation);
        Cookies.set('dropoff_location', pickupLocation);
    }

    $('#pickup_location').val(pickupLocation);

});

/**
 * Next two methods: When dropoff location changes, it changes in both places (top, middle). 
 */
$('#dropoff_location').on('change', function () {

    var dropoffLocation = $('#dropoff_location').val();


    $('#dropoff_location_top').val(dropoffLocation);
    Cookies.set('dropoff_location', dropoffLocation);


});

$('#dropoff_location_top').on('change', function () {

    var dropoffLocation = $('#dropoff_location_top').val();


    $('#dropoff_location').val(dropoffLocation);
    Cookies.set('dropoff_location', dropoffLocation);


});

$(window).scroll(function () {
    if ($(this).width() > 769) {

        if ($(this).scrollTop() > 220) {
            $('#blog-booking-box-top').show(300);
        }
        else {
            $('#blog-booking-box-top').hide(300);
        }
    }

});


/********** Search Bucket End *********/

/****** Blog ******/
// Checks if search bucket is in viewport
// var observer = new IntersectionObserver(function (entries) {
//     if (entries[0].isIntersecting === true) {
//         $('.site-header').hide();
//         console.log('Element has just become visible in screen');
//     }
//     if (entries[0].isIntersecting === false){
//         $('.site-header').show();
//         console.log('not');}

// }, { threshold: [1] });

// observer.observe(document.getElementById('blog-booking-box'));

