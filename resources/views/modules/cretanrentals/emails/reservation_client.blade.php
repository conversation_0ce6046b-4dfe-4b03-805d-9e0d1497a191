@extends('cretanrentals::emails.layout')

@section('title') CRETANRENTALS Reservation Details @stop

@section('content')
<strong><font style="font-family: Verdana, Geneva, sans-serif; font-size:20px;">{!! Lang::get('reservation.dear') !!} {!! $customer->name !!},</font></strong>
<br /><br />
<table cellpadding="0" cellspacing="0" border="0">
   <tbody>
      <tr>
         <td>
            <table cellpadding="0" cellspacing="0" border="0" align="center">
               <tbody>
                  <tr>
                     <td>
                        <table cellpadding="0" cellspacing="0" border="0" align="center">
                           <tbody>
                              <tr>
                                 <td>
                                    <table cellpadding="0" cellspacing="0" border="0">
                                       <tbody>
                                           <!-- content -->
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                {!! Lang::get('email.thank_you_cretan') !!}<br>{!! Lang::get('email.reserved_group') !!} <b>Group {!! $group->name !!}</b> {!! Lang::get('reservation.for') !!} <b>{!! $reservation->total_days !!}</b> {!! Lang::get('reservation.days') !!}
                                             </td>
                                          </tr>
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #000;  line-height: 24px;">
                                                <h3>{!! Lang::get('reservation.number') !!} #{!! $reservation->id !!}</h3>
                                             </td>
                                          </tr>
                                       </tbody>
                                    </table>
                                 </td>
                              </tr>
                           </tbody>
                        </table>
                     </td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<br/>
<table cellpadding="0" cellspacing="0" border="0">
   <tbody>
      <tr>
         <td>
            <table cellpadding="0" cellspacing="0" border="0" align="center">
               <tbody>
                  <tr>
                     <td >
                        <table cellpadding="0" cellspacing="0" border="0" align="center">
                           <tbody>
                              <tr>
                                 <td>
                                    <!-- Start of left column -->
                                    <table width="280" align="left" border="0" cellpadding="0" cellspacing="0">
                                       <tbody>
                                          <!-- image -->
                                          <tr>
                                             <td width="280" height="140" align="center">
                                                <img src="{{ $group->getGroupImage() ? asset($group->getGroupImage()) : asset('cretan/img/car-pl-img.png') }}" alt="" border="0" width="280"  style="display:block; border:none; outline:none; text-decoration:none;" class="col2img">
                                             </td>
                                          </tr>
                                          <!-- /image -->
                                       </tbody>
                                    </table>
                                    <!-- end of left column -->
                                    <!-- spacing for mobile devices-->
                                    <table align="left" border="0" cellpadding="0" cellspacing="0">
                                       <tbody>
                                          <tr>
                                             <td height="15" style="font-size:1px; line-height:1px; mso-line-height-rule: exactly;">&nbsp;</td>
                                          </tr>
                                       </tbody>
                                    </table>
                                    <!-- end of for mobile devices-->
                                    <!-- start of right column -->
                                    <table align="left" border="0" cellpadding="0" cellspacing="0">
                                       <tbody>
                                          <tr>
                                             <td>
                                                <table align="center" border="0" cellpadding="10" cellspacing="0">
                                                   <tbody>
                                                      <!-- title -->
                                                      <tr>
                                                         <td style="font-family: Verdana, Geneva, sans-serif; font-size: 18px; color: #282828; text-align:left; line-height: 24px;">
                                                            Group {!! $group->name !!} {!! $group->description !!}<br>
                                                         </td>
                                                      </tr>
                                                      <tr>
                                                         <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766; text-align:left; line-height: 14px;">
                                                            {!! $group->getGroupCarsTitle() !!}
                                                         </td>
                                                      </tr>
                                                      <!-- end of title -->
                                                   </tbody>
                                                </table>
                                             </td>
                                          </tr>
                                       </tbody>
                                    </table>
                                    <!-- end of right column -->
                                 </td>
                              </tr>
                           </tbody>
                        </table>
                     </td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<br />
<table cellpadding="0" cellspacing="0" border="0">
   <tbody>
      <tr>
         <td>
            <table cellpadding="0" cellspacing="0" border="0" align="center">
               <tbody>
                  <tr>
                     <td>
                        <table cellpadding="0" cellspacing="0" border="0" align="center">
                           <tbody>
                              <tr>
                                 <td>
                                    <table cellpadding="0" cellspacing="0" border="0">
                                       <tbody>
                                          <!-- Title -->
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 18px; font-weight:bold; color: #282828;  line-height: 24px;" colspan="2">
                                                {!! Lang::get('email.reservation_details') !!}
                                             </td>
                                          </tr>
                                           <!-- content -->
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                <p style="font-weight:bold">{!! Lang::get('email.pickup') !!} </p>
                                        		<b>{!! Lang::get('email.location') !!}: </b> {!! $reservation->getPickupLocation() !!}
                                                @if(!empty($reservation->getPickupLocationDescription()))
                                                 (<span style="font-style: italic;">{!! $reservation->getPickupLocationDescription() !!}</span>)
                                                @endif
                                                <br>
                                        		<b>{!! Lang::get('email.date') !!}: </b> {!! $reservation->pickup_date->format('d/m/Y') !!}<br>
                                        		<b>{!! Lang::get('email.time') !!}: </b> {!! $reservation->pickup_time !!}
                                             </td>
                                              <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                <p style="font-weight:bold">{!! Lang::get('email.dropoff') !!} </p>
                                        		<b>{!! Lang::get('email.location') !!}: </b> {!! $reservation->getDropoffLocation() !!}
                                                @if(!empty($reservation->getDropoffLocationDescription()))
                                                 (<span style="font-style: italic;">{!! $reservation->getDropoffLocationDescription() !!}</span>)
                                                @endif
                                                <br>
                                        		<b>{!! Lang::get('email.date') !!}: </b> {!! $reservation->dropoff_date->format('d/m/Y') !!}<br>
                                        		<b>{!! Lang::get('email.time') !!}: </b> {!! $reservation->dropoff_time !!}
                                             </td>
                                          </tr>
                                          <!-- End of content -->
                                            <!-- content -->
                                          @if ( ! $reservation->getAccessories()->isEmpty() )
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                <p style="font-weight:bold; padding-top:20px;">{!! Lang::get('reservation.accessories') !!} </p>
                                        		@foreach($reservation->getAccessories() as $accessory)
                                        		<div>
                                        		<b>{!!$accessory->accessory_name !!}: </b>
                                            		@if($accessory->accessory_amount>1)
                                            		x{!!$accessory->accessory_amount!!}
                                            		@endif
                                            		€{!! $accessory->accessory_price * $reservation->total_days !!}
                                            	</div>	
                                        		@endforeach
                                             </td>
                                          </tr>
                                          @endif
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                @if($reservation->comment)
                                                <p style="font-weight:bold; padding-top:20px; ">{!! Lang::get('forms.comments') !!}</p>
                                                {!! $reservation->comment !!}
                                                @endif
                                                @if($reservation->flight_details)
                                                <p style="font-weight:bold; padding-top:20px ">{!! Lang::get('forms.flight') !!}</p>
                                                {!! $reservation->flight_details !!}
                                                @endif
                                             </td>
                                          </tr>
                                       </tbody>
                                    </table>
                                 </td>
                              </tr>
                           </tbody>
                        </table>
                     </td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<br/>
<table cellpadding="0" cellspacing="0" border="0">
   <tbody>
      <tr>
         <td>
            <table cellpadding="0" cellspacing="0" border="0" align="center">
               <tbody>
                  <tr>
                     <td>
                        <table cellpadding="0" cellspacing="0" border="0" align="center">
                           <tbody>
                              <tr>
                                 <td>
                                    <table cellpadding="0" cellspacing="0" border="0">
                                       <tbody>
                                          <!-- Title -->
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 18px; font-weight:bold; color: #282828;  line-height: 24px;">
                                                {!! Lang::get('email.price_details') !!}
                                             </td>
                                          </tr>
                                           <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                <b>{!! Lang::get('email.car_price') !!}:</b> €{!! $reservation->total_price_initial - $reservation->total_discount_amount!!}
                                             </td>
                                          </tr>
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                @if ( $reservation->extra_miles_charge_price!=0 )
                                        	    <b>{!! Lang::get('book.extra_miles') !!}:</b>
                                        	    €{!! $reservation->extra_miles_charge_price !!}
                                        	    @endif
                                        	 </td>
                                         </tr>	    
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                @if ( $reservation->after_hours_charge_price!=0 )
                                        	    <b>{!! Lang::get('book.after_hours') !!}:</b>
                                        	    €{!! $reservation->after_hours_charge_price !!}
                                        	    @endif
                                        	 </td>
                                         </tr>	    
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                @if ( $reservation->fuel_plan_charge_price!=0 )
                                        	    <b>{!! Lang::get('book.fuel_plan') !!}:</b>
                                        	    €{!! $reservation->fuel_plan_charge_price !!}
                                        	    @endif
                                        	 </td>
                                         </tr>
                                          {{--
                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                        	    @if ( $reservation->extra_day_charge==1 )
                                        	    <b>{!! Lang::get('book.extra_day') !!}:</b>
                                        	    {!! $reservation->extra_day_charge !!} {!! Lang::get('reservation.day') !!}
                                        	    @endif
                                             </td>
                                           </tr>
                                          --}}

                                          <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #666766;  line-height: 24px;">
                                                <b>{!! Lang::get('email.total_price') !!}:</b> €{!! $reservation->final_price !!}
                                             </td>
                                           </tr>
                                            <tr>
                                             <td style="font-family: Verdana, Geneva, sans-serif; font-size: 12px; color: #282828;  line-height: 24px;">
                                             	<br/>
                                                {!! Lang::get('reservation.representative') !!}
                                             </td>
                                           </tr>
                                       </tbody>
                                    </table>
                                 </td>
                              </tr>
                           </tbody>
                        </table>
                     </td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
@stop