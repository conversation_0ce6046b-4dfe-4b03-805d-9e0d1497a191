@extends('cretanrentals::layouts.master')


@section('title'){!! Lang::get('cretanrentals::home.page_title') !!}@stop

@section('meta_description'){!! Lang::get('cretanrentals::home.default_meta_description') !!}@stop

@section('head_extra')

@stop


@section('content')
    <!-- SLIDER
================================================== -->
    <section class="page-section no-padding slider" id="homepage-slider-section">
        <div class="container full-width">

            <div class="main-slider">
                <div class="owl-carousel" id="main-slider">

                    <!-- Slide 1 -->
                    <div class="item slide1 ver1">
                        <div class="caption">
                            <div class="container">
                                <div class="div-table">
                                    <div class="div-cell">
                                        <div class="caption-content">
                                            <h2 class="caption-title">{!! Lang::get('cretanrentals::common.slider_subtitle') !!}</h2>
                                            <h1 class="caption-subtitle">{!! Lang::get('cretanrentals::common.slider_title') !!}</h1>
                                            <!-- Search form -->
                                            @include('cretanrentals::home._searchBucket')
                                            <!-- /Search form -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /Slide 1 -->
                </div>
            </div>

        </div>
    </section>
    <!-- /slider -->

    <!-- WHAT WE OFFER
  ================================================== -->
    <section class="page-section" id="what-we-offer">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-offset-0 col-sm-offset-3 col-md-4 col-sm-6 col-xs-12">
                    <div>
                        <header class="service-header"><img src="{!!asset('cretan/img/icons/umbrella.png')!!}" alt="" alt="" style="opacity: 0.4;"> </header>

                        <div class="thumb-caption">
                            <h4 class="service-title">{!! Lang::get('cretanrentals::services.insurance') !!}</h4>
                            <p class="service-desc">{!! Lang::get('cretanrentals::services.insurance_text') !!}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6 col-xs-12">
                    <div class="thumb">
                        <header class="service-header"><img src="{!!asset('cretan/img/icons/airplane.png')!!}" alt="" style="opacity: 0.4;"> </header>

                        <div class="thumb-caption">
                            <h4 class="service-title">{!! Lang::get('cretanrentals::services.airport') !!}</h4>
                            <p class="service-desc">{!! Lang::get('cretanrentals::services.airport_text') !!}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6 col-xs-12">
                    <div class="thumb">
                        <header class="service-header"><i class="fa fa-check" alt="" style="opacity: 0.4; color: #000; font-size: 48px;"></i> </header>
                        <div class="thumb-caption">
                            <h4 class="service-title">{!! Lang::get('cretanrentals::services.inclusive_rates') !!}</h4>
                            <p class="service-desc">{!! Lang::get('cretanrentals::services.inclusive_rates_text') !!}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- /WHAT WE OFFER -->

    <!-- WHO WE ARE
================================================== -->
    <section class="page-section dark who-we-are">
        <div class="container">

            <div class="row">
                <div class="col-md-8">
                    <h2 class="section-title text-left">
                        <small>{!! Lang::get('cretanrentals::home.who_we_are_subtitle') !!}</small>
                        <span>{!! Lang::get('cretanrentals::home.who_we_are_title') !!}</span>
                    </h2>
                    <p class="margin-bottom">{!! Lang::get('cretanrentals::home.who_we_are_text') !!}</p>
                    <p class="btn-row">
                        <a href="{!! LaravelLocalization::getLocalizedURL(App::getLocale(), URL::route('listings.index')) !!}" class="btn btn-theme btn-theme-md">{!! Lang::get('cretanrentals::home.who_we_are_see_cars_btn') !!}</a>
                        <a href="{!! route('booking.index') !!}" class="btn btn-theme btn-theme-md btn-theme-transparent">{!! Lang::get('cretanrentals::home.who_we_are_booking_btn') !!}</a>
                    </p>
                </div>
                <div class="col-md-4">
                    <ul class="">
                        {!! Lang::get('cretanrentals::home.who_we_are_list_items') !!}
                    </ul>
                </div>
            </div>

        </div>
    </section>
    <!-- /WHO WE ARE -->

    <!-- FEATURED
================================================== -->
    @if($featuredListings)
    <section class="page-section">
        <div class="container">

            <h2 class="section-title">
                <small>{!! Lang::get('cretanrentals::home.featured_title') !!}</small>
                <span>{!! Lang::get('cretanrentals::home.featured_subtitle') !!}</span>
            </h2>
            <div class="swiper swiper--offers-best">
                <div class="swiper-container">

                    <div class="swiper-wrapper">
                        <!-- Slides -->
                        @foreach($featuredListings as $featuredListing)
                            <div class="swiper-slide">
                                <div class="thumbnail no-padding thumbnail-car-card">
                                    <div class="media">
                                        @if (!empty($featuredListing->image))
                                            <a class="media-link" href="{!! route('listings.show', $featuredListing->slug) !!}">
                                                <div style="background-image:url('{!! asset($featuredListing->image) !!}')"
                                                     class="post_list_image"></div>
                                            </a>
                                        @else
                                            <div style="background-image:url('{!! asset('cretan/img/car-pl-img.png') !!}')"
                                                 class="post_list_image"></div>
                                        @endif
                                    </div>
                                    <div class="caption text-center">
                                        <h4 class="caption-title">
                                            <a href="{!! route('listings.show', $featuredListing->slug) !!}">
                                                {!! $featuredListing->SEOshortTitle !!}
                                            </a>
                                        </h4>
                                        <div class="caption-text">Group {{$featuredListing->group->name}} ({{$featuredListing->group->description}})</div>
                                        <div class="buttons">
                                            <a class="btn btn-theme" href="{!! route('listings.show', $featuredListing->slug) !!}">
                                                {!! sprintf(Lang::get('cretanrentals::home.featured_rentit_btn_text'),$featuredListing->group->base_price_cretan) !!}
                                            </a>
                                        </div>
                                        <table class="table listing-features">
                                            <tr>
                                                <td>
                                                    <span data-toggle="tooltip" data-placement="right" title="{{Lang::get('cretanrentals::common.seats')}}">
                                                        <i class="fa fa-male"></i> {{$featuredListing->seats}}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span data-toggle="tooltip" data-placement="top" title="{{Lang::get('cretanrentals::common.doors')}}">
                                                        <i class="fa fa-car"></i> {{$featuredListing->doors}}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span data-toggle="tooltip" data-placement="top" title="{{Lang::get('cretanrentals::common.gearbox')}}">
                                                        <i class="fa fa-cogs"></i> {{ucfirst($featuredListing->transmission)}}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span data-toggle="tooltip" data-placement="left" title="{{Lang::get('cretanrentals::common.fuel')}}">
                                                        <i class="fa fa-tint"></i> {{ucfirst($featuredListing->fuel)}}
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <!-- Add Pagination -->
                    <div class="swiper-pagination"></div>
                </div>

                {{--<div class="swiper-button-next"><i class="fa fa-angle-right"></i></div>--}}
                {{--<div class="swiper-button-prev"><i class="fa fa-angle-left"></i></div>--}}

            </div>

        </div>
    </section>
    @endif
    <!-- /FEATURED -->

    <!-- TESTIMONIALS
================================================== -->
    @if($testimonials = Lang::get('cretanrentals::home.testimonials'))
    <section class="page-section testimonials">
        <div class="container">
            <div class="testimonials-carousel">
                <div class="owl-carousel" id="testimonials">
                    @foreach($testimonials as $testimonial)
                    <div class="testimonial">
                        <div class="row">
                            <div class="col-md-10 col-md-offset-1">
                                <div class="testimonial-text">{!! $testimonial['text'] !!}</div>
                                <div class="testimonial-name">{!! $testimonial['initials'] !!} <span class="testimonial-position">{!! $testimonial['country'] !!}</span></div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    @endif
    <!-- /TESTIMONIALS -->

    <!-- SEARCH TEASER
    ================================================== -->
    <section class="page-section find-car">
        <div class="container">
                <div class="row">

                    <div class="col-md-9 col-sm-8">
                        <h2 class="section-title no-margin">

                            <small>{!! Lang::get('cretanrentals::home.search_teaser_title') !!}</small>
                            <span>{!! Lang::get('cretanrentals::home.search_teaser_subtitle') !!}</span>
                        </h2>

                    </div>
                    <div class="col-md-3 col-sm-4">
                            <a href="{!! LaravelLocalization::getLocalizedURL(App::getLocale(), URL::route('listings.index')) !!}" class="btn btn-block btn-theme">{!! Lang::get('cretanrentals::home.search_teaser_button') !!}</a>
                    </div>

                </div>
        </div>
    </section>
    <!-- /SEARCH TEASER -->

    {{--<div style="height: 500px;"></div>--}}
@stop

@section('footer_extra')
<script src="{!! asset('cretan/js/searchBucket.js') !!}"></script>
@stop



