<!-- <PERSON>GE TITLE -->
<section class="page-section breadcrumbs text-right">
    <div class="container">
        <div class="page-header">
            <h1>{{Lang::get('cretanrentals::booking.step4_heading')}}</h1>
            <h2>{{Lang::get('cretanrentals::booking.step4_subheading')}}</h2>
        </div>
    </div>
</section>
<!-- /PAGE TITLE -->
<!-- PAGE WITH SIDEBAR -->
<section class="page-section with-sidebar sub-page" id="step4">
    <div class="container">
        <div class="row">
            <!-- SIDEBAR -->
            <aside class="col-md-4 col-md-push-8 sidebar" id="sidebar">
                <div class="widgets-container sticky-sidebar">
                    <!-- widget detail reservation -->
                    @include('cretanrentals::booking._reservationDetailsWidget')
                <!-- /widget detail reservation -->
                </div>
            </aside>
            <!-- /SIDEBAR -->

            <!-- CONTENT -->
            <div class="col-md-8 col-md-pull-4 content" id="content">
                {!! Form::open([  'class' => 'form-booking', 'id' => 'bookingDataForm']) !!}
                <h3 class="block-title alt">
                    <i class="fa fa-user"></i>
                    {!! Lang::get('cretanrentals::booking.personal_details') !!}
                </h3>
                <div class="row">
                    <div class="col-md-12">
                        <a href="javascript:;" id="repeating_client" data-target="#repeating_modal">{!! Lang::get('cretanrentals::booking.repeating_client_title') !!}</a>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {!! Form::label('customer_name', Lang::get('cretanrentals::forms.name'), ['class' => 'condensed']) !!}
                            {!! Form::text('customer_name', $personal_info['customer_name'], ['class' => 'errorable form-control',  ]) !!}
                            <span id="error_customer_name" class="error"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {!! Form::label('customer_email', Lang::get('cretanrentals::forms.email'), ['class' => 'condensed']) !!}
                            {!! Form::text('customer_email', $personal_info['customer_email'], ['class' => 'errorable form-control', ]) !!}
                            <span id="error_customer_email" class="error"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        {!! Form::label('customer_address', Lang::get('cretanrentals::forms.address'), ['class' => 'condensed']) !!}
                        {!! Form::text('customer_address', $personal_info['customer_address'], ['class' => 'errorable form-control', ]) !!}
                        <span id="error_customer_address" class="error"></span>
                    </div>
                    <div class="col-md-6">
                        {!! Form::label('customer_telephone', Lang::get('cretanrentals::forms.phone'), ['class' => 'condensed']) !!}
                        {!! Form::text('customer_telephone', $personal_info['customer_telephone'], ['class' => 'errorable form-control',]) !!}
                        <span id="error_customer_telephone" class="error"></span>
                    </div>
                    <div class="col-md-6">
                        <div class="selectpicker-wrapper">
                            {!! Form::label('customer_country_id', Lang::get('cretanrentals::forms.country'), ['class' => 'condensed']) !!}
                            {!! Form::select('customer_country_id', $countries, $personal_info['customer_country_id'], ['class' => 'selectpicker errorable', 'data-live-search' => 'true', 'data-size'=>'10' ,'data-width'=>'100%', 'data-container'=>'body']) !!}
                            <span id="error_customer_country_id" class="error"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <h3 class="block-title alt margin-top">
                            <i class="fa fa-comment"></i>
                            {{Lang::get('cretanrentals::booking.comments')}}
                        </h3>
                        <div class="form-group">
                            {!! Form::textarea('comment', $personal_info['comment'], ['class' => "form-control ", 'placeholder' => "Do you have any comments?", 'cols'=>"30", 'rows'=>"5"]) !!}
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <h3 class="block-title alt margin-top">
                            <i class="fa fa-plane"></i>
                            {{Lang::get('cretanrentals::booking.flight_title')}}
                        </h3>
                        <p class="margin-bottom">{!! Lang::get('cretanrentals::booking.flight_number_text') !!}</p>
                        <div class="form-group">
                            {!! Form::text('flight_details', $personal_info['flight_details'], ['class' => 'errorable form-control', 'placeholder' => Lang::get('cretanrentals::forms.flight')]) !!}
                            <span id="error_flight_details" class="error"></span>
                        </div>
                    </div>
                </div>
                {!! Form::hidden('repeatingClient_url', route('repeatingClients.store'), [
                    'id' => 'repeatingClient_url',
                    'data-success-title'   => Lang::get('cretanrentals::booking.success_title'),
                    'data-error-title'     => Lang::get('cretanrentals::booking.error_title') ,
                    'data-success-message' => Lang::get('cretanrentals::forms.repeating_customer_success'),
                    'data-error-message'   => Lang::get('cretanrentals::forms.repeating_customer_error'),
                    'data-prompt-title'    => Lang::get('cretanrentals::forms.repeating_customer'),
                    'data-prompt-text'     => Lang::get('cretanrentals::forms.repeating_customer_sub_title'),
                    'data-prompt-submit'   => Lang::get('cretanrentals::forms.repeating_customer_submit'),
                    'data-prompt-close'    => Lang::get('cretanrentals::forms.repeating_customer_close'),
                    'data-warning-message' => Lang::get('cretanrentals::booking.repeating_client_email_req')])
                 !!}

                {{--the hidden inputs with the rest of reservation data collected from previous steps--}}
                {!! $hiddenInputs !!}

                <ul class="pagination pull-right margin-top">
                    <li><a class="ajaxStep" data-step=3 href="javascript:;" ><i class="fa fa-angle-double-left"></i> {!! Lang::get('cretanrentals::booking.back_to_step_3') !!}</a></li>
                    <li><a class="ajaxStep" data-step=5 href="javascript:;" >{!! Lang::get('cretanrentals::booking.to_step_5') !!} <i class="fa fa-angle-double-right"></i></a></li>
                </ul>
                <div class="clearfix margin-bottom"></div>

            </div>

        </div>
            <!-- /CONTENT -->
        </div>
    </div>
</section>
<!-- /PAGE WITH SIDEBAR -->

    {{--repeating client modal--}}
    <div id="repeating_modal" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title text-center">Are you a repeating customer?</h4>
                </div>
                <div class="modal-body text-center">
                    <div class="form-group ">
                        <input type="text" class="form-control"  aria-describedby="sizing-addon1">
                    </div>
                    <p class="text-center">{!! Lang::get('cretanrentals::forms.repeating_customer_title') !!}</p>
                    {!! Form::button(Lang::get('cretanrentals::forms.repeating_customer_submit'), ['id' => 'repeating_customer_submit', 'class' => 'btn btn-theme margin-top-small']) !!}
                </div>
            </div>
        </div>
    </div>
