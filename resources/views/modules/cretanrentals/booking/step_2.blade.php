@extends('cretanrentals::layouts.master')

@section('title'){{Lang::get('cretanrentals::booking.page_title')}}@stop

@section('meta_description'){{Lang::get('cretanrentals::booking.meta_description')}}@stop

@section('head_extra')
    <link rel="canonical" href="{!! $canonical_url !!}">
@stop


@section('content')

    <!--
================================================== -->
<div id="step-wrapper">
        <!-- PAGE TITLE -->
        <section class="page-section breadcrumbs text-right">
            <div class="container">
                <div class="page-header">
                    <h1>{{Lang::get('cretanrentals::booking.step2_heading')}}</h1>
                    <h2>{{Lang::get('cretanrentals::booking.step2_subheading')}}</h2>
                </div>
            </div>
        </section>
        <!-- /PAGE TITLE -->

    <!-- PAGE WITH SIDEBAR -->
    <section class="page-section with-sidebar sub-page" id="step2">
        <div class="container">
            <div class="row">
                <div class="col-md-12" >
{{--                    <h3 class="margin-top-small">{!! $results_title !!}</h3>--}}
                </div>
                <!-- SIDEBAR -->
                <aside class="col-md-3 col-md-push-9 sidebar" id="sidebar">
                    <div class="widgets-container sticky-sidebar">
                        <!-- widget detail reservation -->
                        @include('cretanrentals::booking._reservationDetailsWidget')
                        <!-- /widget detail reservation -->

                        <?php /*
                        <!-- widget helping center -->
                        <div class="widget shadow widget-helping-center hidden visible-md visible-lg">
                            <h4 class="widget-title">{!! Lang::get('cretanrentals::booking.helping_center_title') !!}</h4>
                            <div class="widget-content">
                                <p>{!! Lang::get('cretanrentals::booking.helping_center_text') !!}</p>
                                <h5 class="widget-title-sub">+90 555 444 66 33</h5>
                                <p><i class="fa fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>
                        <!-- /widget helping center -->
                        */ ?>
                    </div>

                </aside>
                <!-- /SIDEBAR -->
                <!-- CONTENT -->
                <div class="col-md-9 col-md-pull-3 content car-listing" id="content" >
                    <?php $i=0 ?>
                    @foreach($group_items as $group)
                    <?php $i++; ?>
                    @include('cretanrentals::booking._groupListItem')
                    @if($i == 1)
                    <!----- INFO BLOCKS ------->
                    <div class="row blocks booking-info-banners margin-top">
                        <div class="col-sm-6">
                            <div class="block">
                                <div class="media">
                                    <div class="pull-right"><img src="{!!asset('cretan/img/icons/airplane.png')!!}" alt=""></div>
                                    <div class="media-body">
                                        <h4 class="media-heading">{!! trans('cretanrentals::listings.show.teaser_1_title') !!}</h4>
                                        {!! trans('cretanrentals::listings.show.teaser_1_text') !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="block">
                                <div class="media">
                                    <div class="pull-right"><img src="{!!asset('cretan/img/icons/odometer.png')!!}" alt=""></div>
                                    <div class="media-body">
                                        <h4 class="media-heading">{!! trans('cretanrentals::listings.show.teaser_2_title') !!}</h4>
                                        {!! trans('cretanrentals::listings.show.teaser_2_text') !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!----- ./INFO BLOCKS ------->
                    @if($group['selected'])
                    <hr class="page-divider">
                    <h4 class="margin-bottom-small text-muted">{!! Lang::get('cretanrentals::booking.select_other_group_prompt') !!}</h4>
                    @endif
                @endif
                @endforeach
                </div>
                <!-- /CONTENT -->

            </div>
        </div>
        {!! $formToStep3 !!}
    </section>
    <!-- /PAGE WITH SIDEBAR -->
    <div class="modal fade" id="quote-modal" tabindex="-1" role="dialog" aria-labelledby="quote-modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-theme-color">
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title text-white" id="exampleModalLabel">{!! Lang::get('cretanrentals::quotes.unavailable.explain_title') !!}</h4>
                </div>
                <div class="modal-body">
                    <p>{!! Lang::get('cretanrentals::quotes.unavailable.explain_text') !!}</p>
                    <form class="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {!! Form::text('customer_name', null, ['class' => 'errorable form-control', 'placeholder' => Lang::get('cretanrentals::forms.name') ]) !!}
                                    <span id="error_customer_name" class="error"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {!! Form::text('customer_email', null, ['class' => 'errorable form-control', 'placeholder' => Lang::get('cretanrentals::forms.email')]) !!}
                                    <span id="error_customer_email" class="error"></span>
                                </div>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </form>
                    <button id="quote_submit" type="button" class="btn btn-primary pull-right" data-groupid="" data-url="{!! route('getQuote') !!}">{!! Lang::get('cretanrentals::quotes.unavailable.request_quote') !!}</button>
                    <img src="{!! asset('cretan/img/ajax-loader.gif') !!}" alt="" class="ajax-loader pull-right" id="ajax-loader" style="margin-right: 10px; margin-top:10px; display: none;">
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@stop

@section('footer_extra')
    {!! $policy_modal !!}
    <script src="{!! asset('cretan/plugins/loading-overlay/loadingoverlay.min.js') !!}"></script>
    <script src="{!! asset('cretan/plugins/jquery.sticky.min.js') !!}"></script>
    <script src="{!! asset('cretan/js/booking.js') !!}"></script>
@stop

