<div id="policy_modal" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title text-center">{!! Lang::get('cretanrentals::policy.title') !!}</h3>
            </div>
            <div class="modal-body">
                <h3 class="margin-top-small">{!! Lang::get('cretanrentals::policy.insurance.title') !!}</h3>
                <p class="mb-15">{!! Lang::get('cretanrentals::policy.insurance.sub_text') !!}</p>
                @foreach($insuranceItems as $item)
                    <div class="insurance-item">
                        <img class="pull-left" src="{{asset('cretan/img/shield.png')}}" alt="" width="25">
                        <h4>{!! $item['title'] !!}</h4>
                        {{$item['text']}}
                        <div class="clearfix"></div>
                    </div>
                    <hr>
                @endforeach
                <p>{!! Lang::get('cretanrentals::policy.insurance.note') !!}</p>

                <h3 class="margin-bottom">{!! Lang::get('cretanrentals::policy.policy.title') !!}</h3>
                @foreach($policyItems as $item)
                    <div class="policy-item">
                        <h4><i class="fa fa-info-circle text-light-blue margin-right"></i> {!! $item['title'] !!}</h4>
                        {!! $item['text'] !!}
                        <div class="clearfix"></div>
                    </div>
                    <hr>
                @endforeach
                @if(($accidentReportItems))

                    {!! $accidentReportItems !!}
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-theme" data-dismiss="modal">{!! Lang::get('cretanrentals::common.close') !!}</button>
            </div>

        </div>
    </div>
</div>