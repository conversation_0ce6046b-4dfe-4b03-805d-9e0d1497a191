<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
      <title>EURODOLLAR Quote Details</title>
      
      <style type="text/css">
         /* Client-specific Styles */
         div, p, a, li, td { -webkit-text-size-adjust:none; }
         #outlook a {padding:0;} /* Force Outlook to provide a "view in browser" menu link. */
         html{width: 100%; }
         body{width:100% !important; background-color:#eee; -webkit-text-size-adjust:100%; -ms-text-size-adjust:100%; margin:0; padding:0;}
         /* Prevent Webkit and Windows Mobile platforms from changing default font sizes, while not breaking desktop design. */
         .ExternalClass {width:100%;} /* Force Hotmail to display emails at full width */
         .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {line-height: 100%;} /* Force Hotmail to display normal line spacing. */
         #backgroundTable {margin:0; padding:0; width:100% !important; line-height: 100% !important;}
         img {outline:none; text-decoration:none;border:none; -ms-interpolation-mode: bicubic;}
         a img {border:none;}
         .image_fix {display:block;}
         p {margin: 0px 0px !important;}
         table td {border-collapse: collapse;}
         table { border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt; }
         a {color: #33b9ff;text-decoration: none;text-decoration:none!important;}
         /*STYLES*/
         table[class=full] { width: 100%; clear: both; }
         /*IPAD STYLES*/
         @media only screen and (max-width: 640px) {
         a[href^="tel"], a[href^="sms"] {
         text-decoration: none;
         color: #33b9ff; /* or whatever your want */
         pointer-events: none;
         cursor: default;
         }
         .mobile_link a[href^="tel"], .mobile_link a[href^="sms"] {
         text-decoration: default;
         color: #33b9ff !important;
         pointer-events: auto;
         cursor: default;
         }
         table[class=devicewidth] {width: 440px!important;text-align:center!important;}
         table[class=devicewidthinner] {width: 420px!important;}
         img[class=banner] {width: 440px!important;height:220px!important;}
         img[class=col2img] {width: 440px!important;height:220px!important;}
         
         
         }
         /*IPHONE STYLES*/
         @media only screen and (max-width: 480px) {
         a[href^="tel"], a[href^="sms"] {
         text-decoration: none;
         color: #33b9ff; /* or whatever your want */
         pointer-events: none;
         cursor: default;
         }
         .mobile_link a[href^="tel"], .mobile_link a[href^="sms"] {
         text-decoration: default;
         color: #33b9ff !important; 
         pointer-events: auto;
         cursor: default;
         }
         table[class=devicewidth] {width: 280px!important;text-align:center!important;}
         table[class=devicewidthinner] {width: 260px!important;}
         img[class=banner] {width: 280px!important;height:140px!important;}
         img[class=col2img] {width: 280px!important;height:140px!important;}
         
        
         }
      </style>
   </head>
   <body>
<!-- Start of seperator -->
<table width="100%"   cellpadding="0" cellspacing="0" border="0" id="backgroundTable" st-sortable="seperator">
   <tbody>
      <tr>
         <td>
            <table width="600" bgcolor="#32302F" align="center" cellspacing="0" cellpadding="0" border="0" class="devicewidth">
               <tbody>
                  <tr >
                     <td align="center" height="30" style="font-size:12px; line-height:18px;">
                     <span  style="font-family: Helvetica, arial, sans-serif; text-decoration: none; color: #eee">Call Us</span>
        			 <span  style="font-family: Helvetica, arial, sans-serif; text-decoration: none; color: #eacb3c">+0302810281338</span>
        			 <span  style="font-family: Helvetica, arial, sans-serif; text-decoration: none; color: #eee">Email Us</span>
        			 <span ><a href="{!! route('contact') !!}"  style="font-family: Helvetica, arial, sans-serif; text-decoration: none; color: #eacb3c"><EMAIL></a></span>
                     </td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<!-- End of seperator -->  
     
<!-- Start of header -->
<table width="100%"   cellpadding="0" cellspacing="0" border="0" id="backgroundTable" st-sortable="header">
   <tbody>
      <tr>
         <td>
            <table width="600" bgcolor="#fcfcfc" cellpadding="0" cellspacing="0" border="0" align="center" class="devicewidth">
               <tbody>
                  <tr>
                     <td width="100%">
                        <table width="600" bgcolor="#eacb3c" cellpadding="0" cellspacing="0" border="0" align="center" class="devicewidth">
                           <tbody>
                              <tr>
                                 <td>
                                    <!-- logo -->
                                    <table bgcolor="#eacb3c" width="140" align="left" border="0" cellpadding="10" cellspacing="0" class="devicewidth">
                                       <tbody>
                                          <tr>
                                             <td width="140" height="50" align="center">
                                                <div class="imgpop">
                                                   <a target="_blank" href="{!! route('home') !!}">
                                                   <img src="{!! asset('images/logo12.png')!!}" alt="" border="0" width="240"  style="display:block; border:none; outline:none; text-decoration:none;">
                                                   </a>
                                                </div>
                                             </td>
                                          </tr>
                                       </tbody>
                                    </table>
                                    <!-- end of logo -->
                                 </td>
                              </tr>
                           </tbody>
                        </table>
                     </td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<!-- End of Header -->
<!-- Start of seperator -->
<table width="100%"   cellpadding="0" cellspacing="0" border="0" id="backgroundTable" st-sortable="seperator">
   <tbody>
      <tr>
         <td>
            <table width="600" bgcolor="#fcfcfc" align="center" cellspacing="0" cellpadding="0" border="0" class="devicewidth">
               <tbody>
                  <tr>
                     <td align="center"  height="5" style="font-size:1px; line-height:1px;">&nbsp;</td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<!-- End of seperator --> 
<!-- start of Full text -->
<table width="100%"   cellpadding="0" cellspacing="0" border="0" id="backgroundTable" st-sortable="full-text">
   <tbody>
      <tr>
         <td>
            <table width="580" bgcolor="#fcfcfc" cellpadding="10" cellspacing="0" border="0" align="center" class="devicewidthinner">
               <tbody>
                  <tr>
                     <td width="100%">
                        <table   width="580" cellpadding="0" cellspacing="0" border="0" align="center" class="devicewidthinner">
                           <tbody>
                              <tr>
                                 <td>
                                 <!-- Start of seperator -->
                                    <table width="100%" bgcolor="#fcfcfc" cellpadding="0" cellspacing="0" border="0" id="backgroundTable" st-sortable="seperator">
                                       <tbody>
                                          <tr>
                                             <td>
                                                <table width="580" align="center" cellspacing="0" cellpadding="0" border="0" class="devicewidthinner">
                                                   <tbody>
                                                      <tr>
                                                         <td align="center" height="10" style="font-size:1px; line-height:1px;">&nbsp;</td>
                                                      </tr>
                                                   </tbody>
                                                </table>
                                             </td>
                                          </tr>
                                       </tbody>
                                    </table>
                                    <!-- End of seperator --> 
                                    <table width="560"   cellpadding="0" cellspacing="0" border="0" class="devicewidthinner">
                                       <tbody>
                                          <!-- Title -->
                                          <tr>
                                             <td style="font-family: Helvetica, arial, sans-serif; font-size: 20px; font-weight:bold; color: #282828;  line-height: 34px;">
                                                You just had a new unavailability quote request!
                                             </td>
                                          </tr>
                                          <!-- End of Title -->
                                           <!-- content -->
                                          <tr>
                                              <td style="font-family: Helvetica, arial, sans-serif; font-size: 14px; color: #000;  line-height: 24px;">
                                                  <h3>Dates: {!! substr($quote->pickup_date, 0, 10) !!}, {!! $quote->pickup_time !!} - {!! substr($quote->dropoff_date, 0, 10) !!}, {!! $quote->dropoff_time !!}</h3>
                                                  <h3>Locations: {!! $quote->getPickupLocation() !!} - {!! $quote->getDropoffLocation() !!}</h3>
                                                  <h3>Car: {!! $quote->title !!}</h3>
                                                  <h3>Customer name: {!! $customer->name !!}</h3>
                                                  <h3>Customer email: {!! $customer->email !!}</h3>
                                              </td>
                                          </tr>
                                          <tr>
                                              <td style="font-family: Helvetica, arial, sans-serif; font-size: 14px; color: #000;  line-height: 24px; padding-top:30px">
                                                  <b>{!! link_to_route('admin.quotes.edit', 'VISIT QUOTE PAGE', $quote->id )!!}</b>
                                              </td>
                                          </tr>
                                          <!-- End of content -->
                                       </tbody>
                                    </table>
                                 </td>
                              </tr>
                              
                           </tbody>
                        </table>
                     </td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<!-- End of Full Text -->
<!-- Start of seperator -->
<table width="100%"   cellpadding="0" cellspacing="0" border="0" id="backgroundTable" st-sortable="seperator">
   <tbody>
      <tr>
         <td>
            <table width="600" bgcolor="#fcfcfc" align="center" cellspacing="0" cellpadding="0" border="0" class="devicewidth">
               <tbody>
                  <tr>
                     <td align="center"  height="5" style="font-size:1px; line-height:1px;">&nbsp;</td>
                  </tr>
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<!-- End of seperator --> 

<!-- Start of Postfooter -->
<table width="100%"   cellpadding="0" cellspacing="0" border="0" id="backgroundTable" st-sortable="postfooter" >
   <tbody>
      <tr>
         <td>
            <table width="600" bgcolor="#fcfcfc" cellpadding="0" cellspacing="0" border="0" align="center" class="devicewidth">
               <tbody>
                  <!-- Spacing -->
                  <tr>
                     <td width="100%" height="20"></td>
                  </tr>
                  <!-- Spacing -->
                  <tr>
                     <td align="center" valign="middle" style="font-family: Helvetica, arial, sans-serif; font-size: 13px;color: #282828" st-content="preheader">
                         Don't hesitate to contact us for any further information <a href="{!! route('contact') !!}" style="text-decoration: none; color: #eacb3c"> <EMAIL></a>
                     </td>
                  </tr>
                  <!-- Spacing -->
                  <tr>
                     <td width="100%" height="20"></td>
                  </tr>
                  <!-- Spacing -->
               </tbody>
            </table>
         </td>
      </tr>
   </tbody>
</table>
<!-- End of postfooter -->      
   </body>
   </html>