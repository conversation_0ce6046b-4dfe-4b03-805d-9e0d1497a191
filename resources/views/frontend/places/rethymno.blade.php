@extends('frontend.places.base')

@section('title'){!! Lang::get('places/rethymno.page_title') !!}@stop

@section('meta_description'){!! Lang::get('places/rethymno.meta_description') !!}@stop

@section('main_heading'){!! Lang::get('places/rethymno.main_heading') !!}@stop

@section('sub_heading'){!! Lang::get('places/rethymno.sub_heading') !!}@stop

@section('featured_heading'){!! Lang::get('places/rethymno.featured_heading') !!}@stop

@section('breadcrumbs_completion')
    <span>{!! trans('breadcrumbs.locations.rethymno') !!}</span>
@stop

@section('body_place_text')
<section class="section">
    <div class="container">
        <div class="content-with-image" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
            <div class="content-with-image__image-wrapper">
                <img src="{!! asset('images/crete/rethymno_location.jpg') !!}" alt="rethymno info" />
            </div>
            <div class="content-with-image__text-wrapper">
                <h3>{{ trans('places/rethymno.sections.intro_heading') }}</h3>
                <p>{{ trans('places/rethymno.sections.intro_text') }}</p>
            </div>
        </div>
        <div class="content-with-image" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
            <div class="content-with-image__text-wrapper">
                <h3>{{ trans('places/rethymno.sections.meeting_points.heading') }}</h3>
                <p>{{ trans('places/rethymno.sections.meeting_points.text') }}</p>
            </div>
            <div class="content-with-image__image-wrapper">
                <img src="{!! asset('images/crete/meeting-points-general.jpeg') !!}" alt="rethymno meeting points" />
            </div>
        </div>
        <div class="content-with-image" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
            <div class="content-with-image__image-wrapper">
                <img src="{!! asset('images/crete/rethymno_beaches.jpg') !!}" alt="rethymno beaches" />
            </div>
            <div class="content-with-image__text-wrapper">
                <h3>{{ trans('places/rethymno.sections.top_beaches.heading') }}</h3>
                <?php $beaches = trans('places/rethymno.sections.top_beaches.beaches'); ?>
                <h4>{{ $beaches[0]['title'] }}</h4>
                <p>{{ $beaches[0]['text'] }}</p>
                <h4>{{ $beaches[1]['title'] }}</h4>
                <p>{{ $beaches[1]['text'] }}</p>
                <h4>{{ $beaches[2]['title'] }}</h4>
                <p>{{ $beaches[2]['text'] }}</p>
            </div>
        </div>
        <div class="content-with-image" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
            <div class="content-with-image__text-wrapper">
                <h3>{{ trans('places/rethymno.sections.top_places.heading') }}</h3>
                <?php $places = trans('places/rethymno.sections.top_places.places'); ?>
                <h4>{{ $places[0]['title'] }}</h4>
                <p>{{ $places[0]['text'] }}</p>
                <h4>{{ $places[1]['title'] }}</h4>
                <p>{{ $places[1]['text'] }}</p>
                <h4>{{ $places[2]['title'] }}</h4>
                <p>{{ $places[2]['text'] }}</p>
            </div>
            <div class="content-with-image__image-wrapper">
                <img src="{!! asset('images/crete/rethymno_places.jpg') !!}" alt="rethymno places" />
            </div>
        </div>
        <div class="content-with-image" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
            <div class="content-with-image__image-wrapper">
                <img src="{!! asset('images/crete/rethymno_activites.jpg') !!}" alt="rethymno activities" />
            </div>
            <div class="content-with-image__text-wrapper">
                <h3>{{ trans('places/rethymno.sections.activities.heading') }}</h3>
                <p>{!! trans('places/rethymno.sections.activities.text') !!}</p>
            </div>
        </div>
    </div>
</section>
@stop

@section('useful_info')
@stop

@section('location_tips')
<section class="section section-features" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
    <div class="container container-1291">
        <h2>{{ trans('places/rethymno.sections.useful_tips.heading') }}</h2>
        <div class="features-grid">
            <?php $items = trans('places/rethymno.sections.useful_tips.tips'); ?>
            <?php $counter = 1; ?>
            @foreach($items as $item)
                <div class="feature-box">
                    <div class="feature-box__image-wrapper">
                        <img src="{!! asset('images/icons/feature-rethymno-' . $counter . '.svg') !!}" alt="feature-rethymno-{{ $counter }}-icon" class="feature-box__image feature-box__image--height">
                    </div>
                    <h3 class="feature-box__title">{{ $item['title'] }}</h3>
                    <p class="feature-box__description">{{ $item['text'] }}</p>
                </div>
                <?php $counter++; ?>
            @endforeach
        </div>
    </div>
</section>
@stop

@section('location_faq')
    <?php $faq  = trans('faq.localised_homepages.rethymno.faq_1'); ?>
    <?php $faq2 = trans('faq.localised_homepages.rethymno.faq_2'); ?>
    <?php $localised_heading = trans('faq.localised_homepages.rethymno.localised_heading'); ?>
    @include('frontend.partials._faq')
@stop
