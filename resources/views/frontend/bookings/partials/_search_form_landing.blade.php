<div class="top">

    <div class="row-item pickup-location">
        <div class="input-wrapper input-location">
            <label id="pickup-label" for="pickup_location">{{ Lang::get('forms.pickup') }}</label>

            {!! Form::select('pickup_location', $locations_dropdown_grouped, Arr::get($_COOKIE, 'pickup_location', null), [
                'class' => 'clearable sessionable',
                'id' => 'pickup_location',
            ]) !!}
        </div>
    </div>

    <div class="row-item pickup-fields">
        <div class="input-wrapper input-pickup">
            {!! Form::text('pickup_date', Arr::get($_COOKIE, 'pickup_date', $pickup_date), [
                'class' => 'datepicker_from',
                'id' => 'pickup_date',
                'data-pickup' => Arr::get($_COOKIE, 'pickup_date', $pickup_date),
                'readonly',
            ]) !!}
            {!! Form::select('pickup_time', $time_ranges, $pickup_time, [
                'class' => 'sessionable',
                'id' => 'pickup_time',
                'type' => 'time',
            ]) !!}
        </div>
    </div>

    <div class="row-item dropoff-location">
        <div class="input-wrapper input-location">
            <label for="dropoff_location">{!! Lang::get('forms.dropoff') !!}</label>
            {!! Form::select('dropoff_location', $locations_dropdown_grouped, Arr::get($_COOKIE, 'dropoff_location', null), [
                'class' => 'clearable sessionable',
                'id' => 'dropoff_location',
            ]) !!}
        </div>
    </div>

    <div class="row-item dropoff-fields">
        <div class="input-wrapper input-dropoff">
            {!! Form::text('dropoff_date', Arr::get($_COOKIE, 'dropoff_date', $dropoff_date), [
                'class' => 'datepicker_to',
                'id' => 'dropoff_date',
                'readonly',
            ]) !!}

            {!! Form::select('dropoff_time', $time_ranges, $dropoff_time, [
                'class' => 'sessionable',
                'id' => 'dropoff_time',
                'type' => 'time',
            ]) !!}
        </div>
    </div>

    <div class="hidden" id="default-time" data-value="{!! $default_time !!}"></div>

    <div class="search-bucket-cta">
        <button class="btn btn-red button--winona" data-text="{!! trans('bookings.landing.form_cta') !!}">
            <span>{!! trans('bookings.landing.form_cta') !!}</span>
        </button>
    </div>

</div>


<input type="text" id="datepicker" class="sessionable" style="display: none;" data-lang={{ $current_locale }}/>