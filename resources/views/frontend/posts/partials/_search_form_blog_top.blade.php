<div id="blog-booking-box-top" class="blog-search-form-top" style="display: none;">


    <div class="search-bucket">

        <div class="locations">
            <div class="row-item pickup-location">
                <div class="input-wrapper input-location">
                    <label id="pickup-label-top"
                        for="pickup_location_top">{{ Lang::get('forms.pickup_location_full') }}</label>
                    <label id="pickup-dropoff-label-top"
                        for="pickup_location_top">{{ Lang::get('forms.pickup_dropoff_location_combined') }}</label>
                    {!! Form::select('pickup_location', $locations_dropdown_grouped, Arr::get($_COOKIE, 'pickup_location', null), [
                        'class' => 'clearable sessionable',
                        'id' => 'pickup_location_top',
                    ]) !!}
                </div>
                <div class="different-dropoff-location-top">
                    <label for="different-dropoff-location-top">
                        <input type="checkbox" id="different-dropoff-location-top">
                        {{ Lang::get('forms.different_dropoff_location') }}
                    </label>
                </div>
            </div>
            <div class="row-item dropoff-location-top"
                style="{{ isset($_COOKIE['pickup_location']) && isset($_COOKIE['dropoff_location']) && $_COOKIE['dropoff_location'] !== $_COOKIE['pickup_location'] ? '' : 'display: none;' }}">
                <div class="input-wrapper input-location">
                    <label for="dropoff_location_top">{!! Lang::get('forms.dropoff_location_full') !!}</label>
                    {!! Form::select('dropoff_location', $locations_dropdown_grouped, Arr::get($_COOKIE, 'dropoff_location', null), [
                        'class' => 'clearable sessionable',
                        'id' => 'dropoff_location_top',
                    ]) !!}
                </div>
            </div>
        </div>
        {{-- {{ setcookie('pickup_date', '') }} --}}

        <div class="dates">
            <div class="row-item pickup-fields">
                <label for="pickup_date_top">{!! Lang::get('forms.pickup_date_full') !!}</label>
                <div class="input-wrapper input-pickup">
                    {!! Form::text('pickup_date', Arr::get($_COOKIE, 'pickup_date', $pickup_date), [
                        'class' => 'datepicker_from',
                        'id' => 'pickup_date_top',
                        'data-pickup' => Arr::get($_COOKIE, 'pickup_date', $pickup_date),
                        'readonly',
                    ]) !!}
                    {!! Form::select('pickup_time', $time_ranges, $pickup_time, [
                        'class' => 'sessionable',
                        'id' => 'pickup_time',
                        'type' => 'time',
                    ]) !!}
                </div>
            </div>
            <div class="row-item dropoff-fields">
                <label for="dropoff_date_top">{!! Lang::get('forms.dropoff_date_full') !!}</label>
                <div class="input-wrapper input-dropoff">
                    {!! Form::text('dropoff_date', Arr::get($_COOKIE, 'dropoff_date', $dropoff_date), [
                        'class' => 'datepicker_to',
                        'id' => 'dropoff_date_top',
                        'readonly',
                    ]) !!}
                    {!! Form::select('dropoff_time', $time_ranges, $dropoff_time, [
                        'class' => 'sessionable',
                        'id' => 'dropoff_time',
                        'type' => 'time',
                    ]) !!}
                </div>
            </div>
        </div>

        <div class="hidden" id="default-time" data-value="{!! $default_time !!}"></div>

        <div class="search-bucket-cta">
            <button class="btn btn-red button--winona" data-text="{!! Lang::get('forms.find_cars') !!}">
                <span>{!! Lang::get('forms.find_cars') !!}</span>
            </button>
        </div>
    </div>
</div>


