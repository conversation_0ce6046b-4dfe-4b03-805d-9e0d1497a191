<div class="search-form--left">
    <div class="row">
        <div class="col--1">
            <div class="input-wrapper input-location">
                <label class="input-label" for="pickup_location">{{ Lang::get('forms.pickup_location_full') }}</label>
                {!! Form::select(
                    'pickup_location',
                    $locations_dropdown_pickup,
                    Arr::get($_COOKIE, 'pickup_location', null),
                    ['class' => 'select2 clearable sessionable priceable', 'id' => 'pickup_location']
                ) !!}
            </div>
        </div>
        <div class="col--2">
            <div class="input-wrapper input-calendar">
                <label class="input-label" for="pickup_date">{!! Lang::get('forms.pickup_date_full') !!}</label>
                {!! Form::text(
                    'pickup_date',
                    Arr::get($_COOKIE, 'pickup_date', $pickup_date),
                    ['class' => 'sessionable datepicker_from priceable', 'id' => 'pickup_date', 'readonly']
                ) !!}
            </div>
        </div>
        <div class="col--3">
            <div class="input-wrapper input-time">
                <label class="input-label" for="pickup_time">{!! Lang::get('forms.pickup_time_full') !!}</label>
                {!! Form::select(
                    'pickup_time',
                    $time_ranges,
                    $pickup_time,
                    ['class' => 'select2 sessionable priceable', 'id' => 'pickup_time', 'type' => 'time']
                ) !!}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col--1">
            <div class="input-wrapper input-location">
                <label class="input-label" for="dropoff_location">{!! Lang::get('forms.dropoff_location_full') !!}</label>
                {!! Form::select(
                    'dropoff_location',
                    $locations_dropdown_dropoff,
                    Arr::get($_COOKIE, 'dropoff_location', null),
                    ['class' => 'select2 clearable sessionable priceable', 'id' => 'dropoff_location']
                ) !!}
            </div>
        </div>
        <div class="col--2">
            <div class="input-wrapper input-calendar">
                <label class="input-label" for="dropoff_date">{!! Lang::get('forms.dropoff_date_full') !!}</label>
                {!! Form::text('dropoff_date',
                    Arr::get($_COOKIE, 'dropoff_date', $dropoff_date),
                    ['class' => 'sessionable datepicker_to priceable', 'id' => 'dropoff_date', 'readonly']
                ) !!}
            </div>
        </div>
        <div class="col--3">
            <div class="input-wrapper input-time">
                <label class="input-label" for="dropoff_time">{!! Lang::get('forms.dropoff_time_full') !!}</label>
                {!! Form::select(
                    'dropoff_time',
                    $time_ranges,
                    $dropoff_time,
                    ['class' => 'select2 sessionable priceable', 'id' => 'dropoff_time', 'type' => 'time']
                ) !!}
            </div>
        </div>
    </div>
</div>
<div class="search-form--right">
    <ul class="tick-list">
        <li>{!! Lang::get('services.inclusive_rates') !!}</li>
        <li>{!! Lang::get('services.no_credit_card') !!}</li>
        <li>{!! Lang::get('services.no_deposit') !!}</li>
    </ul>
</div>
<div class="hidden" id="default-time" data-value="{!! $default_time !!}"></div>