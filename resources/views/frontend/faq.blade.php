@extends('layouts.frontend.base')

@section('title'){!! Lang::get('faq.page_title') !!}@stop

@section('meta_description'){!! Lang::get('faq.meta_description') !!}@stop

@section('head_extra')
{{-- <link rel="stylesheet" type="text/css" href="{!! asset('css/pages.pages.css') !!}"> --}}
<link rel="canonical" href="{{ $canonical_url }}" />
@stop

@section('body')
<section
        data-aos="fade-up"
        data-aos-duration="1500"
        data-aos-once="true"
        class="section section-hero section-hero--black-text background-image faq"
        style="background: url({!! asset('images/services-new-hero.jpg') !!});">
    <div class="container">
        <div class="hero-txt">
            <h1>{!! Lang::get('faq.title') !!}</h1>
        </div>
    </div>
</section>

<section class="section section-faq no-top-padding">
    <div class="container">
        <div class="breadcrumbs" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
            <a href="{!! route('home') !!}">
                <img src="{!! asset('images/home.svg') !!}" alt="{!! trans('breadcrumbs.home') !!}" /> {!! trans('breadcrumbs.home') !!}
            </a>
            <span>‣</span>
            <span>{!! trans('breadcrumbs.faq') !!}</span>

            {!! $breadcrumbs_schema !!}
        </div>

        <h2 data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">{!! Lang::get('faq.home_heading') !!}</h2>
        <ul class="faq-list">
            <?php $faq = Lang::get('faq.text');  ?>
            @foreach ($faq as $faqItem)
            <li class="faq-list-item" data-aos="fade-up" data-aos-duration="1500" data-aos-once="true">
                <h3 class="question"><span>{!! $faqItem['q'] !!}</span><img src="{!! asset('images/chevron-down.svg') !!}" alt="icon down arrow"></h3>
                <p class="answer">{!! $faqItem['a'] !!}</p>
            </li>
            @endforeach

            {!! $faq_schema !!}
        </ul>
    </div>
</section>
@include('frontend.partials._rental_policy_prompt')
@stop