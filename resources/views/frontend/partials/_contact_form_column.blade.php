<div class="contact-form-flexbox contact-form-flexbox--right">
    <h3>{{ trans('common.contact_us') }}</h3>
    @if ($message = Session::get('success'))
        <div class="alert alert-success alert-block">
            {!! $message !!}
        </div>
    @endif
    {!! Form::open(['route' => 'contact.store', 'class' => 'contact-form', 'id' => 'contactForm', 'method' => 'POST']) !!}
    <div class="input-wrapper">
        <label class="input-label" for="name">{{ trans('contact.your_name') }}</label>
        {!! Form::text('name', old('name'), ['id' => 'name']) !!}
        <span class="error">{!! $errors->first('name') !!}</span>
    </div>
    <div class="input-wrapper">
        <label class="input-label" for="email">{{ trans('contact.your_email') }}</label>
        {!! Form::text('email', old('email'), ['id' => 'email']) !!}
        <span class="error">{!! $errors->first('email') !!}</span>
    </div>
    <div class="input-wrapper">
        <label class="input-label" for="phone">{{ trans('contact.your_phone') }}</label>
        {!! Form::text('phone', old('phone'), ['id' => 'phone']) !!}
        <span class="error">{!! $errors->first('phone') !!}</span>
    </div>
    <div class="input-wrapper">
        <label class="input-label" for="comment">{{ trans('contact.your_comment') }}</label>
        {!! Form::textarea('comment', old('comment'), ['id' => 'message']) !!}
        <span class="error">{!! $errors->first('comment') !!}</span>
    </div>
    <div class="input-wrapper">
        <label class="custom-checkbox-container">
            {!! Lang::get('common.form_terms') !!}
            {!! Form::checkbox('acceptTermsCheckbox', 'true', null, ['id' => 'acceptTermsCheckbox']) !!}
            <span class="custom-checkbox"></span>
        </label>
        <span class="error">{!! $errors->first('acceptTermsCheckbox') !!}</span>
    </div>
    <div class="flex-input-wrapper flex-input-wrapper__mob-col">

        <button
            class="g-recaptcha btn button--winona"
            type="submit"
            data-sitekey="{{ config('recaptcha.site_key') }}"
            data-callback='onSubmit'
            data-action='submit'
            data-text="{{ trans('contact.submit') }}"
        >

            <span>{{ trans('contact.submit') }}</span>
        </button>
    </div>
    <span class="error">{!! $errors->first('g-recaptcha-response') !!}</span>
    <input type="hidden" name="origin" value="{{ $origin }}" />
    {!! Form::close() !!}

</div>

