@extends('layouts.admin.base')

@section('head_extra')
<style>
@media print {
.hide-print {display:none;}
}
</style>
@stop  

@section('footer_js')
    <script src="{!! asset('js/jquery.admin-reservations.js') !!}"></script>
@stop  

@section('body')

<section class="padding stripes">
	<div class="container">
        <div class="desktop-8 columns">
            <h1 class="text condensed">RESERVATION ID: {!! $reservation->id !!}</h1>
            <h2>{!! $reservation->Title !!} - Group: {{ $reservation->listing_group }}</h2>
			<h2 class="text condensed bold margin-top"><span class="badge badge_large badge_site_{!! $reservation->site !!}">{!! $reservations_site !!}</span></h2>
        </div>
	 
	   <div class="desktop-4 text right columns    "  >
			<h3 class="text condensed hide-print"><a href="javascript:window.print()"><i class="fa fa-print"></i></a><br>{!! link_to_route('admin.reservations.index', 'back to reservations', ['filter' => 'pending']) !!}</h3>
		</div>
	</div>	
</section>

<section class="border_site_{!! $reservation->site !!}">
	<div class="container ">
		<div class="desktop-6   padding  columns   "  >
		  <div class="margin-bottom">
			  <h2 class="text condensed">STATUS: </h2>
                {!! Form::model($reservation, ['data-remote', 'method' => 'PUT', 'class' => '', 'route' => ['admin.reservations.update', $reservation->id]]) !!}
                {!! Form::select('status', [
                    'pending' => 'Pending',
                    'scheduled' => 'Scheduled']) !!}
                {!! Form::close() !!}
            </div>    
		  <div class="margin-bottom">
		      <h2 class="text condensed">SHOW: </h2>
                {!! Form::model($reservation, ['data-remote', 'method' => 'PUT', 'class' => '', 'route' => ['admin.reservations.update', $reservation->id]]) !!}
                {!! Form::select('show', [
                    'show' => 'Show',
                    'no show' => 'No show']) !!}
                {!! Form::close() !!}
            </div>    
		    <h2 class="text condensed margin-bottom-small">RESERVATION DETAILS</h2>
        	<h3 class="text condensed  "><b>Dates:</b> {!! $reservation->pickup_date->format('d M Y') !!} {!! substr($reservation->pickup_time, 0, -3) !!} - {!! $reservation->dropoff_date->format('d M Y') !!} {!! substr($reservation->dropoff_time, 0, -3) !!}</h3>
        	<h3 class="text  condensed "><b>From:</b> {!! $reservation->getPickupLocation() !!} - <b>To:</b> {!! $reservation->getDropoffLocation() !!} </h3>
        	 <h2 class="text condensed margin-top margin-bottom-small">CUSTOMER DETAILS</h2>
        	<h3 class="text condensed  "><b>Name:</b> {!! $customer->name  !!}  </h3>
        	<h3 class="text  condensed "><b>email:</b> {!! $customer->email !!}  </h3>
        	<h3 class="text  condensed "><b>Phone:</b> {!! $customer->telephone !!}  </h3>
        	<h3 class="text  condensed "><b>Address:</b> {!! $customer->address !!}  </h3>
        	<h3 class="text  condensed "><b>Country:</b> {!! $customer->getCountry() !!}  </h3>
        	<h3 class="text  condensed "><b>Flight Details:</b> {!! $reservation->flight_details !!}  </h3>
		</div>
		<div class="desktop-6  padding  columns   "  >
		    <h2 class="text condensed margin-bottom-small">PRICING DETAILS</h2>
        	<h3 class="text condensed "><b>Final price:</b> €{!! $reservation->final_price !!} (for {!! $reservation->total_days !!} days) </h3>
        	<h3 class="text condensed "><b>Extra day charge:</b> @if($reservation->extra_day_charge == 1) YES @else NO @endif</h3>
        	<h3 class="text condensed "><b>Dropoff charge:</b> €@if($reservation->extra_miles_charge == 1) {!! $reservation->extra_miles_charge_price !!} @else 0 @endif</h3>
        	<h3 class="text condensed "><b>Remote location charge:</b> €@if($reservation->remote_location_charge == 1) {!! $reservation->remote_location_charge_price !!} @else 0 @endif</h3>
        	<h3 class="text condensed "><b>After hours charge:</b> €@if($reservation->after_hours_charge == 1) {!! $reservation->after_hours_charge_price !!} @else 0 @endif</h3>
        	<h3 class="text condensed "><b>Fuel plan charge:</b> €@if($reservation->fuel_plan_charge == 1) {!! $reservation->fuel_plan_charge_price !!} @else 0 @endif</h3>
        	<br>
            <h3 class="text condensed "><b>Discount coupon:</b> @if( ! empty($reservation->discount_coupon) ) {!! $reservation->discount_coupon !!} @else NO @endif</h3>

        	<br>
            <h3 class="text condensed "><b>From commercial offer:</b> @if( $reservation->from_commercial_offer ) YES @else NO @endif</h3>

        	<h2 class="text condensed margin-top margin-bottom-small">CUSTOMER COMMENTS</h2>
            {!! $reservation->comment !!}
        	<h2 class="text condensed margin-top margin-bottom-small">CUSTOMER REFERENCE</h2>
            @if($reservation->reference){{ trans('forms.reference_options.' . $reservation->reference) }}@endif
        	<h2 class="text condensed margin-top margin-bottom-small">PEOPLE COUNT</h2>
            {!! $reservation->people_count !!}
        	<h2 class="text condensed margin-top margin-bottom-small">FEEDBACK</h2>
            <h4 class="text">
                @if($reservation->feedback)
                    Has feedback {!! link_to_route('admin.feedback.show', '(preview feedback)', $reservation->feedback->id, ['class' => 'text cyan']) !!}
                @else
                    NO feedback yet
                @endif
            </h4>
		</div>
	</div>
</section>

 
<section class=" padding  stripes  ">
	<div class="container ">
		<div class="desktop-12     columns   "  >
		@if(!$reservation->getAccessories()->isEmpty())
			<h2 class="text condensed bold margin-bottom-small">ACCESSORIES</h2> 
		@endif	
        	@foreach($reservation->getAccessories() as $accessory)
        		<h3>{!! $accessory->accessory_name !!} <span class="text bold  cyan">x {!! $accessory->accessory_amount !!}</span></h3>
        		<p>Price per piece: <b>€{!! $accessory->accessory_price !!}</b></p>
        		<hr>
        	@endforeach
		</div>
	</div>
</section> 
	 

@stop