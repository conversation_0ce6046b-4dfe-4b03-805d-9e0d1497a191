@extends('layouts.admin.base')

@section('footer_js')

<script type="text/javascript">
$(document).ready(function() {
	
    $('#filter').change( function() {
    	this.form.submit();
    });
 
 
 
});


</script>
@stop  

@section('body')


<section class=" padding stripes  ">
	<div class="container ">
		<div class="desktop-12    columns   "  >
			<h1 class="text bold condensed">Repeating Clients</h1>
		</div>
		<div class="clear"></div>
		<div class="divider"></div>

		{!! Form::open(array('url' => URL::route('admin.repeatingClients.index'), 'class'=>'main', 'method'=>'GET')) !!}
		<div class="desktop-2 offset-8 columns"  >
			<p>{!! Form::select('siteSelected', $sites, $siteSelected) !!}</p>
		</div>
		<div class="desktop-2 columns"  >
            <button type="submit" class="button button-small text  responsive">{!! Lang::get('forms.find_now') !!}</button>
		</div>
		{!! Form::close() !!}
		<div class="clear"></div>
    </div>
</section>	
<section class="padding">
		  @foreach ($repeatingClients as $repeatingClient)
            <div class="container margin-bottom-small border_site_{!! $repeatingClient->site !!}"> 
            	<div class="bck white">
            		<div class="desktop-5 columns   padding-top-small"  > <span class="text grey">{!! $repeatingClient->created_at->format('d M Y') !!}</span>
            			<h2 class="text condensed  bold ">{!! $repeatingClient->id !!}: <span class="text cyan">{!! $repeatingClient->email !!}</span>
						@if ($repeatingClient->hasCustomer())
            				<span class="text xtiny">( {!! count($repeatingClient->getCustomer()) !!} customers )</span>
						@endif</h2>
						@if (!empty($repeatingClient->notes))
            			<h3 class="padding-top-small">Notes</h3>
            			<h4>{!! $repeatingClient->notes !!}</h4>
						@endif
            		</div>
            		<div class="desktop-7 columns">
            			<div class="padding-top-large box_model">
                            <h4>{!! $repeatingClient->getListingTitle() !!}</h4>
	                    	@if (!empty($repeatingClient->pickup_date ) && !empty($repeatingClient->dropoff_date ))
	            				<h4 class="text   "><b>{!! $repeatingClient->pickup_date->format('d M Y') !!}</b> {!! substr($repeatingClient->pickup_time, 0, -3)  !!} - <b>{!! $repeatingClient->dropoff_date->format('d M Y') !!}</b> {!! substr($repeatingClient->dropoff_time, 0, -3) !!} </h4>
	                        @endif
            				<h4 class="text   ">{!! $repeatingClient->getPickupLocation() !!} - To: {!! $repeatingClient->getDropoffLocation() !!} </h4>
            			</div>
            		</div>
            		<div class="clear"></div>
            		<div class="desktop-5 columns  padding-bottom-small"  > {!! link_to_route('admin.repeatingClients.show', 'SEE REPEATING CLIENT', $repeatingClient->id, array('class'=>'button button-small margin-top-small')) !!} </div>
            		<div class="desktop-7 columns text right   "  > {!! Form::model($repeatingClient, ['method' => 'DELETE', 'route' => ['admin.repeatingClients.destroy', $repeatingClient->id]]) !!}
            			{!! Form::submit('Delete', array('class'=>'button button-tiny button-delete alert-popup')) !!}
            			{!! Form::close() !!} </div>
            		<div class="clear"></div>
            	</div>
            </div>
		      
		@endforeach	 
			
	 
</section>










 
@stop