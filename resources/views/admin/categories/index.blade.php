@extends('layouts.admin.base')

@section('head_extra')
<link rel="stylesheet" type="text/css" href="{!! asset('css/pagination.css') !!}">
@stop

@section('footer_js')

<script type="text/javascript">
$(document).ready(function() {
	
    $('#filter').change( function() {
    	this.form.submit();
    });
 
 
 
});


</script>
@stop  

@section('body')


<section class=" padding stripes  ">
	<div class="container ">
		<div class="desktop-6    columns   "  >
			<h1 class="text bold condensed">Categories</h1>
		</div>
		<div class="desktop-6    columns right text   "  >
			 <a class="button" href="{!! route('admin.categories.create') !!}">Add new category</a>
		</div>
    </div>
</section>	
<section class=" padding   ">
 
		  @foreach ($categories as $category)
            
            <div class="container"> 
            	<div class="bck white margin-bottom-small padding-tiny">
            		<div class="desktop-12 columns padding-bottom-tiny "><span class="text grey">{!! $category->created_at->format('d M Y') !!}</span></div>
            		<div class="clear"></div>
            		<div class="desktop-3 columns"  >
            			<h2 class="text condensed  bold ">{!! $category->name !!}</h2>
            		</div>
            		<div class="desktop-8 columns"  >
            			<div class="padding-top-tiny box_model" >
            				<h4 class="text   ">{!! $category->description !!}</h4>
            			</div>
            		</div>
            		@if(!$category->locked)
            		<div class="clear"></div>
            		<div class="desktop-6 columns  padding-small"  > {!! link_to_route('admin.categories.edit', 'EDIT', $category->id, array('class'=>'button button-tiny margin-top-small')) !!} </div>
            		<div class="desktop-6 columns text right padding-small"  > {!! Form::model($category, ['method' => 'DELETE', 'route' => ['admin.categories.destroy', $category->id]]) !!}
            			{!! Form::submit('Delete', array('class'=>'button button-tiny button-delete alert-popup')) !!}
            			{!! Form::close() !!}
            		</div>
            		@else
            		<div class="desktop-1 columns padding-bottom-tiny text center"><i class="fa fa-lock fa-2x" style="color:#D2A009;"></i></div>
            		@endif
            		<div class="clear"></div>
            	</div>
            </div>
		      
		@endforeach	 
        <div class="text center">
	        {!! $categories->render() !!}
        </div>
			
	 
</section>










 
@stop