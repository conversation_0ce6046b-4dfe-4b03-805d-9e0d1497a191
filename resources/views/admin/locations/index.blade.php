@extends('layouts.admin.base')

@section('body')

<!-- FORM
	================================================== -->
<section class=" padding stripes  ">
	<div class="container ">
		<div class="desktop-6    columns   "  >
			<h1 class="text bold condensed">Locations</h1>
		</div>
		<div class="desktop-6    columns right text   "  >
			 <a class="button" href="{!! route('admin.locations.create') !!}">Add new location</a>
		</div>
    </div>
</section>		
 
<section class=" padding   ">
	<div class="container "> 
		<div class="desktop-12 columns   "  >
		  @foreach ($locations as $location)
			<!-- START SEARCH ITEM -->
			<div class="bck white margin-bottom-small">
				<div>
				  <div class="bd padding-right-large padding-bottom-small padding-left-small padding-small box_model" style="position:relative">
					<h2 class="text condensed  bold ">{!! $location->name !!}</h2>
					<h4 class="text grey ">{!! $location->area !!}</h4>
					<h4 class="text grey ">{!! $location->remote_location ? 'Remote' : 'Not Remote' !!}</h4>
					 {!! link_to_route('admin.locations.edit', 'Edit', $location->id, array('class'=>'button button-small')) !!}
					 
					<div style="position:absolute; bottom:0px; right:0px; text-align:right;">
					    
						{!! Form::model($location, array('method' => 'DELETE', 'route' => array('admin.locations.destroy', $location->id, 'class'=>'button button-tiny'))) !!}
                			{!! Form::submit('Delete', array('class'=>'button button-tiny button-delete  alert-popup')) !!}
                		{!! Form::close() !!}

                		</div>
				  </div>
				</div> 
			</div><!-- END SEARCH ITEM -->
		@endforeach	 
			
		</div>
	</div>
</section>









 
@stop