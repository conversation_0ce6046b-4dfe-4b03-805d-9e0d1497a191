@extends('layouts.admin.base')

@section('head_extra')
<link rel="stylesheet" type="text/css" href="{!! asset('css/pagination.css') !!}">
@stop

@section('footer_js')

<script type="text/javascript">
$(document).ready(function() {
	
    $('#filter').change( function() {
    	this.form.submit();
    });
 
 
 
});


</script>
@stop  

@section('body')


<section class=" padding stripes  ">
	<div class="container ">
		<div class="desktop-12    columns   "  >
			<h1 class="text bold condensed">Customers</h1>
		</div>
		<div class="clear"></div>
		<div class="divider"></div>

		{!! Form::open(array('url' => URL::route('admin.customers.index'), 'class'=>'main', 'method'=>'GET')) !!}
		<div class="desktop-2 offset-8 columns"  >
			<p>{!! Form::select('siteSelected', $sites, $siteSelected) !!}</p>
		</div>
		<div class="desktop-2 columns"  >
            <button type="submit" class="button button-small text  responsive">{!! Lang::get('forms.find_now') !!}</button>
		</div>
		{!! Form::close() !!}
		<div class="clear"></div>
    </div>
</section>

<section class="padding">
	<div class="container ">
		<div class="desktop-12 columns padding-bottom-tiny"  >
			<strong class="text grey pull-left padding-top-small">{{$customers->firstItem()}}-{{$customers->lastItem()}} of {{$customers->total()}} total result<?php echo ($customers->total() === 1 ? '' : 's');?></strong>
			<span class="pull-right pagination-top">
					{!! $customers->appends(array('siteSelected' => $siteSelected))->render() !!}
				</span>
			<div class="clear"></div>
		</div>
	</div>
	@foreach ($customers as $customer)
    	@include('admin.customers.partials.details')
	@endforeach
    <div class="text center">
		{!! $customers->appends(array('siteSelected' => $siteSelected))->render() !!}
	</div>
</section>
@stop