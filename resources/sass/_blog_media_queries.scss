@media only screen and (min-width: 769px) and (max-width: 1020px) {
    #blog-booking-box-top {
        display: none;
    }

    .blog {
        #hero-blog {
            .hero-content {
                .hero-text {
                    flex: 0 0 100%;
                }
                img {
                    display: none;
                }
            }
        }
        #featured {
            .featured-posts {
                .featured-item {
                    flex: 0 0 30%;
                }
            }
        }
    }

    .blog-post {
        .container {
            width: 90%;
        }

        .blog-search-form {
            .search-form-container {
                flex-direction: column;
                margin-left: 0;
                box-shadow: unset;
                align-items: center;
                gap: 1rem;

                img {
                    order: 2;
                    width: 70%;
                    margin-top: 1rem;
                }

                .search-bucket {
                    margin-top: 1rem;
                    padding: 0 1rem;
                }

                .dates {
                    flex-direction: column;
                    margin-top: 1rem;
                }
            }
        }

        #search-form-mobile-cta {
            display: block;
            background-color: $red;
            text-align: center;
            margin: 2rem 0 2rem 0rem;
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            box-shadow: -425px 0 $red;
            border-radius: 4px;
            a {
                padding: 1rem 0;
                display: block;
                color: #fff;
                transition: color 0.4s;
                &:hover {
                    text-decoration: none;
                    color: $yellow;
                }
            }
            &:before {
                content: "";
                background-image: url("../../public/images/hover-car.webp");
                position: absolute;
                left: -165px;
                height: 126px;
                width: 174px;
                background-repeat: no-repeat;
                top: -44px;
                z-index: 1000000;
                background-position: 50% 50%;
                background-size: contain;
            }
        }
    }
}
/* Max width 768px */

@media only screen and (max-width: 768px) {
    #blog-booking-box-top {
        display: none;
    }
    .blog {
        .search-row {
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
            .search-wrapper {
                .search-icon {
                    display: block;
                }
            }
        }

        #hero-blog {
            .hero-content {
                .hero-text {
                    flex: 0 0 100%;
                }
                img {
                    display: none;
                }
            }
        }
        #featured {
            .featured-posts {
                margin: 2rem 0 0;
                padding-bottom: 1rem;
                .featured-item {
                    flex: 0 0 100%;
                }
            }
        }
        #main {
            .main-content {
                flex-wrap: wrap;
                .articles {
                    order: 2;
                    flex: 0 0 100%;
                    margin-top: 2rem;
                    .blog-item {
                        .blog-item-content {
                            flex-wrap: wrap;
                            .item-image {
                                flex: 0 0 100%;
                            }
                        }
                    }
                }
                .sidebar {
                    order: 1;
                    flex: 0 0 100%;
                    .sidebar-content {
                        .flatpickr-calendar.inline {
                            margin-top: 6rem !important;
                            left: 0 !important;
                        }
                    }
                }
            }
        }
    }

    .blog-post {
        margin-top: 0rem;
        .breadcrumbs {
            margin: 0 0 0 1rem;
            justify-content: start;
        }
        .container {
            width: 90%;
            padding: 0.2rem;

            h1 {
                font-size: 2.2rem;
                text-align: left;
                margin: 0.5rem 0;
                line-height: 1.02;
            }
            h2 {
                font-size: 1.7rem;
            }
            p {
                font-size: 1.2rem;
            }

            ul {
                margin: 2rem 0 2rem 2rem;
            }

            .main-content {
                width: unset;
                .publish-info {
                    margin-bottom: 0.3rem;
                }
            }
            .related-posts {
                width: 100%;
                margin-bottom: 0;
                .post-list {
                    h4 {
                        font-size: 1.1rem;
                        a {
                            &::before {
                                margin-right: 0.5rem;
                            }
                        }
                    }
                }
            }

            #search-form-mobile-cta {
                display: block;
                background-color: $red;
                text-align: center;
                margin: 2rem 0 3.5rem 0rem;
                padding-right: 1rem;
                position: -webkit-sticky;
                position: sticky;
                top: 0;
                border-radius: 4px;
                box-shadow: -100px 0 $red;
                a {
                    padding: 0.5rem 0 0.5rem 8rem;
                    color: #fff;
                    transition: color 0.4s;
                    display: block;
                    font-size: 0.9rem;
                    &:hover {
                        text-decoration: none;
                        color: $yellow;
                        background-color: $color-red;
                    }
                }
                &:before {
                    content: "";
                    background-image: url("../../public/images/hover-car.webp");
                    position: absolute;
                    left: -34px;
                    height: 126px;
                    width: 174px;
                    background-repeat: no-repeat;
                    top: -44px;
                    z-index: 1000000;
                    background-position: 50% 50%;
                    background-size: contain;
                }
            }
        }
        .blog-search-form {
            display: block;
            .search-form-container {
                flex-direction: column;
                align-items: center;
                margin-left: 0;
                gap: 0;
                box-shadow: unset;

                img {
                    order: 2;
                    margin-top: 1rem;
                    width: 70%;
                }

                ul {
                    margin: 2rem 0 1rem 0rem;
                    li {
                        font-size: 1.1rem;
                    }
                }

                .search-bucket {
                    margin-top: 1rem;
                    padding: 0 1rem;
                    .locations {
                        flex-direction: column;
                    }

                    .dates {
                        flex-direction: column;
                        margin-top: 1rem;
                    }
                }
            }
        }
        .flatpickr-calendar.inline {
            margin-top: -23rem;
        }
    }
}

/* Width 560px - 1020px */

@media only screen and (min-width: 560px) and (max-width: 1020px) {
    .blog {
        #featured {
            .featured-posts {
                .featured-item {
                    flex: 0 0 45%;
                }
            }
        }
    }
}

/* Width > 1020px */

@media only screen and (min-width: 1020px) and (max-width: 1320px) {
    .blog {
        #featured {
            .featured-posts {
                gap: 1rem;
                justify-content: space-evenly;
                .featured-item {
                    flex: 0 0 18%;
                }
            }
        }
    }
}
