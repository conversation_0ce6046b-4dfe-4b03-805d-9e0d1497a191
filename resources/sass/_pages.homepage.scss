/* Widescreen */

.main input:not([type="checkbox"]):not([type="submit"]), .main textarea, .main select, .main textarea {
	background-color: rgba(255, 255, 255, 0.9);
}
.home_search_left label, .home_search_left a{
	color:#fff;
}
 .home_search_left a:hover{
    color:#3498DB;
 }
 .home_search_left{
 
    background-color:rgba(0,0,0,0.2);
    
}
 
.home_search_right{
 
	background-color:rgba(0,0,0,0.4);
	   color:#eee;
}

.select2-container--default .select2-selection--single {
	background-color: rgba(255, 255, 255, 0.9) !important;
}

@media screen and (min-width: 370px) {
	.home_search #locations-wrapper .select2-container {
		min-width: 270px;
	}
}

.home_search #timepickers-wrapper .select2-container {
	min-width: 160px;
}

@media screen and (min-width: 960px) {
	
    .media .img {display:block; clear:both; }
	#container_where   .media .bd {display:block; clear:both;}
	#container_where .container.desktop-margin {
	    margin-bottom:40px;
	}
	
}

/* Tablet */
@media screen and (min-width: 768px) and (max-width: 959px) {
	 
	#container_where .media .img {display:block; clear:both; margin:0px!important}
	#container_where	.media .bd {display:block; clear:both;}
		
	#container_where .media .img {
	    display: block;
	    clear: both;
	    float:none
	}
}

/* Mobile - Portrait */
@media screen and (max-width: 767px) {
    #container_where .media {
	   margin:10px 0px 20px 0px;
	}
}
/* Mobile - Landscape */
@media screen and (min-width: 480px) and (max-width: 767px) {
}



#slider_container {min-height:520px;}
#slider_title {margin-top:20px}

/* Widescreen */
@media screen and (min-width: 1300px) {
    #slider_container {height:550px;}
    #slider_title {
	   margin-top:40px
	}	
}
	
/* Tablet */
@media screen and (min-width: 768px) and (max-width: 980px) {

    #slider_container {height:700px;}
    #slider_title {margin-top:50px}
	}
	
/* Mobile - Portrait */
@media screen and (max-width: 767px) {
    #slider_container {min-height:1100px;}
    #slider_title {margin-top:40px}
}