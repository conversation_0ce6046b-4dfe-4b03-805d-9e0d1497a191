.offer {
    .class {
        margin-bottom: 2rem;
    }
    .extras-checkbox-container input,
    .extras-checkbox {
        cursor: default;
    }

    .mobile {
        display: none;
    }
}

@media only screen and (max-width: 1024px) {
    .offer.section-car-details .col-lg-12:nth-child(2) {
        display: none;
    }

    .offer {
        .mobile {
            display: block;
            .final-price-wrapper {
                margin-bottom: 1rem;
                .rent-btn {
                    height: unset;
                }
            } 
        }
    }
}
