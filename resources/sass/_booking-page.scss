.booking-fleet-page {
    .container {
        display: flex;
        justify-content: space-between;

        .aside {
            flex-basis: 25%;
            .booking-details {
                position: sticky;
                top: 0;
                padding: 1rem;

                .date-time {
                    position: absolute;
                    margin-left: 25px;
                }

                h3 {
                    margin-bottom: 1rem;
                    color: $color-red;
                }

                .distance {
                    height: 3rem;
                    border-left: 1px dashed;
                    margin-left: 9px;
                    display: block;
                }

                .change-details {
                    display: flex;
                    justify-content: flex-end;
                    margin-top: 2rem;

                    .change-cta {
                        border: 2px solid $color-red;
                        border-radius: 2rem;
                        padding: 0.5rem 1rem;
                        transition: 0.5s;
                        &:hover {
                            background-color: $color-red;
                            color: #fff;
                        }
                    }
                }
            }
        }

        .car-list-container {
            flex-basis: 73%;
            padding-top: 1rem;
            margin-bottom: 2rem;

            .filter-container {
                border-bottom: 1px solid $yellow;
                padding: 1rem;
                display: flex;
                justify-content: flex-end;
                position: relative;
                background-color: #f4f4f4;

                img {
                    width: 22px;
                    margin-left: 7px;
                }

                span, img {
                    cursor: pointer;
                }

                #supergroup-filter {
                    position: absolute;
                    top: 56px;
                    z-index: 100;
                    width: 13rem;
                    background-color: #fff;
                    border: 1px solid $yellow;
                    padding: 1rem;

                    .filter-button {
                        cursor: pointer;

                        span {
                            &::after {
                                content: "";
                                background-color: $yellow;
                                border-radius: 50%;
                                width: 10px;
                            }
                        }
                    }
                    .filter-button.active {
                        &:after {
                            content: "✓";
                            margin-left: 5px;
                            color: #fff;
                            font-weight: bold;
                            border-radius: 50%;
                            background-color: $yellow;
                            height: 1rem;
                            width: 1rem;
                            display: inline-block;
                            font-size: 0.75rem;
                            text-align: center;
                        }
                    }
                }
            }

            .booking-item {
                width: 100%;
                .item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 1rem;
                    border-bottom: 1px solid $color-light-gray;

                    .item-model {
                        position: relative;
                    }

                    h3 {
                        color: $color-black;
                        font-size: 1.4rem;
                    }

                    .info {
                        background: $yellow;
                        width: 0.85rem;
                        height: 0.85rem;
                        display: inline-block;
                        text-align: center;
                        border-radius: 50%;
                        font-size: 0.75rem;
                        line-height: 1;
                    }

                    .car-price {
                        font-weight: bold;
                        font-size: 1.5rem;
                    }
                }
                .item-details {
                    justify-content: unset;
                    padding: 1rem 0;
                    gap: 1rem;

                    .main-photo {
                        img {
                            width: 300px;
                        }
                    }
                    .item-info {
                        padding: 0 1rem;
                        ul {
                            list-style: none;
                            li:not(.action) {
                                font-size: initial;
                                position: relative;
                                &:before {
                                    content: "\2713";
                                    margin-right: 10px;
                                    color: $color-black;
                                    font-weight: bold;
                                    border-radius: 50%;
                                    height: 1rem;
                                    width: 1rem;
                                    display: inline-block;
                                    font-size: 0.75rem;
                                    text-align: center;
                                }
                            }
                            li.prominent {
                                &:before {
                                    background: $yellow;
                                    color: #fff;
                                }
                            }
                            li.action {
                                font-size: initial;
                                color: $color-light-gray;
                                cursor: pointer;
                                text-decoration: underline;
                            }
                        }
                    }
                }

                span.tooltip {
                    background-color: #fff;
                    position: absolute;
                    z-index: 100;
                    left: 1rem;
                    right: -14rem;
                    top: 2rem;
                    box-shadow: -10px 0px 25px -5px #999;
                    padding: 0.5rem;
                    &::after {
                        content: "";
                        width: 1rem;
                        height: 1rem;
                        background-color: #fff;
                        position: absolute;
                        left: 0.5rem;
                        top: -0.25rem;
                        rotate: 45deg;
                    }
                }

                .cta {
                    align-self: end;
                    padding-right: 1rem;
                    .cta-button {
                        a {
                            // width: 5rem;
                        }
                    }
                    .item-extra-bottom {
                        ul {
                            li {
                                font-size: 0.75rem;
                            }
                        }
                    }
                }

                .badges {
                    display: flex;
                    justify-content: center;
                    font-size: 0.5rem;
                    gap: 0.4rem;
                    color: #fff;
                    align-items: center;

                    .badge-item {
                        display: flex;
                        align-items: center;
                        flex-grow: 0;
                        flex-shrink: 0;
                        justify-content: center;
                        padding: 0 0.5rem;

                        img {
                            width: 40px;
                            padding: 0 0.3rem;
                        }
                        span {
                            flex-shrink: 0;
                        }
                    }
                    .offer {
                        background: $background-orange;
                        img {
                            height: 30px;
                        }
                    }
                    .popular {
                        background: $background-indigo;
                    }
                    .eco {
                        background: var(--background-green);
                    }
                }

                .attributes {
                    display: flex;
                    gap: 1rem;
                    justify-content: start;

                    .attribute {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        img {
                            width: 20px;
                        }
                        span {
                            font-size: 75%;
                        }
                    }
                }
            }
        }
    }

    @media only screen and (max-width: 768px) {
        .container {
            display: block;
            .aside {
                .booking-details {
                    padding: 0 1rem;
                }
            }
            .car-list-container {
                .booking-item {
                    .item-header {
                        flex-wrap: wrap;
                        row-gap: 1rem;
                        .item-model {
                            flex-basis: 50%;
                            flex-shrink: 0;
                            h3 {
                                font-size: 1.2rem;
                            }
                        }
                        .attributes {
                            order: 3;
                            flex-basis: 100%;
                            justify-content: center;
                        }
                        .item-price {
                            flex-basis: 50%;
                            flex-shrink: 0;
                            text-align: right;
                            .car-price-container {
                                .car-period {
                                    display: block;
                                }
                            }
                        }
                    }
                    .item-details {
                        flex-direction: column;
                        align-items: center;
                        padding: 0;
                        .main-photo {
                            img {
                                margin: unset;
                                position: relative;
                            }
                        }
                        .cta {
                            align-self: stretch;
                            padding: 0 1rem;
                            .cta-button {
                                // display: flex;
                                // gap: 3rem;
                                a {
                                    padding: 1rem;
                                    font-size: 1.5rem;
                                }
                            }
                            .item-extra-bottom {
                                margin: 0 auto 1rem;
                                ul {
                                    margin: 0;
                                    li {
                                        margin: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
