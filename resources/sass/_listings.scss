.fleet-page {
    // Search bucket
    .section-search-form {
        margin-top: 0;

        .search-form {
            max-width: unset;
            padding: 0.5rem 0;

            .top {
                label {
                    font-size: 0.8rem;
                    padding-bottom: 0;
                    padding-left: 0.75rem;
                }
            }
        }

        .search-bucket-cta {
            margin-top: 1.5rem;
        }
    }
    // Title
    .title-container {
        max-width: 1291px;
        h1 {
            color: $color-red;
            font-size: 2.1rem;
        }
        p {
            font-size: 1.15rem;
        }
    }

    .section-car-listing {
        background-color: $background-light-gray;
    }

    .filters-container {
        border-top: none;
    }

    // Listing page
    .listing-container {
        margin: 1rem 0;

        h4 {
            font-weight: normal;
        }

        .group-container {
            margin: 1rem 0;
            .group-items {
                display: flex;
                flex-wrap: wrap;
                gap: 1rem;
                .listing-item {
                    margin: 1rem 0;
                    background-color: #fff;
                    border: 1px solid $background-light-gray;
                    box-shadow: 1px 2px 2px rgba(237, 180, 23, 0.3);
                    border-radius: 4px;
                    padding: 0.5rem 0.3rem;
                    position: relative;

                    .car-group {
                        font-size: 80%;
                        color: $color-light-gray;
                    }

                    h3 {
                        font-size: 1.4rem;
                    }

                    figure {
                        img {
                            object-fit: contain;
                            height: 165px;
                            max-width: 250px;
                        }
                    }

                    .price {
                        text-align: center;
                        font-weight: bold;
                    }

                    .feature {
                        position: absolute;
                        top: 4.2rem;
                        right: 0;
                        color: $yellow;
                        text-shadow: 0px 1px $color-light-gray;
                        padding: 0.2rem 0.3rem;
                        margin: 0 .3rem;
                        border: 1px solid $yellow;
                        border-radius: 4px;
                        font-size: 80%;
                    }

                    .fuel {
                        text-align: center;
                        padding: 0.3rem 0;
                        background-color: $background-light-gray;
                        border-radius: 15px;
                        margin: 0.5rem 0 1rem;
                    }

                    .attributes {
                        display: flex;
                        gap: 1rem;
                        justify-content: start;

                        .attribute {
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            img {
                                width: 20px;
                            }
                            span {
                                font-size: 75%;
                            }
                        }
                    }

                    .detais-button {
                        margin-top: 1rem;
                        text-align: center;
                        button {
                            font-size: 1rem;
                            padding: 0.5rem 1rem;
                            display: block;
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    @media only screen and (min-width: 769px) and (max-width: 1020px) {
    }
    /* Max width 1020px */

    @media only screen and (max-width: 1020px) {

        .container {
            padding: 0 0 0 20px;
        }

        .section-search-form {
            .search-bucket-cta {
                margin-top: 0;
                ul.tick-list {
                    display: none;
                }
            }
           
            .top {
                .input-wrapper {
                    input, select {
                        height: 40px;
                    }
                }
                .input-pickup, .input-dropoff {
                    &:before {
                        bottom: 8px;
                    }
                }
                .input-location {
                    &:after {
                        bottom: 7px;
                    }
                }
            }
        }

        .listing-container {
            .group-container {
                .group-items {
                    flex-wrap: nowrap;
                    overflow: scroll;

                    .listing-item {
                        width: unset;
                    }
                }
            }
        }
    }
}
