/**
 * Created by kos<PERSON> on 5/1/2017.
 */
var suggestedCarousel = $('#suggested');

jQuery(document).ready(function () {
    // suggested listings carousel
    if (suggestedCarousel.length) {
        suggestedCarousel.on('initialized.owl.carousel', function(e) {
            // hide controls if all items are visible
            if(suggestedCarousel.find('.item').length <= suggestedCarousel.find('.owl-item.active').length){
                $(e.currentTarget).find('.owl-controls').hide();
            }
        });
        suggestedCarousel.owlCarousel({
            autoplay: false,
            loop: false,
            margin: 30,
            dots: false,
            nav: true,
            callbacks: true,
            navText: [
                "<i class='fa fa-angle-left'></i>",
                "<i class='fa fa-angle-right'></i>"
            ],
            responsive: {
                0: {items: 1},
                479: {items: 2},
                768: {items: 2},
                991: {items: 3},
                1024: {items: 3},
                1280: {items: 5}
            }
        });
    }


    $('.selectpicker.categories').on('change', function(){
        var selected = $(this).find("option:selected").val();
        if (selected != 'all') {
            window.location = $('#url').html() + "?group=" + selected;
        } else {
            window.location = $('#url').html();
        }
    });

});

