'use strict';

// Cache
var body = $('body');
var mainSlider = $('#main-slider');
var testimonialsCarousel = $('#testimonials');
var owlCarouselSelector = $('.owl-carousel');
var toTop = $('#to-top');
var hover = $('.thumbnail');
var navigation = $('.navigation');
var superfishMenu = $('ul.sf-menu');
var swiperOffersBest = $('.swiper--offers-best .swiper-container');

// Slide in/out with fade animation function
jQuery.fn.slideFadeToggle = function (speed, easing, callback) {
	return this.animate({opacity: 'toggle', height: 'toggle'}, speed, easing, callback);
};
//
jQuery.fn.slideFadeIn = function (speed, easing, callback) {
	return this.animate({opacity: 'show', height: 'show'}, speed, easing, callback);
};
jQuery.fn.slideFadeOut = function (speed, easing, callback) {
	return this.animate({opacity: 'hide', height: 'hide'}, speed, easing, callback);
};

jQuery(document).ready(function () {
	// Prevent empty links
	// ---------------------------------------------------------------------------------------
	$('a[href=#]').on('click', function (event) {
		event.preventDefault();
	});
	// Sticky header/menu
	// ---------------------------------------------------------------------------------------
	if ($().sticky) {
		$('.header.fixed').sticky({topSpacing: 0});
		//$('.header.fixed').on('sticky-start', function() { console.log("Started"); });
		//$('.header.fixed').on('sticky-end', function() { console.log("Ended"); });
	}
	// SuperFish menu
	// ---------------------------------------------------------------------------------------
	if ($().superfish) {
		superfishMenu.superfish();
	}
	// $('ul.sf-menu a').on('click', function () {
	//     body.scrollspy('refresh');
	// });
	// Fixed menu toggle
	$('.menu-toggle').on('click', function () {
		if (navigation.hasClass('opened')) {
			navigation.removeClass('opened').addClass('closed');
		} else {
			navigation.removeClass('closed').addClass('opened');
		}
	});
	$('.menu-toggle-close').on('click', function () {
		if (navigation.hasClass('opened')) {
			navigation.removeClass('opened').addClass('closed');
		} else {
			navigation.removeClass('closed').addClass('opened');
		}
	});
	//
	if ($('.content-area.scroll').length) {
		$('.open-close-area').on('click', function () {
			if ($('.wrapper').hasClass('opened')) {
				$('.wrapper').removeClass('opened').addClass('closed');
			} else {
				$('.wrapper').removeClass('closed').addClass('opened');
			}
		});
	}
	// Smooth scrolling
	// ----------------------------------------------------------------------------------------
	// $('.scroll-to').on('click', function () {
	//     $('html, body').animate({
	//         scrollTop: $($(this).attr('href')).offset().top - 43
	//     }, {
	//         duration: 1200,
	//         easing: 'easeInOutExpo'
	//     });
	//     return false;
	// });
	// BootstrapSelect
	// ---------------------------------------------------------------------------------------
	if ($().selectpicker) {
		$('.selectpicker').selectpicker();
	}
	// prettyPhoto
	// ---------------------------------------------------------------------------------------
	if ($().prettyPhoto) {
		$("a[data-gal^='prettyPhoto']").prettyPhoto({
			theme: 'dark_square'
		});
	}
	// Scroll totop button
	// ---------------------------------------------------------------------------------------
	$(window).scroll(function () {
		if ($(this).scrollTop() > 1) {
			toTop.css({bottom: '15px'});
		} else {
			toTop.css({bottom: '-100px'});
		}
	});
	toTop.on('click', function () {
		$('html, body').animate({scrollTop: '0px'}, 800);
		return false;
	});
	// Add hover class for correct view on mobile devices
	// ---------------------------------------------------------------------------------------
	/*hover.on('hover',
	 function () {
	 $(this).addClass('hover');
	 },
	 function () {
	 $(this).removeClass('hover');
	 }
	 );*/
	// Ajax / load external content in tabs
	// ---------------------------------------------------------------------------------------
	$('[data-toggle="tabajax"]').on('click', function (e) {
		e.preventDefault();
		var loadurl = $(this).attr('href');
		var targ = $(this).attr('data-target');
		$.get(loadurl, function (data) {
			$(targ).html(data);
		});
		$(this).tab('show');
	});
	// Sliders
	// ---------------------------------------------------------------------------------------
	if ($().owlCarousel) {
		var owl = $('.owl-carousel');
		owl.on('changed.owl.carousel', function (e) {
			// update prettyPhoto
			if ($().prettyPhoto) {
				$("a[data-gal^='prettyPhoto']").prettyPhoto({
					theme: 'dark_square'
				});
			}
		});
		// Main slider
		if (mainSlider.length) {
			mainSlider.owlCarousel({
				items: 1,
				autoplay: false,
				autoplayHoverPause: true,
				//loop: true,
				margin: 0,
				dots: false,
				nav: false,
				touchDrag: false, mouseDrag: false,
				// navText: [
				//     "<i class='fa fa-angle-left'></i>",
				//     "<i class='fa fa-angle-right'></i>"
				// ],
				responsiveRefreshRate: 100,
				responsive: {
					0: {items: 1},
					479: {items: 1},
					768: {items: 1},
					991: {items: 1},
					1024: {items: 1}
				}
			});
		}

		// Testimonials carousel
		if (testimonialsCarousel.length) {
			testimonialsCarousel.owlCarousel({
				autoplay: false,
				loop: false,
				margin: 0,
				dots: true,
				nav: false,
				navText: [
					"<i class='fa fa-angle-left'></i>",
					"<i class='fa fa-angle-right'></i>"
				],
				responsive: {
					0: {items: 1},
					479: {items: 1},
					768: {items: 1},
					991: {items: 1},
					1024: {items: 1},
					1280: {items: 1}
				}
			});
		}

		// on tab click
		$('a[data-toggle="tab"]').on('show.bs.tab', function (e) {
			updater();
		});
		$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
			updater();
		});
	}
	// Sliders
	// ---------------------------------------------------------------------------------------
	if ($().swiper) {
		if (swiperOffersBest.length) {
			swiperOffersBest = new Swiper(swiperOffersBest, {
				direction: 'horizontal',
				slidesPerView: 3,
				spaceBetween: 30,
				paginationType: 'bullets',
				autoplay: 5000,
				loop: false,
				paginationClickable: true,
				pagination: '.swiper-pagination',
				nextButton: '.swiper-button-next',
				prevButton: '.swiper-button-prev'
			});
		}

		var swiper = new Swiper('.navigation', {
			scrollbar: '.swiper-scrollbar',
			direction: 'vertical',
			slidesPerView: 'auto',
			mousewheelControl: true,
			freeMode: true
		});
		if ($('.content-area.scroll').length) {
			var swiper2 = new Swiper('.content-area.scroll', {
				scrollbar: '.swiper-scrollbar',
				direction: 'vertical',
				slidesPerView: 'auto',
				mousewheelControl: true,
				freeMode: true
			});
		}

	}
	// countdown
	// ---------------------------------------------------------------------------------------
	if ($().countdown) {
		var austDay = new Date();
		austDay = new Date(austDay.getFullYear() + 1, 1 - 1, 26);
		$('#dealCountdown1').countdown({until: austDay});
		$('#dealCountdown2').countdown({until: austDay});
		$('#dealCountdown3').countdown({until: austDay});
	}

	updater();
});

jQuery(window).load(function () {
	updater();
});

function updater() {
	if ($().sticky) {
		$('.header.fixed').sticky('update');
	}

	// refresh swiper slider
	if ($().swiper) {
		//
		if (typeof (swiperOffersBest.length) == 'undefined') {
			swiperOffersBest.update();
			swiperOffersBest.onResize();
			if ($(window).width() > 991) {
				swiperOffersBest.params.slidesPerView = 3;
				//swiperOffersBest.stopAutoplay();
				//swiperOffersBest.startAutoplay();
			}
			else {
				if ($(window).width() < 768) {
					swiperOffersBest.params.slidesPerView = 1;
					//swiperOffersBest.stopAutoplay();
					//swiperOffersBest.startAutoplay();
				}
				else {
					swiperOffersBest.params.slidesPerView = 2;
					//swiperOffersBest.stopAutoplay();
					//swiperOffersBest.startAutoplay();
				}
			}
		}
	}

	// refresh waypoints
	//$.waypoints('refresh');
	// refresh owl carousels/sliders
	//owlCarouselSelector.trigger('refresh');
	//owlCarouselSelector.trigger('refresh.owl.carousel');

	//$('.datepicker').datetimepicker();

}

jQuery(window).scroll(function () {
	if ($().sticky) {
		$('.header.fixed').sticky('update');
	}
});


jQuery(document).ready(function () {
	//tooltips
	$('[data-toggle="tooltip"]').tooltip();
	// countdown
	if ($().countdown) {
	    var austDay = new Date();
		austDay = new Date(2017, 2, 1);
		$('#cretan_countdown').countdown({until: austDay});

		fixPageHeight();
	}
});
jQuery(window).load(function(){
	fixPageHeight();
});
jQuery(window).resize(function(){
	updater();
	fixPageHeight();
});
function fixPageHeight(){
    jQuery('.page').css('height', jQuery(window).height() );
    jQuery('.page').css('min-height', jQuery(window).height() );
}
