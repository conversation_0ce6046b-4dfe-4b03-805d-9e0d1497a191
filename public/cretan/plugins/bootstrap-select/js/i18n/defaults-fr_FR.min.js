/*!
 * Bootstrap-select v1.6.3 (http://silviomoreto.github.io/bootstrap-select/)
 *
 * Copyright 2013-2014 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a){a.fn.selectpicker.defaults={noneSelectedText:"Aucune s&eacute;lection",noneResultsText:"Aucun r&eacute;sultat",countSelectedText:function(a){return a>1?"{0} &eacute;l&eacute;ments s&eacute;lection&eacute;s":"{0} &eacute;l&eacute;ment s&eacute;lection&eacute;"},maxOptionsText:function(a,b){var c=[];return c[0]=a>1?"Limite atteinte ({n} &eacute;l&eacute;ments max)":"Limite atteinte ({n} &eacute;l&eacute;ment max)",c[1]=b>1?"Limite du groupe atteinte ({n} &eacute;l&eacute;ments max)":"Limite du groupe atteinte ({n} &eacute;l&eacute;ment max)",c},multipleSeparator:", "}}(jQuery);