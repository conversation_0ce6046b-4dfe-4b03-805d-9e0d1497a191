@CHARSET "UTF-8";
* {
    font-family: '<PERSON><PERSON>', sans-serif;
    text-transform: none !important;
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
    font-family:'Roboto Condensed', Sans-Serif;
    color: #555;
}
p {
    margin-bottom: 5px;
}
.round{
    border-radius: 50%;
}
.bordered {
    border:1px solid #d8d8d8
}
.border-top {
    border-top: 1px solid #e8e8e8
}
.border-bottom {border-bottom: 1px solid #e8e8e8}
.border-bottom-dash {border-bottom: 1px dashed #e8e8e8}
.dottedUnderline { border-bottom: 1px dotted; border-color: #999 }
.responsive {
    width:100%;
}
.bg-white {
    background-color: #ffffff;
}
.page-section {
    padding-top:40px;
}
.to-top {
  background-color: rgba(0, 121, 193, 0.57);
}
.logo{
    background-color: #ffffff;
    border: 1px solid rgba(235, 232, 232, 0.9);
    border-top:none;
}
.logo-bordered {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}
/* -------------------------------------------------------------------------- */
/* Main Menu
/* ========================================================================== */

.sf-menu{
    font-size:16px;
}
.sf-menu.nav > li.lang-dropdown > a:hover:before,
.sf-menu.nav > li.lang-dropdown > a:focus:before {
    content: none;
}
.navigation ul.lang-items {
    min-width: 30px;
    margin-left: 2px;
}
.sf-menu li.fleet  ul {
    width: 480px ;
    background-color: #ffffff;
}
.sf-menu li.fleet > ul li {
    width: 50%;
    float: left;
    font-size:14px;
}

.sf-menu.nav li  span.main-menu-noLinkItem {
    padding: 28px 15px 28px 15px;
    color: #a5abb7;
    position: relative;
    display: block;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.sf-menu.nav > li:hover > span.main-menu-noLinkItem{
    color: #36414d !important;
    cursor: pointer;
}

.sf-menu.nav > li.active > span.main-menu-noLinkItem{
    color: #36414d;
}

.sf-menu.nav > li > span.main-menu-noLinkItem:hover:before,
.sf-menu.nav > li > span.main-menu-noLinkItem:focus:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 5px;
    width: 100%;
    background-color: #0079c1;
}

.is-sticky .sf-menu.nav > li > span.main-menu-noLinkItem {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
}



/* -------------------------------------------------------------------------- */
/* Footer
/* ========================================================================== */

.footer {
    background-color: #f4f5f7;
}
.footer-widgets {
    font-size: 13px;
}
.footer-widgets .widget-title::before {
    background-color: #cbcbcb;
}
.footer-widgets .widget-title {
    font-weight: 500;
}
.footer-meta {
    padding:12px 0;
}
img#eot-logo{
    width:100px;
    margin-bottom:10px;
}
.social-icons a{
    border-color: #c3c3c3;
}
.social-icons a:hover i{
    color: #fafafa;
}
.social-icons a:hover {
    background-color: #00a78e;
}

.widget-categories ul li a {
    padding:5px 0;
}
.widget-categories ul li a:before {
    content: none;
}

/* -------------------------------------------------------------------------- */
/* Homepage
/* ========================================================================== */
#home-search-bucket{
    padding-top:30px;
}
#what-we-offer img,
#what-we-offer .fa {
    border:1px solid #2d2c2c;
    -webkit-border-radius:50%;
    -moz-border-radius:50%;
    border-radius:50%;
    padding:7px;
}
/*-- slider title --*/
.main-slider .caption-subtitle {
    font-family: 'Roboto', sans-serif;
    font-size: 52px;
    font-weight: 500;
}
/*-- slider subtitle --*/
.main-slider .caption-title {
    font-weight:300;
    color: #e9e9e9;
}
.main-slider .item {
    height:600px;
}
.main-slider .slide1 {
    background-image: url('../img/preview/slider/slide-1.1.jpg');
}
/*-- fix for datepicker overflowing main slider --*/
#homepage-slider-section{
    overflow: visible !important;
}
#main-slider .owl-stage-outer {
    overflow: visible !important;
}
/*-- featured section --*/
.thumbnail-car-card {
    border-color: #dddddd;
}
.swiper--offers-best .caption-title{
    font-size: 20px;
    font-weight:normal;
}
.swiper--offers-best .caption-text{
    font-size: 12px;
}
.swiper--offers-best .post_list_image {
    width: 100%;
    height: 210px;
    background-size: cover;
    background-position: center;
}
.thumbnail-car-card .buttons .btn-theme {
    font-weight:500;
    font-size:15px;
}
.thumbnail-car-card .buttons .btn-theme span{
    color: #d2d2d2;
    font-size:12px;
}
table.listing-features td{
    cursor: default;
}
.swiper--offers-best .media-link:after, .fleet-item .media-link:after {
    content:'\A';
    position:absolute;
    width:100%; height:100%;
    top:0; left:0;
    background:rgba(255,255,255,0.6);
    opacity:0;
    transition: all 0.4s;
    -webkit-transition: all 0.4s;
}
.swiper--offers-best .media-link:hover:after, .fleet-item .media-link:hover:after{
    opacity:0.2;
}
/*-- Testimonials section --*/
.page-section.testimonials {
    background: #e9e9e9 url(../img/chania.jpg) center 50% no-repeat;
    background-size: cover;
    color: #fafafa;
    padding-top:35px;

}
/*-- Who we are --*/
.who-we-are ul li + li {
    margin-top: 10px;
}
.who-we-are ul li {
    padding-left: 30px;
    position: relative;
}
.who-we-are ul li > .fa {
    position: relative;
    left: 0;
    top: 3px;
    padding-right: 15px;
}
.who-we-are ul {
    position: relative;
}
.who-we-are:before {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: .55;
    /*z-index: -1;*/
    background: url(../img/road-bg-2-inv.png) bottom 0 right 0 no-repeat;
}

/* -------------------------------------------------------------------------- */
/* Car index
/* ========================================================================== */

.alert.seo_text {
    background-color: white;
    border-left: 5px solid #00a4e4;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.06);
}
.bootstrap-select.no-icon button.selectpicker span.caret{
    display:none;
}
.bootstrap-select.btn-group.no-icon .btn .filter-option {
    padding-left:10px;
    text-align: center;
}
.fleet_list_image {
    width: 100%;
    height: 220px;
    background-size: cover;
    background-position: center;
}
.fleet-item .caption-text a {
    width:50%;
    text-align: center;
}
.fleet-item .caption-title {
   margin-bottom: 0;
    font-size:19px;
    font-weight: normal;
}
.fleet-item td.buttons > a {
    width:100%;
    font-size:13px;
    color: #444444;
}
.fleet-item td.buttons a.bottom-green {
    border-bottom: 4px solid #00a4e4;
}
.fleet-item td.buttons a.bottom-orange {
    border-bottom: 4px solid #F48024 ;
}
.fleet-item td.buttons.buttons-green:before {
    display: block;
    content: '';
    border-top: solid 3px #00a4e4;
    transform: scaleX(0);
    transition: transform 250ms ease-in-out;
}
.fleet-item td.buttons.buttons-orange:before {
    display: block;
    content: '';
    border-top: solid 3px #F48024;
    transform: scaleX(0);
    transition: transform 250ms ease-in-out;
}
.fleet-item td.buttons:hover:before
{
    transform: scaleX(1);
}

#fleet-search-bucket {
    background: transparent !important;
}
#fleet-search-bucket input, #fleet-search-bucket .bootstrap-select {
    background: #ffffff;
}
#fleet-search-bucket label{
    font-size:13px;
    font-weight:600;
}
#fleet-search-bucket .btn-submit {
    margin-top:26px;
}
/* -------------------------------------------------------------------------- */
/* Policy page
/* ========================================================================== */
.page-section.insurance-section,
.page-section.accident-report-section{
    padding-top:40px;
}
.page-section.policy
{
    padding-top:40px;
    padding-bottom:40px;
}
.page-section.policy h2{
    margin-bottom:0 ;
}
.insurance-item img {
    max-width:54px;
    margin-right:5px;
}
.insurance-item h3{
    margin-bottom:0;
    font-size:22px;
    color: #004940;
}
.policy-section h3 {
    font-size:20px;
    color: #004940;
}
.accident-report-section ul li i {
    color: #00bce4;
}
.page-section.image.policy {
    background: url(../img/road_bg.jpg) center 60% no-repeat;
    background-attachment: fixed;
    background-size: cover;
}
.image_alt {
    padding-top:33px;
    padding-bottom:25px;
    background: url(../img/chania.jpg) center 50% no-repeat;
    background-attachment: fixed;
    background-size: cover;
}
.image_alt ul {
    font-size: 17px;
    color: #fff;
}
.image_alt ul.booking-benefits li:before {
    content: '\f058';
    font-family: 'FontAwesome';
    color: #00a0af;
    color: #56a0d3;
    padding-right:10px;
}

/* -------------------------------------------------------------------------- */
/* About
/* ========================================================================== */
.page-section.image.about {
    background: url(../img/road-car-bg-2.jpg) center 60% no-repeat;
    background-attachment: fixed;
    background-size: cover;
    color: #fafafa;
}

/* -------------------------------------------------------------------------- */
/* Misc
/* ========================================================================== */

.error-page:after  {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: .35;
    z-index: 0;
    background: url(../img/road-bg-2-inv.png) bottom 0 right 0 no-repeat;
}
.error-page a{
    position: relative;
    z-index: 1;
}
.bg-theme-color {
    background-color: #0079c1;
}
.theme-color {
    color: #0079c1 !important;
}
.theme-color-dark {
    color: #30435c !important;
}
.modal-backdrop.in {
    opacity: 0.8;
}
.bckg-white{
    background-color: #fff;
}
.media .img {
    float: left;
}
.contact-info:before{
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: .15;
    z-index: -1;
    background: url(../img/road-bg-2-inv.png) bottom 0 right 0 no-repeat;

}
.contact-info .media .fa {
    background-color: #8aabbf;
}
.form-control {
    border-color: #dbdbdb;
}
.widget.shadow {
    border-color: #dbdbdb;
}
.page-section.breadcrumbs {
    /*background-color: #f0f1f3;*/
}
.alert-theme {
    color: #306987;
    background-color: #f3f4f6;
    border-color: #e8e9eb;
}
.no-border {
    border:none;
}
.opaque {
    opacity:0.9;
}
.image-small {
    background-position:center;
    background-size:cover;
    width:130px;
    height:90px;
}
.image-small:hover {
    opacity:0.8
}
.section-title {
    font-weight:600;
}
.page-section.dark {
    background-color: #36414d;
    color: #b1b3b4;
}
.btn-theme{
    font-weight:500;
    font-family: 'Roboto', sans-serif;
}
.btn-theme-green {
    background-color: #00a78e !important;
    border-color: #00a78e !important;
    /*color: #ffffff;*/
}
.btn-theme-orange {
    background-color: #F48024 !important;
    border-color: #F48024 !important;
    /*color: #ffffff;*/
}
.form-search .btn-theme-dark,
.form-search .btn-theme-dark:focus,
.form-search .btn-theme-dark:active {
    background-color: #0091cd;
    border-color: #0091cd;
    color: #ffffff;
}
.accordion .panel {
    margin-bottom: 10px;
}
.accordion .dot{
    margin-left:5px;
}
/*-- page header --*/
.page-section.breadcrumbs .page-header {
    margin: 20px 0 5px 0;
}
.page-section.breadcrumbs .page-header h1 {
    margin-top:27px;
    margin-bottom:10px;
    font-size: 40px;
    font-weight: 500;
    color: #666;
    font-family:'Roboto Condensed', sans-serif;
}
.page-section.breadcrumbs .page-header h2{
    margin-top:-7px;
    margin-bottom:17px;
    font-size:24px;
    font-family:'Roboto Condensed', sans-serif;
    color: #00a4e4;
}
.page-section.find-car {
    overflow: visible;
    padding-top: 34px;
    padding-bottom: 24px;
}
/*-- search teaser --*/
.find-car h2 {
    text-align: left;
}
.find-car a.btn {
    font-size: 14px;
    margin-top:10px;
}
.find-car h2 span {
    font-family: 'Roboto Condensed', Sans-Serif;
    font-weight:500;

}
/*-- select dropdown options text --*/
.bootstrap-select.btn-group .dropdown-menu li a span.text {
    font-size:14px;
}

/*-- datetimepicker disabled dates --*/
.datepicker table tr td.disabled,
.datepicker table tr td.disabled:hover {
    color: #bfbfbf;
    cursor: default;
}
input.datepicker{
    cursor: default;
}
.form-search label {
    font-weight: 500;
    letter-spacing: 0.9px;
}
.error {
    font-size: 12px;
    color: #b94a48;
}
.swiper-button-prev, .swiper-button-next {
    border:none;
}
.swiper-button-prev .fa, .swiper-button-next .fa{
    font-size:30px;
}
.swiper-button-prev:hover i, .swiper-button-next:hover i{
    color: #00a78e;
}
.box-icon-big {
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 36px;
}
.box-icon-border{
    background: 0 0;
    border: 1px solid #00a4e4;
}
.service-header {
    margin-bottom: 15px;
}
.service-title{
    font-size:23.66px;
}

/* -------------------------------------------------------------------------- */
/* Services
/* ========================================================================== */
.services .fa {
    font-size:48px;
}
.services:not(.dark) .fa {
    color: #000;
    opacity:0.4;
}
.services:not(.dark) img {
    opacity:0.4;
}

.services:not(.dark) h4{
    color: #36414d;
}
/* -------------------------------------------------------------------------- */
/* BOOKING
/* ========================================================================== */

/* step 1
/* ====== */
ul.booking-benefits li{
    float: left;
    padding-right:10px;
    padding-bottom:10px;
    width: 33.33%;
}
ul.booking-benefits li:before {
    content: '\f058';
    font-family: 'FontAwesome';
    color: #e60000;
    padding-right:7px;
}
.form-search.transparent {
    background: white;
    padding:20px;
    border:1px solid #dbdbdb;
    -webkit-border-radius:4px;
    -moz-border-radius:4px;
    border-radius:4px;
}
.form-search.transparent .form-control,
.form-search.transparent .bootstrap-select,
.form-booking .bootstrap-select,
#booking-form-1 .bootstrap-select
{
    background: white !important;
}
.form-search.transparent i.fa {
    color: #888;
}
#booking-form-1 {
    background: transparent !important;
}
#booking-form-1 input, #fleet-search-bucket .bootstrap-select {
    background: #ffffff;
}
#booking-form-1 label{
    font-size:14px;
    font-weight:600;
    color: #666;
}
#booking-form-1 .btn-submit {
    margin-top:26px;
}

/* step 2
/* ====== */
.car-listing .thumbnail-car-card .table td {
    color: #6d6d6d;
}
#step2 .car-listing, #step2 .sidebar {
    margin-top: 10px !important;;
}
/** Group List Item **/
.group-list-item {
    border: solid 1px #dfdfdf;
    background-color: #ffffff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.group-list-item.selected{
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.6);
    border-color: #a4a4a4;
}
.group-list-item:not(:first-of-type) {
    margin-top:30px;
}
.group-list-item:not(.selected):hover {
    border-color: #bcbcbc;
    /*border-radius:4px;*/
}
.group-list-item:hover {
    cursor: default;
}
.group-list-item img {
    padding:5px;
}
.group-info {
    padding-top:15px;
    padding-right:30px;
}

.btn-input {
    border:1px solid #eee;
    color: #888;
}
.btn-input.active {
    background-color: #00a4e4;
    color: #fff;
}
.selected-group-data a {
    color: #0d7cb9;
}
.selected-group-data a:before {

}
.selected-group-data a:hover {
    color: #0c5e8f;
    text-decoration: underline;
}

.group-list-item .price-info .price-value {
    font-size:28px;
    color: #286891;
    line-height:1;
    letter-spacing:-1px;
}
.group-list-item .price-info .price-total {
    min-width: 10px;
    padding: 4px 7px;
    font-size: 12px;
    /*font-weight: 700;*/
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    background-color: #F48024;
    border-radius: 8px;
    margin-top: 3px;
}
.group-title {
    font-size:16px;
}
.group-caption-title-sub {
    font-size:16px;
}
/* group item booking info list */
.group-info ul li{
    float: left;
    padding-right:10px;
    padding-bottom:10px;
    width: 50%;
    font-size:13px;
}
.group-info ul li:before {
    content: '\f058';
    font-family: 'FontAwesome';
    color: #e60000;
    padding-right:5px;
}
/* booking group item bottom table */
.group-list-item .table {
    margin:0;
}
.group-list-item .table td img {
    padding:0;
}
.group-list-item .table td + td {
    border-left: solid 1px #e9e9e9;
}
.group-list-item .table td {
    text-align: center;
    font-size: 13px;
    color: #6d6d6d;
    padding: 10px 8px 8px 8px;
}
.group-list-item .table td.buttons {
    padding: 0;
    width:190px;
}
.group-list-item  .btn-theme {
    display: block;
    padding:13px 40px 12px 40px;
    line-height: 1;
    /*font-weight: 600;*/
    background-color: #0079c1;
    border-color: #0079c1;
}
.group-list-item .btn-theme:hover{
    background-color: #005C92;
    border-color: #005C92;
}
 .btn-muted {
    display: block;
    padding:13px 40px 12px 40px;
    line-height: 1;
    font-weight: 600;
    background-color: #fafafa;
     color: #0079c1;
}
.group-list-item .btn-muted:hover{
    color: #30435c !important;
}
/* step 2 info blocks */
.booking-info-banners .block {
    padding: 15px 17px;
    border: solid 4px #eee;
    background-image: url('../img/texture-1.gif');
}
.booking-info-banners .block h4 {
    font-weight: 500;
    color: #0079c1;
}
.booking-info-banners .block i {
    font-size: 60px;
    line-height: 60px;
    /*color: #39a2ae;*/
}
.booking-info-banners {
    margin-top:40px;
}
.widget.widget-details-reservation a.btn {
    margin-top: 20px;
    padding: 5px;
    border: 1px solid #fff;
    border-radius: 4px;
    background-color: white;
    color: #5594c6;
    font-size:12px;
    width:100%;
}
/* step 3
/* ====== */
.block-title.alt {
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    text-transform: uppercase;
    padding: 10px 70px 10px 20px;
    border: solid 1px #56a0d3;
    overflow: visible;
    position: relative;
    color: #fff;
    background-color: #56a0d3;
}
.accessory {
    margin-bottom:12px;
    padding:10px;
    /*border-radius:4px;*/
}
.accessory.picked {
    border: 1px solid #b0b0b0;
    box-shadow: 2px 2px 10px rgba(0,0,0,0.2);
}
.accessories-actions a img {
    transition: all 0.1s;
    -webkit-transition: all 0.1s;
}
.accessories-actions a:hover img{
    opacity:0.8;
}
.img-accessory {
    height:100px;
}
.payment-details {
    padding:10px 15px;
    -webkit-border-radius:4px;
    -moz-border-radius:4px;
    border-radius:4px;
}

.payment-details i {
    padding-right:5px;
}
/* Step 4
================ */
#repeating_client:hover{
    text-decoration: underline;
    color: #0079c1;
}
/*repeating client modal*/
#repeating_modal .modal-header {
    background-color: #00a4e4;
}
#repeating_modal .modal-header h4 {
    color: #fafafa;
}
#repeating_modal .modal-footer {
    border:none;
    padding-top:0;
}
#repeating_modal .btn-theme {
    padding-top:10px;
    padding-bottom:7px;
    border-radius:4px;
}
#step4 form label{
    font-size:16px;
    font-weight:500;
    color: #666;
}
/* Reservation Details Widget
================================= */
.widget.widget-details-reservation a.btn:hover {
    background-color: #4a80ac;
}
.widget.widget-details-reservation a {
    color: #5594c6;
    font-size:12px;
}
.widget.widget-details-reservation a:hover {
    text-decoration: underline;
    color: #517fae;
}
.widget-details-reservation .widget-title,
#reservation-summary .widget-title {
    font-size:20px !important;
    font-weight:400 !important;
}
.widget-details-reservation .selected-extras p:before {
    content: '\f0da';
    font-family: 'FontAwesome';
    color: #5594c6;
    padding-right:5px;
}
.widget-details-reservation .groupData div.img-text {
    position: relative;
    display: inline-block;
    width: 100%;
}
.widget-details-reservation .groupData div.img-text p.hover-top {
    position: absolute;
    background: rgba(0,0,0,0.5);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding-bottom:3px;
    padding-top:3px;
    color: #fff;
    top:0;
    width:100%;
}
.widget-details-reservation .groupData div.img-text div.hover-bottom{
    position: absolute;
    bottom:0px;
    background: rgba(0,0,0,0.4);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    width:100%;
    padding:0 5px 0 5px;
    color: #fff;
}
.widget-details-reservation .groupData div.img-text div.extras p {
    padding-bottom: 0;
    margin-bottom:0;
}
.widget-details-reservation .widget-title-sub {
    padding-bottom:8px;
    margin-bottom:5px;
}
.widget-details-reservation .media {
    margin-top:10px;
}
/*reservation summary */
#reservation-summary .summary-section {
    padding-bottom: 5px;
}
#reservation-summary table tr:not(#summary-final-price) td {
    border:none;
    padding: 1px 0;
}
a#complete_booking,
.pagination > li:last-child > a{
    background-color: #0079c1;
    color: #fff;
}
a#complete_booking:hover,
.pagination > li:last-child > a:hover{
    background-color: #30435c;
    border-color: #30435c;
    color: #fff;
}
tr#summary-final-price {
    background-color: #f5f6f8;
    border: 1px solid #ececec;
}
tr#summary-final-price td {
    padding:10px;
    border:none;
}
/*loader*/
.loadingoverlay_fontawesome {
    font-size:90px !important;
    font-weight:400 !important;
}
/* -------------------------------------------------------------------------- */
/* LISTING DETAIL
/* ========================================================================== */
.white-wrapper {
    background-color: #fff;
    padding: 0 20px 20px 20px;
    border: 1px solid #eee;
    margin-top: 10px;
    margin-bottom:40px;
}
.group-description {
    padding:20px 15px;
}
 /* car header */
.car-header {
    margin-top:10px;
}
/* info blocks
/* ============ */
.info-banners .block {
    padding: 15px 17px;
    border: solid 4px #eee;
    background-image: url('../img/texture-1.gif');
}
.info-banners .block h4 {
    font-weight: 500;
    color: #0079c1;
}
.info-banners .block i {
    font-size: 60px;
    line-height: 60px;
    color: #2b82ad;
}
.info-banners img, .booking-info-banners img{
    opacity:0.5;
}
.block-title.alt .fa {
    border: solid 1px #e6e6e6;
}
/*sidebar form widget*/
.widget-find-car form label {
    font-size:12px;
    letter-spacing:normal;
}
.widget-find-car .datepicker {
    color: #5c5c5c;
}
/* vehicle info section (features, equipment)
/* =========================================== */

.info-item {
    /*font-size:13px;*/
    width:33.33%;
}
.vehicle-default-equipment .info-item{
    width:20%;
}
.vehicle-features .info-item i {
    border: 1px solid #d3d3d3;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    padding: 5px;
    margin-right: 5px;
    width: 28px;
    text-align: center;
    color: #29739d;
}
/* Related Cars Widget
/* ===================== */
.widget-related .media + .media {
    padding-top: 15px;
    border-top: solid 1px #e9e9e9;
}
/* Predefined Offers Widget
/* ========================= */
.widget-offers ul li {
    margin-bottom:10px;
    padding-bottom:10px;
}
.widget-offers ul li:last-of-type {
    border:none;
}
 /*offers book it button*/
.widget-offers ul li a{
    font-family: "Roboto Condensed", Sans-Serif;
    padding: 2px 10px 2px 10px;
    font-size: 13px;
    border-radius: 2px;
    color: white;
    background-color: #F48024;
    border-color: #F48024;
}
.widget-offers ul li a:hover{
    color: #F48024;
    background-color: white;
    border-color: #F48024;
}
.sidebar .widget .widget-title {
    font-size: 16px;
    font-weight: 600;
}
/* Suggested Carousel
/* ========================================= */
#suggested .owl-next, #suggested .owl-prev {
    padding:2px 15px;
}
.suggested-carousel .owl-carousel div a {
    display: block;
    text-align: center;
    border: solid 1px #f3f4f6;
    background-color: #f3f4f6;
    padding: 10px;
    margin: 1px;
    -webkit-transition: none;
    transition: none;
}
.suggested-carousel .owl-carousel div a:hover {
    border-color: #ddd;;
}
.suggested-carousel .suggested-group {
    display: block;
}

/* FONTS & TEXTS
/* ========================================================= */

.condensed { font-family: 'Roboto Condensed', sans-serif; }

.text.thin { font-weight: 200 }

.text.book { font-weight: 300 }

.text.normal { font-weight: 400 }

.text.strong {font-weight: 500}

.text.semibold { font-weight: 600 }

.text.bold { font-weight: 700 }

.text-light-blue {
    color: #00aaf1 !important;
}
.text-white{
    color: #ffffff !important;
}
.text-green {
    color: #00a78e !important;
}
.text-green-darker {
    color: #007b69 !important;
}
.text-red {
    color: #fd5c63;
}
.text-orange {
    color: #F48024;
}
.text-dark {
    color: #333 !important;
}
.text-blue {
    color: #56a0d3;
}
.text-muted-extra {
    color: #b1b1b1 !important;
}


/* MARGINS & PADDINGS
/* ================================== */
.margin-none { margin: 0 !important}

.margin { margin: 1em }

.margin-left { margin-left: 1em }

.margin-right { margin-right: 1em }

.margin-top { margin-top: 1em }

.margin-bottom { margin-bottom: 1em }

.margin-large { margin: 3em }

.margin-left-large,
.margin-large-left { margin-left: 3em }

.margin-right-large,
.margin-large-right { margin-right: 3em }

.margin-top-large,
.margin-large-top { margin-top: 3em }

.margin-bottom-large,
.margin-large-bottom { margin-bottom: 3em }

.margin-small { margin: 0.3em }

.margin-left-small,
.margin-small-left { margin-left: 0.3em }

.margin-right-small,
.margin-small-right { margin-right: 0.3em }

.margin-top-small,
.margin-small-top { margin-top: 0.3em }

.margin-bottom-small,
.margin-small-bottom { margin-bottom: 0.3em }

.mt-10 { margin-top: 10px !important }
.mb-15 { margin-bottom:15px !important }

.padding-none { padding: 0 }

.padding { padding: 2em 0 }

.padding-left { padding-left: 2em }

.padding-right { padding-right: 2em }

.padding-top { padding-top: 2em }

.padding-bottom { padding-bottom: 2em }

.padding-large { padding: 3.5em 0 }

.padding-left-large { padding-left: 3.5em }

.padding-right-large { padding-right: 3.5em }

.padding-top-large { padding-top: 3.5em }

.padding-bottom-large { padding-bottom: 3.5em }

.padding-medium { padding: 3em 0 }

.padding-left-medium { padding-left: 3em }

.padding-right-medium { padding-right: 3em }

.padding-top-medium { padding-top: 3em }

.padding-bottom-medium { padding-bottom: 3em }

.padding-small { padding: 1em 0 }

.padding-left-small { padding-left: 1em }

.padding-right-small { padding-right: 1em }

.padding-top-small { padding-top: 1em }

.padding-bottom-small { padding-bottom: 1em }

.padding-tiny { padding: 0.5em 0 }

.padding-left-tiny { padding-left: 0.5em }

.padding-right-tiny { padding-right: 0.5em }

.padding-top-tiny { padding-top: 0.5em }

.padding-bottom-tiny { padding-bottom: 0.5em }

.padding-right-xlarge { padding-right: 6em }

/* -------------------------------------------------------------------------- */
/* Media Queries
/* ========================================================================== */

@media (max-width: 1199px) and (min-width: 992px){
    ul.booking-benefits li {
        width:50%;
    }
    #contact-form .form-button-submit {
        float: none !important;
        margin-top:10px;
        width:100%;
    }
    #contact-form .captcha-holder {
        float: none !important;
    }
}

@media (max-width: 991px) {

    .btn-block{
        margin-left:auto;
        margin-right:auto;
    }
    .main-slider .item {
        min-height: 750px;
    }
    .main-slider .caption-subtitle {
        font-size:38px;
    }

    .info-item {
        width:50%;
    }

    /*detail sidebar related widget*/
    .widget-related {
        width:80%;
        margin-left:auto;
        margin-right: auto;
    }

    .sf-menu li.fleet  ul {
        /*display: none !important;*/
        width: auto ;
        background-color: transparent;
        font-size: 12px;
    }
    .sf-menu li.fleet  ul li a {
        padding-top:0;
        padding-bottom:0;
        padding-left:25px;
    }

    /* homepage who we are */
    .who-we-are {
        padding-bottom:30px;
    }
    .who-we-are ul li {
        padding-left: 0px;
    }

    .sf-menu.nav > li > span.main-menu-noLinkItem {
        color: #ffffff;
    }

    .sf-menu.nav > li > span.main-menu-noLinkItem {
        background-color: #36414d;
        padding-top: 10px !important;
        padding-bottom: 0px !important;
    }
    .sf-menu.nav > li > span.main-menu-noLinkItem:hover {
        background-color: #ffffff !important;
        color: #0079c1;
    }
}

@media (max-width: 991px) and (min-width: 500px)
{
    .widget-details-reservation .pickupData,
    .widget-details-reservation .dropoffData {
        float:left;
        width:50%;
    }
    .widget-details-reservation .groupData div.img-semi-trans {
        height:200px !important;
        margin-bottom:10px;
    }
}

@media (max-width: 767px) {
    ul.booking-benefits li {
        width:50%;
    }
    /* -- main slider --*/
    .main-slider .item {
        min-height: 850px;
    }
    .main-slider .caption-subtitle {
        font-size:32px;
    }
    .main-slider .caption-title {
        font-size:20px;
    }
    /*-- search teaser --*/
    .find-car h2 {
        text-align: center;
    }
    .find-car a.btn {
        margin-left:auto;
        margin-right:auto;
    }
    /* -- footer --*/
    .footer .widget {
        text-align: center;
    }
    .footer-widgets .widget-title{
        padding:0;
        margin-bottom:10px;
        font-weight:600;
    }
    .footer-widgets .widget-title::before{
        display:none;
    }
    .footer ul.social-icons {
        display: inline-block;
    }

    .vehicle-default-equipment .info-item{
        width:25%;
    }

    .clear-xs {
        float: none !important;
    }

    /* booking step 2 */
    .group-list-item .group-info {
        padding-left:30px;
        margin-top:-30px;
    }
    .group-list-item img {
        padding:20px 20px 0 20px;
    }

}

@media (max-width: 520px) {
    #contact-form .form-button-submit {
        float: none !important;
        margin-top:10px;
        width:100%;
    }
    #contact-form .captcha-holder {
        float: none !important;
    }
    .main-slider #formSearchSubmit,
    #formSearchSubmit3{
        width:100%;
    }
}

@media (max-width: 480px) {
    .main-slider .ver1 .form-search .btn-submit {
        width:100%;
    }
    .vehicle-default-equipment .info-item{
        width:33.33%;
    }
    /*detail sidebar related widget*/
    .widget-related {
        width:100%;
    }
    /*booking step 2 group list item*/
    .price-info, .group-title {
        text-align: center;
    }
    .clear-xxs {
        float: none !important;
    }
    .pull-left-xxs {
        float: left;
    }
    .pull-right-xxs{
        float: right;
    }
    .hidden-xxs {
        display: none !important;
    }
    .visible-xxs{
        display: block !important;
    }
    .group-list-item .table td {
        border-bottom: 1px solid #e9e9e9;
    }
    .group-list-item .price-info .price-total{
        padding:7px;
    }
    .group-caption-title-sub {
        text-align: center;
        margin-bottom:10px;
    }
}

@media (max-width: 400px) {
    .main-slider .item {
        min-height: 880px;
    }
    .col-xxs-12 {
        width:100%;
    }
    ul.booking-benefits li {
        width:100%;
    }

}
