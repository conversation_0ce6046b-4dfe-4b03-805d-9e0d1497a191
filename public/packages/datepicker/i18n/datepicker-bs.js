/* Bosnian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON>. */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.bs = {
	closeText: "Zatvori",
	prevText: "&#x3C;",
	nextText: "&#x3E;",
	currentText: "<PERSON><PERSON>",
	monthNames: [ "<PERSON><PERSON>r","<PERSON>ruar","<PERSON>","April","<PERSON>","<PERSON><PERSON>",
	"<PERSON><PERSON>","August","Septembar","Oktobar","Novembar","Decembar" ],
	monthNamesShort: [ "Jan","Feb","Mar","Apr","Maj","Jun",
	"Jul","Aug","Sep","Okt","Nov","Dec" ],
	dayNames: [ "<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>v<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>" ],
	dayNamesShort: [ "<PERSON>","Pon","U<PERSON>","<PERSON>","Čet","Pet","Sub" ],
	dayNamesMin: [ "Ne","Po","Ut","Sr","Če","Pe","Su" ],
	weekHeader: "Wk",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.bs );

return datepicker.regional.bs;

} ) );
