/* Indonesian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.id = {
	closeText: "Tutup",
	prevText: "&#x3C;mundur",
	nextText: "maju&#x3E;",
	currentText: "hari ini",
	monthNames: [ "<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","April","<PERSON>","<PERSON><PERSON>",
	"<PERSON><PERSON>","<PERSON>gus<PERSON>","September","Oktober","Nopember","Desember" ],
	monthNamesShort: [ "Jan","Feb","Mar","Apr","<PERSON>","<PERSON>",
	"<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>" ],
	dayNames: [ "<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>bt<PERSON>" ],
	dayNamesShort: [ "<PERSON>","Sen","Sel","Rab","kam","Jum","Sab" ],
	dayNamesMin: [ "Mg","Sn","Sl","Rb","Km","jm","Sb" ],
	weekHeader: "Mg",
	dateFormat: "dd/mm/yy",
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.id );

return datepicker.regional.id;

} ) );
