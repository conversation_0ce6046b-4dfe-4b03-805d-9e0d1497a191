/* Finnish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.fi = {
	closeText: "Sulje",
	prevText: "&#xAB;Edellinen",
	nextText: "Seuraava&#xBB;",
	currentText: "Tänään",
	monthNames: [ "<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON>aku<PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>" ],
	monthNamesShort: [ "<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>" ],
	dayNamesShort: [ "<PERSON>","<PERSON>","<PERSON>i","<PERSON>","<PERSON>","<PERSON>e","La" ],
	dayNames: [ "Sunnuntai","<PERSON>anantai","<PERSON>ii<PERSON>i","<PERSON><PERSON>viik<PERSON>","<PERSON><PERSON>i","<PERSON>jantai","<PERSON>antai" ],
	day<PERSON>ames<PERSON>in: [ "<PERSON>","<PERSON>","<PERSON>i","<PERSON>","<PERSON>","<PERSON>e","<PERSON>" ],
	week<PERSON><PERSON>er: "Vk",
	dateFormat: "d.m.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.fi );

return datepicker.regional.fi;

} ) );
