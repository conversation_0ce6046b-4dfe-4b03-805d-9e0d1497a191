/* Luxembourgish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> <<EMAIL>> */
( function( factory ) {
	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
}( function( datepicker ) {

datepicker.regional.lb = {
	closeText: "Fäerdeg",
	prevText: "<PERSON>réck",
	nextText: "Weider",
	currentText: "Haut",
	monthNames: [ "<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>",
	"<PERSON><PERSON>","August","September","Okto<PERSON>","November","Dezember" ],
	monthNamesShort: [ "Jan", "Feb", "<PERSON>äe", "<PERSON>br", "<PERSON>e", "Jun",
	"Jul", "Aug", "Sep", "<PERSON><PERSON>", "Nov", "Dez" ],
	dayNames: [
		"<PERSON>ndeg",
		"Méindeg",
		"Dënschdeg",
		"Mëttwoch",
		"Donneschdeg",
		"Freideg",
		"Samschdeg"
	],
	dayNamesShort: [ "Son", "Méi", "Dën", "Mët", "Don", "Fre", "Sam" ],
	dayNamesMin: [ "So","Mé","Dë","Më","Do","Fr","Sa" ],
	weekHeader: "W",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.lb );

return datepicker.regional.lb;

} ) );
