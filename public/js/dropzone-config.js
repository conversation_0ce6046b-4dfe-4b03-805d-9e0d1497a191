var photo_counter = 0;
Dropzone.options.realDropzone = {

    uploadMultiple: false,
    parallelUploads: 100,
    maxFilesize: 8,
    previewsContainer: '#dropzonePreview',
    previewTemplate: document.querySelector('#preview-template').innerHTML,
    addRemoveLinks: true,
    dictRemoveFile: 'Remove',
    dictFileTooBig: 'Image is bigger than 8MB',
    dictRemoveFileConfirmation: "Are you sure you wish to delete this image?",

    // The setting up of the dropzone
    init:function() {

        // Add server images
        var myDropzone = this;
        
        if($('#listingImagesURL').length || $('#locationImagesURL').length){
            var imagesURL = $('#listingImagesURL').length ? '#listingImagesURL' : '#locationImagesURL';
        	$.get($(imagesURL).val(), function(data) {
        		
        		$.each(data.images, function (key, value) {
        			
        			var file = {name: value.original, size: value.size};
        			myDropzone.options.addedfile.call(myDropzone, file);
        			myDropzone.options.thumbnail.call(myDropzone, file, value.url);
        			myDropzone.createThumbnailFromUrl(file, value.url);
        			myDropzone.emit("complete", file);
        	    	$('.serverfilename', file.previewElement).val(value.original);
        			photo_counter++;
        			$("#photoCounter").text( "(" + photo_counter + ")");
        		});
        		updateImages(true);
        	});
        }

        this.on("removedfile", function(file) {
            $.ajax({
                type: 'POST',
                url: $('#imageDeleteRoute').val(),
                data: {id: $('.serverfilename', file.previewElement).val() , _token: $('#csrf-token').val()},
                dataType: 'html',
                success: function(data){
                    var rep = JSON.parse(data);
                    if(rep.code == 200)
                    {
                        photo_counter--;
                        $("#photoCounter").text( "(" + photo_counter + ")");
                        updateImages(true);
                    }

                }
            });

        } );
    },
    error: function(file, response) {
        if($.type(response) === "string")
            var message = response; //dropzone sends it's own error messages in string
        else
            var message = response.message;
        file.previewElement.classList.add("dz-error");
        _ref = file.previewElement.querySelectorAll("[data-dz-errormessage]");
        _results = [];
        for (_i = 0, _len = _ref.length; _i < _len; _i++) {
            node = _ref[_i];
            _results.push(node.textContent = message);
        }
		updateImages();
        return _results;
    },
    success: function(file, response) {
    	$('.serverfilename', file.previewElement).val(response.filename);
        photo_counter++;
        $("#photoCounter").text( "(" + photo_counter + ")");
		updateImages();
    }
}



