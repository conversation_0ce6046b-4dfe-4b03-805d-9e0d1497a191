$(document).ready(function() {

    // Questions Slider
    var questionsSlider = new Swiper ('.swiper-container.questions', {
        slidesPerView: 1,
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        }
    });

    // On star click change slide
    $( "#feedbackForm" ).on( "change", "input[type=radio]", function(e) {
        $('form#feedbackForm').submit();
        // questionsSlider.slideNext();
    });

    // make the specific form ajax<scriptified
    $('form#feedbackForm').on('submit', function(e){
        // grab the form
        var form = $(this);
        // <PERSON><PERSON> creates hidden inputs for the method attributes of forms (except post methods)
        // so we need to pass the POST by ourselves
        var method = form.find('input[name="_method"]').val() || 'POST';
        var token = $('input[name="_token"]').val();
        // grab the url
        var url = form.prop('action');

        $.ajax({
            type: method,
            url: url,
            headers: {
                "x-csrf-token": token
            },
            data: form.serialize(),
            dataType: 'json'
        })
            .fail(function() {
                swal({   title: 'An error occurred', text: 'Please try again',   type:"error"   });
            })
            .done(function(data){
                if(data.status == 'success')
                {
                    // swal({   title: 'Thank you for your feedback!' ,   text: '', type: "success"  });
                    console.log('form success');
                    questionsSlider.appendSlide($('.next-slides .swiper-slide').eq(0));
                    questionsSlider.slideNext();

                }
                else
                {
                    swal({   title: 'An error occurrged', text: 'Please try again',   type:"warning"   });
                }
            });
        //alert('cot');
        e.preventDefault();
    });
});
