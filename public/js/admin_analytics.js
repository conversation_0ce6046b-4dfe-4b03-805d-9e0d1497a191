$(function () {
    /* Tables */
    $('.data-table').DataTable({
        language: {
            decimal: ",",
        }
    });
    /* Tables */
    /* Charts */
    if($('#world-map').length)
    {
        showWorldMap();
    }
    if($('#barChart').length)
    {
        addBarChart('#barChart', graphData['created_at'], graphData['created_at'],graphData['pickup_date']);
    }
    if($('#budgetPickupBarChart').length)
    {
        addLineChart('#budgetPickupBarChart', 'Pickup dates', graphData['analytics_dates'], graphData['sum_pickup_date'],graphData['pickup_date']);
    }
    if($('#budgetCreatedBarChart').length)
    {
        addLineChart('#budgetCreatedBarChart', 'Created dates', graphData['analytics_dates'], graphData['sum_created_date'],graphData['created_date']);
    }
    if($('#pieChartListing').length)
    {
        addPieChart('#pieChartListing', Object.keys(tableData['listing_model_sum']), Object.values(tableData['listing_model_sum']));
    }
    if($('#pieChartGroup').length)
    {
        addPieChart('#pieChartGroup', Object.keys(tableData['listing_group_sum']), Object.values(tableData['listing_group_sum']));
    }
    /* Charts */
});

function showWorldMap()
{
    $('#world-map').vectorMap({
        map: 'world_en',
        backgroundColor: 'transparent',
        borderColor: '#818181',
        borderOpacity: 0.25,
        borderWidth: 1,
        color: '#f4f3f0',
        enableZoom: true,
        hoverColor: '#005ace',
        hoverOpacity: null,
        normalizeFunction: 'linear',
        scaleColors: ['#b6d6ff', '#005ace'],
        values: graphData['customers'],
        // selectedColor: '#c9dfaf',
        // selectedRegions: Object.keys(customersData),
        showTooltip: true,
        series: {
            regions: [{
                values: graphData['customers'],
                scale: ['#C8EEFF', '#0071A4'],
                normalizeFunction: 'polynomial'
            }]
        },
        onLabelShow: function (event, label, code) {
            if(graphData['customers'][code] !== undefined)
            {
                label.text(label.text() + ': ' + graphData['customers'][code]);
            }
        }
    });
}

function addBarChart(element, labels, firstBarData, secondBarData)
{
    // Date charts
    let dateChartData = {
        labels  : Object.keys(labels),
        datasets: [
            {
                label               : 'Reservation created',
                backgroundColor     : 'rgba(60,141,188,0.9)',
                borderColor         : 'rgba(60,141,188,0.8)',
                pointRadius          : false,
                pointColor          : '#3b8bba',
                pointStrokeColor    : 'rgba(60,141,188,1)',
                pointHighlightFill  : '#fff',
                pointHighlightStroke: 'rgba(60,141,188,1)',
                data                : Object.values(firstBarData)
            },
            {
                label               : 'Pickup',
                backgroundColor     : 'rgba(210, 214, 222, 1)',
                borderColor         : 'rgba(210, 214, 222, 1)',
                pointRadius         : false,
                pointColor          : 'rgba(210, 214, 222, 1)',
                pointStrokeColor    : '#c1c7d1',
                pointHighlightFill  : '#fff',
                pointHighlightStroke: 'rgba(220,220,220,1)',
                data                : Object.values(secondBarData)
            },
        ]
    };

    let barChartCanvas = $(element).get(0).getContext('2d');
    let barChartData = $.extend(true, {}, dateChartData);
    barChartData.datasets[0] = dateChartData.datasets[0];
    barChartData.datasets[1] = dateChartData.datasets[1];

    let barChartOptions = {
        responsive              : true,
        maintainAspectRatio     : false,
        datasetFill             : false
    };

    new Chart(barChartCanvas, {
        type: 'bar',
        data: barChartData,
        options: barChartOptions
    });
}

function addLineChart(element, title, labels, firstBarData, secondBarData)
{

    let lineChartData = {
        labels  : Object.keys(labels),
        datasets: [
            {
                label: 'Revenue',
                backgroundColor     : 'rgba(60,141,188,0.9)',
                borderColor         : 'rgba(60,141,188,0.8)',
                pointColor          : '#3b8bba',
                pointStrokeColor    : 'rgba(60,141,188,1)',
                pointHighlightFill  : '#fff',
                pointHighlightStroke: 'rgba(60,141,188,1)',
                data: Object.values(firstBarData),
                yAxisID: 'y',
                fill: false
            },
            {
                label: title,
                backgroundColor     : 'rgba(210, 214, 222, 1)',
                borderColor         : 'rgba(210, 214, 222, 1)',
                pointColor          : 'rgba(210, 214, 222, 1)',
                pointStrokeColor    : '#c1c7d1',
                pointHighlightFill  : '#fff',
                pointHighlightStroke: 'rgba(220,220,220,1)',
                data: Object.values(secondBarData),
                yAxisID: 'y1',
                fill: false
            }
        ]
    };

    let lineChartCanvas = $(element).get(0).getContext('2d');

    let lineChartOptions = {
        responsive              : true,
        maintainAspectRatio     : false,
        datasetFill             : false,
        intersect               : false,
        tooltips: {
            mode: 'x',
            callbacks: {
                label: function(tooltipItem, data) {
                    var label = data.datasets[tooltipItem.datasetIndex].label || '';

                    if (label) {
                        label += ': ';
                    }
                    label += new Intl.NumberFormat('el-GR').format(tooltipItem.yLabel);
                    return label;
                }
            }
        },
        scales: {
            yAxes: [{
                id: 'y',
                type: 'linear',
                position: 'left',
            }, {
                id: 'y1',
                type: 'linear',
                position: 'right',
            }]
        },
        elements: {
            line: {
                tension: 0, // disables bezier curves
            }
        },
    };

    new Chart(lineChartCanvas, {
        type: 'line',
        data: lineChartData,
        options: lineChartOptions
    });
}
function addPieChart(element, labels, datasetData)
{
    var pieData = {
        labels: labels,
        datasets: [
            {
                data: datasetData,
                backgroundColor : [
                    '#8F3D09', '#337A0E', '#24253D', '#A863DB', '#260FC2', '#B05C1B', '#5186E4', '#DB14B9', '#39F941', '#60CDFC',
                    '#8F805C', '#E6B31F', '#5AD698', '#78633D', '#ED18DC', '#009568', '#B274FA', '#808C9F', '#944B85', '#FF9059',
                    '#DCAE30', '#D31275', '#E97D50', '#474172', '#FC56C8', '#9555A4', '#73959A', '#FFB569', '#0D4AAC', '#96FCF1'
                ],
            }
        ]
    };
    //Create pie or douhnut chart
    new Chart($(element).get(0).getContext('2d'), {
        type: 'pie',
        data: pieData,
        options: {
            maintainAspectRatio : false,
            responsive : true,
        }
    });
}
