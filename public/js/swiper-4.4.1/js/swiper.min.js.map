{"version": 3, "sources": ["swiper.js"], "names": ["global", "factory", "exports", "module", "define", "amd", "Swiper", "this", "doc", "document", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "location", "hash", "win", "window", "navigator", "userAgent", "history", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "Dom7", "arr", "i", "length", "$", "selector", "context", "els", "tempParent", "html", "trim", "indexOf", "toCreate", "innerHTML", "push", "match", "split", "nodeType", "unique", "uniqueArray", "fn", "prototype", "Class", "Methods", "addClass", "className", "classes", "j", "classList", "add", "removeClass", "remove", "hasClass", "contains", "toggleClass", "toggle", "attr", "attrs", "value", "arguments$1", "arguments", "getAttribute", "attrName", "removeAttr", "removeAttribute", "data", "key", "el", "dom7ElementDataStorage", "dataKey", "transform", "elStyle", "webkitTransform", "transition", "duration", "webkitTransitionDuration", "transitionDuration", "on", "assign", "args", "len", "eventType", "targetSelector", "listener", "capture", "handleLiveEvent", "e", "target", "eventData", "dom7EventData", "unshift", "is", "apply", "parents", "k", "handleEvent", "undefined", "events", "event$1", "dom7LiveListeners", "proxyListener", "event", "dom7Listeners", "off", "handlers", "handler", "splice", "trigger", "evt", "detail", "bubbles", "cancelable", "filter", "dataIndex", "dispatchEvent", "transitionEnd", "callback", "dom", "fireCallBack", "call", "outerWidth", "<PERSON><PERSON><PERSON><PERSON>", "styles", "offsetWidth", "parseFloat", "outerHeight", "offsetHeight", "offset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "css", "props", "prop", "each", "text", "textContent", "compareWith", "matches", "webkitMatchesSelector", "msMatchesSelector", "index", "child", "previousSibling", "eq", "returnIndex", "append", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "prepend", "this$1", "insertBefore", "next", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "prev", "previousElementSibling", "prevAll", "prevEls", "parent", "parentNode", "closest", "find", "foundElements", "found", "<PERSON><PERSON><PERSON><PERSON>", "toAdd", "Object", "keys", "for<PERSON>ach", "methodName", "testDiv", "Utils", "deleteProps", "obj", "object", "nextTick", "delay", "now", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "WebKitCSSMatrix", "map", "a", "replace", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "toString", "m41", "m42", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "params", "param", "query", "urlToParse", "href", "paramsPart", "decodeURIComponent", "isObject", "o", "constructor", "extend", "len$1", "to", "nextSource", "keysArray", "nextIndex", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "enumerable", "Support", "touch", "Modernizr", "DocumentTouch", "pointerEvents", "pointer<PERSON><PERSON>bled", "PointerEvent", "prefixedPointerEvents", "msPointer<PERSON><PERSON><PERSON>", "transforms3d", "csstransforms3d", "flexbox", "observer", "passiveListener", "supportsPassive", "opts", "defineProperty", "get", "gestures", "SwiperClass", "self", "eventsListeners", "eventName", "staticAccessors", "components", "configurable", "priority", "method", "once", "once<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "emit", "Array", "isArray", "slice", "useModulesParams", "instanceParams", "instance", "modules", "moduleName", "useModules", "modulesParams", "moduleParams", "modulePropName", "moduleProp", "bind", "moduleEventName", "create", "set", "use", "installModule", "name", "proto", "static", "install", "m", "concat", "defineProperties", "update", "updateSize", "width", "height", "swiper", "$el", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "size", "updateSlides", "$wrapperEl", "swiperSize", "rtl", "rtlTranslate", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "slidesNumberEvenToRows", "slideSize", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesPerColumn", "Math", "floor", "ceil", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumnFill", "max", "newSlidesGrid", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "-webkit-box-ordinal-group", "-moz-box-ordinal-group", "-ms-flex-order", "-webkit-order", "order", "slideStyles", "currentTransform", "currentWebKitTransform", "roundLengths", "swiperSlideSize", "centeredSlides", "abs", "slidesPerGroup", "effect", "setWrapperSize", "i$1", "slidesGridItem", "i$2", "slidesGridItem$1", "centerInsufficientSlides", "allSlidesSize", "slideSizeValue", "allSlidesOffset", "snap", "snapIndex", "watchOverflow", "checkOverflow", "watchSlidesProgress", "watchSlidesVisibility", "updateSlidesOffset", "updateAutoHeight", "speed", "activeSlides", "newHeight", "setTransition", "activeIndex", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "translate", "offsetCenter", "slideVisibleClass", "visibleSlidesIndexes", "visibleSlides", "slideProgress", "minTranslate", "slideBefore", "slideAfter", "progress", "updateProgress", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "wasBeginning", "wasEnd", "updateSlidesClasses", "activeSlide", "realIndex", "slideActiveClass", "loop", "slideDuplicateClass", "slideDuplicateActiveClass", "nextSlide", "slideNextClass", "prevSlide", "slidePrevClass", "slideDuplicateNextClass", "slideDuplicatePrevClass", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "normalizeSlideIndex", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "x", "y", "previousTranslate", "transition$1", "transitionStart", "runCallbacks", "direction", "autoHeight", "dir", "animating", "slideTo", "internal", "slideIndex", "preventInteractionOnTransition", "initialSlide", "initialized", "allowSlideNext", "allowSlidePrev", "onSlideToWrapperTransitionEnd", "destroyed", "slideToLoop", "newIndex", "loopedSlides", "slideNext", "loopFix", "_clientLeft", "slidePrev", "normalize", "val", "prevIndex", "normalizedTranslate", "normalizedSnapGrid", "prevSnap", "slideReset", "slideToClosest", "currentSnap", "slidesPerViewDynamic", "slideToIndex", "loopCreate", "loopFillGroupWithBlank", "blankSlidesNum", "blankNode", "loopAdditionalSlides", "prependSlides", "appendSlides", "cloneNode", "diff", "loop<PERSON><PERSON><PERSON>", "grabCursor", "setGrabCursor", "moving", "simulate<PERSON>ouch", "isLocked", "cursor", "unsetGrabCursor", "manipulation", "appendSlide", "prependSlide", "addSlide", "activeIndexBuffer", "baseLength", "slidesBuffer", "currentSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "<PERSON><PERSON>", "ua", "device", "ios", "android", "androidChrome", "desktop", "windows", "iphone", "ipod", "ipad", "<PERSON><PERSON>", "phonegap", "os", "osVersion", "toLowerCase", "webView", "osVersionArr", "metaViewport", "minimalUi", "pixelRatio", "devicePixelRatio", "onResize", "breakpoints", "setBreakpoint", "freeMode", "newTranslate", "min", "attachEvents", "touchEvents", "wrapperEl", "onTouchStart", "touchEventsData", "touches", "originalEvent", "isTouchEvent", "type", "which", "button", "isTouched", "isMoved", "noSwiping", "noSwipingSelector", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "targetTouches", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "threshold", "allowThresholdMove", "preventDefault", "formElements", "allowTouchMove", "touchStartPreventDefault", "onTouchMove", "preventedByNestedSwiper", "touchReleaseOnEdges", "touchAngle", "diffX", "diffY", "sqrt", "pow", "atan2", "PI", "touchMoveStopPropagation", "nested", "stopPropagation", "startTranslate", "allowMomentumBounce", "touchRatio", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "velocities", "position", "time", "onTouchEnd", "currentPos", "touchEndTime", "timeDiff", "lastClickTime", "clickTimeout", "freeModeMomentum", "lastMoveEvent", "pop", "velocityEvent", "distance", "velocity", "freeModeMinimumVelocity", "freeModeMomentumVelocityRatio", "momentumDuration", "freeModeMomentumRatio", "momentumDistance", "newPosition", "afterBouncePosition", "needsLoopFix", "doBounce", "bounceAmount", "freeModeMomentumBounceRatio", "freeModeMomentumBounce", "freeModeSticky", "longSwipesMs", "stopIndex", "groupSize", "ratio", "longSwipes", "longSwipesRatio", "shortSwipes", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "touchEventsTarget", "start", "passiveListeners", "passive", "move", "end", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakPointsParams", "originalParams", "needsReLoop", "points", "point", "sort", "b", "breakpointsInverse", "innerWidth", "Browser", "isIE", "isEdge", "<PERSON><PERSON><PERSON><PERSON>", "isUiWebView", "test", "defaults", "init", "uniqueNavElements", "preloadImages", "updateOnImagesReady", "noSwipingClass", "containerModifierClass", "slideClass", "slideBlankClass", "wrapperClass", "runCallbacksOnInit", "prototypes", "wasLocked", "navigation", "addClasses", "classNames", "suffixes", "suffix", "removeClasses", "images", "loadImage", "imageEl", "src", "srcset", "sizes", "checkForComplete", "image", "onReady", "complete", "onload", "onerror", "imagesLoaded", "imagesToLoad", "currentSrc", "extendedDefaults", "SwiperClass$$1", "prototypeGroup", "protoMethod", "moduleParamName", "swiperParams", "passedParams", "swipers", "containerEl", "newParams", "touchEventsTouch", "touchEventsDesktop", "__proto__", "spv", "breakLoop", "translateValue", "destroy", "deleteInstance", "cleanStyles", "extendDefaults", "newDefaults", "Device$1", "Support$1", "support", "Browser$1", "browser", "Resize", "resize", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "Observer", "func", "MutationObserver", "WebkitMutationObserver", "attach", "options", "mutations", "observerUpdate", "requestAnimationFrame", "observe", "attributes", "childList", "characterData", "observers", "observeParents", "containerParents", "disconnect", "Observer$1", "Virtual", "force", "ref", "ref$1", "addSlidesBefore", "addSlidesAfter", "ref$2", "previousFrom", "from", "previousTo", "previousSlidesGrid", "renderSlide", "previousOffset", "offsetProp", "slidesAfter", "slidesBefore", "onRendered", "lazy", "load", "renderExternal", "slidesToRender", "prependIndexes", "appendIndexes", "cache", "$slideEl", "newCache", "cachedIndex", "Virtual$1", "beforeInit", "overwriteParams", "Keyboard", "handle", "kc", "keyCode", "charCode", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "keyboard", "onlyInViewport", "inView", "windowWidth", "windowHeight", "innerHeight", "swiperOffset", "swiperCoord", "returnValue", "enable", "disable", "Keyboard$1", "Mousewheel", "lastScrollTime", "isSupported", "element", "implementation", "hasFeature", "isEventSupported", "sX", "sY", "pX", "pY", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "handleMouseEnter", "mouseEntered", "handleMouseLeave", "mousewheel", "releaseOnEdges", "delta", "rtlFactor", "forceToAxis", "invert", "sensitivity", "timeout", "autoplay", "autoplayDisableOnInteraction", "stop", "getTime", "eventsTarged", "Navigation", "$nextEl", "$prevEl", "disabledClass", "lockClass", "nextEl", "prevEl", "Pagination", "pagination", "current", "total", "paginationType", "bullets", "firstIndex", "lastIndex", "midIndex", "dynamicBullets", "bulletSize", "dynamicMainBullets", "dynamicBulletIndex", "bullet", "$bullet", "bulletIndex", "bulletActiveClass", "$firstDisplayedBullet", "$lastDisplayedBullet", "dynamicBulletsLength", "bulletsOffset", "formatFractionCurrent", "formatFractionTotal", "progressbarDirection", "progressbarOpposite", "scale", "scaleX", "scaleY", "renderCustom", "render", "paginationHTML", "numberOfBullets", "renderBullet", "bulletClass", "renderFraction", "currentClass", "totalClass", "renderProgressbar", "progressbarFillClass", "clickable", "clickableClass", "modifierClass", "progressbarOppositeClass", "hiddenClass", "Sc<PERSON><PERSON>", "scrollbar", "dragSize", "trackSize", "$dragEl", "newSize", "newPos", "hide", "opacity", "divider", "moveDivider", "display", "scrollbarHide", "setDragPosition", "positionRatio", "clientX", "clientY", "onDragStart", "dragTimeout", "onDragMove", "onDragEnd", "snapOnRelease", "enableDraggable", "activeListener", "disableDraggable", "$swiperEl", "dragEl", "draggable", "Parallax", "setTransform", "p", "currentOpacity", "currentScale", "parallax", "slideEl", "parallaxEl", "$parallaxEl", "parallaxDuration", "Zoom", "getDistanceBetweenTouches", "x1", "y1", "x2", "y2", "onGestureStart", "zoom", "gesture", "fakeGestureTouched", "fakeGestureMoved", "scaleStart", "$imageEl", "$imageWrapEl", "maxRatio", "isScaling", "onGestureChange", "scaleMove", "minRatio", "onGestureEnd", "changedTouches", "touchesStart", "slideWidth", "slideHeight", "scaledWidth", "scaledHeight", "minX", "maxX", "minY", "maxY", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "onTransitionEnd", "out", "in", "touchX", "touchY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "Lazy", "loadInSlide", "loadInDuplicate", "$images", "elementClass", "loadedClass", "loadingClass", "imageIndex", "background", "slideOriginalIndex", "originalSlide", "duplicatedSlide", "slideExist", "initialImageLoaded", "elIndex", "loadPrevNext", "loadPrevNextAmount", "amount", "maxIndex", "minIndex", "Controller", "LinearSpline", "guess", "i1", "i3", "binarySearch", "array", "interpolate", "getInterpolateFunction", "c", "controller", "spline", "setTranslate$1", "multiplier", "controlledTranslate", "controlled", "control", "setControlledTranslate", "by", "inverse", "setControlledTransition", "a11y", "makeElFocusable", "addElRole", "role", "addElLabel", "label", "disableEl", "enableEl", "onEnterKey", "$targetEl", "notify", "lastSlideMessage", "nextSlideMessage", "firstSlideMessage", "prevSlideMessage", "click", "message", "notification", "liveRegion", "updateNavigation", "updatePagination", "bulletEl", "$bulletEl", "paginationBulletMessage", "History", "pushState", "hashNavigation", "paths", "get<PERSON>ath<PERSON><PERSON><PERSON>", "scrollToSlide", "replaceState", "setHistoryPopState", "pathArray", "pathname", "part", "setHistory", "slugify", "includes", "currentState", "state", "HashNavigation", "onHashCange", "newHash", "setHash", "watchState", "Autoplay", "run", "$activeSlideEl", "reverseDirection", "stopOnLastSlide", "running", "pause", "paused", "waitForTransition", "Fade", "tx", "ty", "slideOpacity", "fadeEffect", "crossFade", "eventTriggered", "triggerEvents", "C<PERSON>", "$cubeShadowEl", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "cubeEffect", "wrapperRotate", "shadow", "slideAngle", "round", "tz", "slideShadows", "shadowBefore", "shadowAfter", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "transform-origin", "shadowOffset", "shadowAngle", "sin", "cos", "scale1", "shadowScale", "scale2", "zFactor", "Flip", "flipEffect", "limitRotation", "rotateY", "rotateX", "zIndex", "Coverflow", "coverflowEffect", "center", "rotate", "depth", "offsetMultiplier", "modifier", "translateZ", "stretch", "slideTransform", "$shadowBeforeEl", "$shadowAfterEl", "<PERSON><PERSON><PERSON><PERSON>", "Thumbs", "thumbsParams", "thumbs", "swiperCreated", "thumbsContainerClass", "onThumbClick", "thumbsSwiper", "currentIndex", "initial", "newThumbsIndex", "currentThumbsIndex", "prevThumbsIndex", "nextThumbsIndex", "thumbsToActivate", "thumbActiveClass", "slideThumbActiveClass", "hideOnClick", "toEdge", "fromEdge", "bulletElement", "number", "activeIndexChange", "snapIndexChange", "slidesLengthChange", "snapGridLengthChange", "dragClass", "containerClass", "zoomedSlideClass", "touchStart", "touchEnd", "doubleTap", "loadOnTransitionStart", "preloaderClass", "scroll", "scrollbarDragMove", "notificationClass", "paginationUpdate", "disableOnInteraction", "beforeTransitionStart", "slider<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideChange", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;CAYC,SAAUA,EAAQC,GACE,iBAAZC,SAA0C,oBAAXC,OAAyBA,OAAOD,QAAUD,IAC9D,mBAAXG,QAAyBA,OAAOC,IAAMD,OAAOH,GACnDD,EAAOM,OAASL,IAHnB,CAIEM,KAAM,WAAe,aAarB,IAAIC,EAA2B,oBAAbC,SAA4B,CAC5CC,KAAM,GACNC,iBAAkB,aAClBC,oBAAqB,aACrBC,cAAe,CACbC,KAAM,aACNC,SAAU,IAEZC,cAAe,WACb,OAAO,MAETC,iBAAkB,WAChB,MAAO,IAETC,eAAgB,WACd,OAAO,MAETC,YAAa,WACX,MAAO,CACLC,UAAW,eAGfC,cAAe,WACb,MAAO,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,aAAc,aACdC,qBAAsB,WACpB,MAAO,MAIbC,SAAU,CAAEC,KAAM,KAChBnB,SAEAoB,EAAyB,oBAAXC,OAA0B,CAC1CrB,SAAUD,EACVuB,UAAW,CACTC,UAAW,IAEbL,SAAU,GACVM,QAAS,GACTC,YAAa,WACX,OAAO3B,MAETI,iBAAkB,aAClBC,oBAAqB,aACrBuB,iBAAkB,WAChB,MAAO,CACLC,iBAAkB,WAChB,MAAO,MAIbC,MAAO,aACPC,KAAM,aACNC,OAAQ,GACRC,WAAY,aACZC,aAAc,cACZX,OAgBAY,EAAO,SAAcC,GAGvB,IAFA,IAESC,EAAI,EAAGA,EAAID,EAAIE,OAAQD,GAAK,EAF1BrC,KAGJqC,GAAKD,EAAIC,GAIhB,OAPWrC,KAKNsC,OAASF,EAAIE,OAEXtC,MAGT,SAASuC,EAAEC,EAAUC,GACnB,IAAIL,EAAM,GACNC,EAAI,EACR,GAAIG,IAAaC,GACXD,aAAoBL,EACtB,OAAOK,EAGX,GAAIA,EAEF,GAAwB,iBAAbA,EAAuB,CAChC,IAAIE,EACAC,EACAC,EAAOJ,EAASK,OACpB,GAAyB,GAArBD,EAAKE,QAAQ,MAAkC,GAArBF,EAAKE,QAAQ,KAAW,CACpD,IAAIC,EAAW,MAQf,IAP4B,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,MAChB,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,SAChB,IAAxBH,EAAKE,QAAQ,QAAwC,IAAxBF,EAAKE,QAAQ,SAAgBC,EAAW,MAC1C,IAA3BH,EAAKE,QAAQ,YAAmBC,EAAW,SACf,IAA5BH,EAAKE,QAAQ,aAAoBC,EAAW,WAChDJ,EAAa1C,EAAIa,cAAciC,IACpBC,UAAYJ,EAClBP,EAAI,EAAGA,EAAIM,EAAW3B,WAAWsB,OAAQD,GAAK,EACjDD,EAAIa,KAAKN,EAAW3B,WAAWqB,SAUjC,IAFEK,EALGD,GAA2B,MAAhBD,EAAS,IAAeA,EAASU,MAAM,aAK9CT,GAAWxC,GAAKS,iBAAiB8B,EAASK,QAH3C,CAAC5C,EAAIU,eAAe6B,EAASK,OAAOM,MAAM,KAAK,KAKlDd,EAAI,EAAGA,EAAIK,EAAIJ,OAAQD,GAAK,EAC3BK,EAAIL,IAAMD,EAAIa,KAAKP,EAAIL,SAG1B,GAAIG,EAASY,UAAYZ,IAAalB,GAAOkB,IAAavC,EAE/DmC,EAAIa,KAAKT,QACJ,GAAsB,EAAlBA,EAASF,QAAcE,EAAS,GAAGY,SAE5C,IAAKf,EAAI,EAAGA,EAAIG,EAASF,OAAQD,GAAK,EACpCD,EAAIa,KAAKT,EAASH,IAIxB,OAAO,IAAIF,EAAKC,GAOlB,SAASiB,EAAOjB,GAEd,IADA,IAAIkB,EAAc,GACTjB,EAAI,EAAGA,EAAID,EAAIE,OAAQD,GAAK,GACE,IAAjCiB,EAAYR,QAAQV,EAAIC,KAAciB,EAAYL,KAAKb,EAAIC,IAEjE,OAAOiB,EATTf,EAAEgB,GAAKpB,EAAKqB,UACZjB,EAAEkB,MAAQtB,EACVI,EAAEJ,KAAOA,EAkqBT,IAAIuB,EAAU,CACZC,SAxpBF,SAAkBC,GAGhB,QAAyB,IAAdA,EACT,OAAO5D,KAGT,IADA,IAAI6D,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACX,IARhB9D,KAQS8D,SAAqD,IAR9D9D,KAQ6C8D,GAAGC,WARhD/D,KAQoF8D,GAAGC,UAAUC,IAAIH,EAAQxB,IAG1H,OAAOrC,MA6oBPiE,YA3oBF,SAAqBL,GAInB,IAHA,IAEIC,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACX,IALhB9D,KAKS8D,SAAqD,IAL9D9D,KAK6C8D,GAAGC,WALhD/D,KAKoF8D,GAAGC,UAAUG,OAAOL,EAAQxB,IAG7H,OAAOrC,MAmoBPmE,SAjoBF,SAAkBP,GAChB,QAAK5D,KAAK,IACHA,KAAK,GAAG+D,UAAUK,SAASR,IAgoBlCS,YA9nBF,SAAqBT,GAInB,IAHA,IAEIC,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACX,IALhB9D,KAKS8D,SAAqD,IAL9D9D,KAK6C8D,GAAGC,WALhD/D,KAKoF8D,GAAGC,UAAUO,OAAOT,EAAQxB,IAG7H,OAAOrC,MAsnBPuE,KApnBF,SAAcC,EAAOC,GACnB,IAAIC,EAAcC,UAGlB,GAAyB,IAArBA,UAAUrC,QAAiC,iBAAVkC,EAEnC,OAAIxE,KAAK,GAAaA,KAAK,GAAG4E,aAAaJ,QAC3C,EAIF,IAAK,IAAInC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpC,GAA2B,IAAvBqC,EAAYpC,OAVLtC,KAYFqC,GAAGnB,aAAasD,EAAOC,QAI9B,IAAK,IAAII,KAAYL,EAhBZxE,KAiBAqC,GAAGwC,GAAYL,EAAMK,GAjBrB7E,KAkBAqC,GAAGnB,aAAa2D,EAAUL,EAAMK,IAI7C,OAAO7E,MA6lBP8E,WA1lBF,SAAoBP,GAGlB,IAFA,IAESlC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAFzBrC,KAGJqC,GAAG0C,gBAAgBR,GAE5B,OAAOvE,MAqlBPgF,KAnlBF,SAAcC,EAAKR,GACjB,IAEIS,EACJ,QAAqB,IAAVT,EAAX,CAkBA,IAAK,IAAIpC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,GACpC6C,EAtBWlF,KAsBCqC,IACJ8C,yBAA0BD,EAAGC,uBAAyB,IAC9DD,EAAGC,uBAAuBF,GAAOR,EAEnC,OAAOzE,KApBL,GAFAkF,EAAKlF,KAAK,GAEF,CACN,GAAIkF,EAAGC,wBAA2BF,KAAOC,EAAGC,uBAC1C,OAAOD,EAAGC,uBAAuBF,GAGnC,IAAIG,EAAUF,EAAGN,aAAc,QAAUK,GACzC,OAAIG,QAGJ,IAokBJC,UArjBF,SAAmBA,GAGjB,IAFA,IAEShD,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAIiD,EAHOtF,KAGUqC,GAAGpB,MACxBqE,EAAQC,gBAAkBF,EAC1BC,EAAQD,UAAYA,EAEtB,OAAOrF,MA8iBPwF,WA5iBF,SAAoBC,GAGM,iBAAbA,IACTA,GAAsB,MAExB,IAAK,IAAIpD,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAIiD,EANOtF,KAMUqC,GAAGpB,MACxBqE,EAAQI,yBAA2BD,EACnCH,EAAQK,mBAAqBF,EAE/B,OAAOzF,MAkiBP4F,GA/hBF,WAKE,IAJA,IACIC,EAEAC,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GAOnB,SAASM,EAAgBC,GACvB,IAAIC,EAASD,EAAEC,OACf,GAAKA,EAAL,CACA,IAAIC,EAAYF,EAAEC,OAAOE,eAAiB,GAI1C,GAHID,EAAUzD,QAAQuD,GAAK,GACzBE,EAAUE,QAAQJ,GAEhB9D,EAAE+D,GAAQI,GAAGT,GAAmBC,EAASS,MAAML,EAAQC,QAGzD,IADA,IAAIK,EAAUrE,EAAE+D,GAAQM,UACfC,EAAI,EAAGA,EAAID,EAAQtE,OAAQuE,GAAK,EACnCtE,EAAEqE,EAAQC,IAAIH,GAAGT,IAAmBC,EAASS,MAAMC,EAAQC,GAAIN,IAIzE,SAASO,EAAYT,GACnB,IAAIE,EAAYF,GAAKA,EAAEC,QAASD,EAAEC,OAAOE,eAAsB,GAC3DD,EAAUzD,QAAQuD,GAAK,GACzBE,EAAUE,QAAQJ,GAEpBH,EAASS,MAAM3G,KAAMuG,GA1BA,mBAAZT,EAAK,KACEE,GAAfH,EAASC,GAAyB,GAAII,EAAWL,EAAO,GAAIM,EAAUN,EAAO,GAC9EI,OAAiBc,GAEdZ,IAAWA,GAAU,GA0B1B,IAFA,IACIrC,EADAkD,EAAShB,EAAU7C,MAAM,KAEpBd,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAI6C,EAxCOlF,KAwCKqC,GAChB,GAAK4D,EAaH,IAAKnC,EAAI,EAAGA,EAAIkD,EAAO1E,OAAQwB,GAAK,EAAG,CACrC,IAAImD,EAAUD,EAAOlD,GAChBoB,EAAGgC,oBAAqBhC,EAAGgC,kBAAoB,IAC/ChC,EAAGgC,kBAAkBD,KAAY/B,EAAGgC,kBAAkBD,GAAW,IACtE/B,EAAGgC,kBAAkBD,GAAShE,KAAK,CACjCiD,SAAUA,EACViB,cAAef,IAEjBlB,EAAG9E,iBAAiB6G,EAASb,EAAiBD,QApBhD,IAAKrC,EAAI,EAAGA,EAAIkD,EAAO1E,OAAQwB,GAAK,EAAG,CACrC,IAAIsD,EAAQJ,EAAOlD,GACdoB,EAAGmC,gBAAiBnC,EAAGmC,cAAgB,IACvCnC,EAAGmC,cAAcD,KAAUlC,EAAGmC,cAAcD,GAAS,IAC1DlC,EAAGmC,cAAcD,GAAOnE,KAAK,CAC3BiD,SAAUA,EACViB,cAAeL,IAEjB5B,EAAG9E,iBAAiBgH,EAAON,EAAaX,IAgB9C,OAAOnG,MA6dPsH,IA3dF,WAKE,IAJA,IACIzB,EAEAC,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GACI,mBAAZA,EAAK,KACEE,GAAfH,EAASC,GAAyB,GAAII,EAAWL,EAAO,GAAIM,EAAUN,EAAO,GAC9EI,OAAiBc,GAEdZ,IAAWA,GAAU,GAG1B,IADA,IAAIa,EAAShB,EAAU7C,MAAM,KACpBd,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAEtC,IADA,IAAI+E,EAAQJ,EAAO3E,GACVyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,EAAG,CACvC,IAAIoB,EAnBKlF,KAmBO8D,GACZyD,OAAW,EAMf,IALKtB,GAAkBf,EAAGmC,cACxBE,EAAWrC,EAAGmC,cAAcD,GACnBnB,GAAkBf,EAAGgC,oBAC9BK,EAAWrC,EAAGgC,kBAAkBE,IAE9BG,GAAYA,EAASjF,OACvB,IAAK,IAAIuE,EAAIU,EAASjF,OAAS,EAAQ,GAALuE,EAAQA,GAAK,EAAG,CAChD,IAAIW,EAAUD,EAASV,GACnBX,GAAYsB,EAAQtB,WAAaA,GACnChB,EAAG7E,oBAAoB+G,EAAOI,EAAQL,cAAehB,GACrDoB,EAASE,OAAOZ,EAAG,IACTX,IACVhB,EAAG7E,oBAAoB+G,EAAOI,EAAQL,cAAehB,GACrDoB,EAASE,OAAOZ,EAAG,KAM7B,OAAO7G,MAmbP0H,QAjbF,WAGE,IAFA,IACI5B,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAIzC,IAFA,IAAIiB,EAASlB,EAAK,GAAG3C,MAAM,KACvBoD,EAAYT,EAAK,GACZzD,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAEtC,IADA,IAAI+E,EAAQJ,EAAO3E,GACVyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,EAAG,CACvC,IAAIoB,EATKlF,KASO8D,GACZ6D,OAAM,EACV,IACEA,EAAM,IAAIrG,EAAIK,YAAYyF,EAAO,CAC/BQ,OAAQrB,EACRsB,SAAS,EACTC,YAAY,IAEd,MAAOzB,IACPsB,EAAM1H,EAAIW,YAAY,UAClBC,UAAUuG,GAAO,GAAM,GAC3BO,EAAIC,OAASrB,EAGfrB,EAAGsB,cAAgBV,EAAKiC,OAAO,SAAU/C,EAAMgD,GAAa,OAAmB,EAAZA,IACnE9C,EAAG+C,cAAcN,GACjBzC,EAAGsB,cAAgB,UACZtB,EAAGsB,cAGd,OAAOxG,MAoZPkI,cAlZF,SAAuBC,GACrB,IAEI9F,EAFA2E,EAAS,CAAC,sBAAuB,iBACjCoB,EAAMpI,KAEV,SAASqI,EAAahC,GAEpB,GAAIA,EAAEC,SAAWtG,KAEjB,IADAmI,EAASG,KAAKtI,KAAMqG,GACfhE,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAClC+F,EAAId,IAAIN,EAAO3E,GAAIgG,GAGvB,GAAIF,EACF,IAAK9F,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAClC+F,EAAIxC,GAAGoB,EAAO3E,GAAIgG,GAGtB,OAAOrI,MAkYPuI,WAhYF,SAAoBC,GAClB,GAAkB,EAAdxI,KAAKsC,OAAY,CACnB,GAAIkG,EAAgB,CAElB,IAAIC,EAASzI,KAAKyI,SAClB,OAAOzI,KAAK,GAAG0I,YAAcC,WAAWF,EAAO5G,iBAAiB,iBAAmB8G,WAAWF,EAAO5G,iBAAiB,gBAExH,OAAO7B,KAAK,GAAG0I,YAEjB,OAAO,MAwXPE,YAtXF,SAAqBJ,GACnB,GAAkB,EAAdxI,KAAKsC,OAAY,CACnB,GAAIkG,EAAgB,CAElB,IAAIC,EAASzI,KAAKyI,SAClB,OAAOzI,KAAK,GAAG6I,aAAeF,WAAWF,EAAO5G,iBAAiB,eAAiB8G,WAAWF,EAAO5G,iBAAiB,kBAEvH,OAAO7B,KAAK,GAAG6I,aAEjB,OAAO,MA8WPC,OA5WF,WACE,GAAkB,EAAd9I,KAAKsC,OAAY,CACnB,IAAI4C,EAAKlF,KAAK,GACV+I,EAAM7D,EAAG8D,wBACT7I,EAAOF,EAAIE,KACX8I,EAAY/D,EAAG+D,WAAa9I,EAAK8I,WAAa,EAC9CC,EAAahE,EAAGgE,YAAc/I,EAAK+I,YAAc,EACjDC,EAAYjE,IAAO5D,EAAMA,EAAI8H,QAAUlE,EAAGiE,UAC1CE,EAAanE,IAAO5D,EAAMA,EAAIgI,QAAUpE,EAAGmE,WAC/C,MAAO,CACLE,IAAMR,EAAIQ,IAAMJ,EAAaF,EAC7BO,KAAOT,EAAIS,KAAOH,EAAcH,GAIpC,OAAO,MA8VPO,IAxVF,SAAaC,EAAOjF,GAClB,IAEIpC,EACJ,GAAyB,IAArBsC,UAAUrC,OAAc,CAC1B,GAAqB,iBAAVoH,EAEJ,CACL,IAAKrH,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEhC,IAAK,IAAIsH,KAAQD,EATV1J,KAUEqC,GAAGpB,MAAM0I,GAAQD,EAAMC,GAGlC,OAAO3J,KARP,GAAIA,KAAK,GAAM,OAAOsB,EAAIM,iBAAiB5B,KAAK,GAAI,MAAM6B,iBAAiB6H,GAW/E,GAAyB,IAArB/E,UAAUrC,QAAiC,iBAAVoH,EAAoB,CACvD,IAAKrH,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAjBvBrC,KAkBFqC,GAAGpB,MAAMyI,GAASjF,EAE3B,OAAOzE,KAET,OAAOA,MAkUP4J,KA/TF,SAAczB,GAIZ,IAAKA,EAAY,OAAOnI,KAExB,IAAK,IAAIqC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IAA+C,IAA3C8F,EAASG,KAPFtI,KAOcqC,GAAIA,EAPlBrC,KAO4BqC,IAErC,OATSrC,KAab,OAAOA,MAkTP4C,KA/SF,SAAcA,GAGZ,QAAoB,IAATA,EACT,OAAO5C,KAAK,GAAKA,KAAK,GAAGgD,eAAY+D,EAGvC,IAAK,IAAI1E,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EANzBrC,KAOJqC,GAAGW,UAAYJ,EAExB,OAAO5C,MAsSP6J,KAnSF,SAAcA,GAGZ,QAAoB,IAATA,EACT,OAAI7J,KAAK,GACAA,KAAK,GAAG8J,YAAYjH,OAEtB,KAGT,IAAK,IAAIR,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EATzBrC,KAUJqC,GAAGyH,YAAcD,EAE1B,OAAO7J,MAuRP0G,GArRF,SAAYlE,GACV,IACIuH,EACA1H,EAFA6C,EAAKlF,KAAK,GAGd,IAAKkF,QAA0B,IAAb1C,EAA4B,OAAO,EACrD,GAAwB,iBAAbA,EAAuB,CAChC,GAAI0C,EAAG8E,QAAW,OAAO9E,EAAG8E,QAAQxH,GAC/B,GAAI0C,EAAG+E,sBAAyB,OAAO/E,EAAG+E,sBAAsBzH,GAChE,GAAI0C,EAAGgF,kBAAqB,OAAOhF,EAAGgF,kBAAkB1H,GAG7D,IADAuH,EAAcxH,EAAEC,GACXH,EAAI,EAAGA,EAAI0H,EAAYzH,OAAQD,GAAK,EACvC,GAAI0H,EAAY1H,KAAO6C,EAAM,OAAO,EAEtC,OAAO,EACF,GAAI1C,IAAavC,EAAO,OAAOiF,IAAOjF,EACxC,GAAIuC,IAAalB,EAAO,OAAO4D,IAAO5D,EAE3C,GAAIkB,EAASY,UAAYZ,aAAoBL,EAAM,CAEjD,IADA4H,EAAcvH,EAASY,SAAW,CAACZ,GAAYA,EAC1CH,EAAI,EAAGA,EAAI0H,EAAYzH,OAAQD,GAAK,EACvC,GAAI0H,EAAY1H,KAAO6C,EAAM,OAAO,EAEtC,OAAO,EAET,OAAO,GA6PPiF,MA3PF,WACE,IACI9H,EADA+H,EAAQpK,KAAK,GAEjB,GAAIoK,EAAO,CAGT,IAFA/H,EAAI,EAEuC,QAAnC+H,EAAQA,EAAMC,kBACG,IAAnBD,EAAMhH,WAAkBf,GAAK,GAEnC,OAAOA,IAmPTiI,GA9OF,SAAYH,GACV,QAAqB,IAAVA,EAAyB,OAAOnK,KAC3C,IACIuK,EADAjI,EAAStC,KAAKsC,OAElB,OACS,IAAIH,EADDG,EAAS,EAAjB6H,EACc,GAEdA,EAAQ,GACVI,EAAcjI,EAAS6H,GACL,EAAqB,GACvB,CAACnK,KAAKuK,IAER,CAACvK,KAAKmK,MAmOtBK,OAjOF,WAGE,IAFA,IAIIC,EAHA3E,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAIzC,IAAK,IAAIc,EAAI,EAAGA,EAAIf,EAAKxD,OAAQuE,GAAK,EAAG,CACvC4D,EAAW3E,EAAKe,GAChB,IAAK,IAAIxE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpC,GAAwB,iBAAboI,EAAuB,CAChC,IAAIC,EAAUzK,EAAIa,cAAc,OAEhC,IADA4J,EAAQ1H,UAAYyH,EACbC,EAAQC,YAZR3K,KAaEqC,GAAGuI,YAAYF,EAAQC,iBAE3B,GAAIF,aAAoBtI,EAC7B,IAAK,IAAI2B,EAAI,EAAGA,EAAI2G,EAASnI,OAAQwB,GAAK,EAhBnC9D,KAiBEqC,GAAGuI,YAAYH,EAAS3G,SAjB1B9D,KAoBAqC,GAAGuI,YAAYH,GAK5B,OAAOzK,MAwMP6K,QAtMF,SAAiBJ,GACf,IAEIpI,EACAyB,EAHAgH,EAAS9K,KAIb,IAAKqC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAChC,GAAwB,iBAAboI,EAAuB,CAChC,IAAIC,EAAUzK,EAAIa,cAAc,OAEhC,IADA4J,EAAQ1H,UAAYyH,EACf3G,EAAI4G,EAAQ1J,WAAWsB,OAAS,EAAQ,GAALwB,EAAQA,GAAK,EACnDgH,EAAOzI,GAAG0I,aAAaL,EAAQ1J,WAAW8C,GAAIgH,EAAOzI,GAAGrB,WAAW,SAEhE,GAAIyJ,aAAoBtI,EAC7B,IAAK2B,EAAI,EAAGA,EAAI2G,EAASnI,OAAQwB,GAAK,EACpCgH,EAAOzI,GAAG0I,aAAaN,EAAS3G,GAAIgH,EAAOzI,GAAGrB,WAAW,SAG3D8J,EAAOzI,GAAG0I,aAAaN,EAAUK,EAAOzI,GAAGrB,WAAW,IAG1D,OAAOhB,MAmLPgL,KAjLF,SAAcxI,GACZ,OAAkB,EAAdxC,KAAKsC,OACHE,EACExC,KAAK,GAAGiL,oBAAsB1I,EAAEvC,KAAK,GAAGiL,oBAAoBvE,GAAGlE,GAC1D,IAAIL,EAAK,CAACnC,KAAK,GAAGiL,qBAEpB,IAAI9I,EAAK,IAGdnC,KAAK,GAAGiL,mBAA6B,IAAI9I,EAAK,CAACnC,KAAK,GAAGiL,qBACpD,IAAI9I,EAAK,IAEX,IAAIA,EAAK,KAsKhB+I,QApKF,SAAiB1I,GACf,IAAI2I,EAAU,GACVjG,EAAKlF,KAAK,GACd,IAAKkF,EAAM,OAAO,IAAI/C,EAAK,IAC3B,KAAO+C,EAAG+F,oBAAoB,CAC5B,IAAID,EAAO9F,EAAG+F,mBACVzI,EACED,EAAEyI,GAAMtE,GAAGlE,IAAa2I,EAAQlI,KAAK+H,GAClCG,EAAQlI,KAAK+H,GACtB9F,EAAK8F,EAEP,OAAO,IAAI7I,EAAKgJ,IA0JhBC,KAxJF,SAAc5I,GACZ,GAAkB,EAAdxC,KAAKsC,OAAY,CACnB,IAAI4C,EAAKlF,KAAK,GACd,OAAIwC,EACE0C,EAAGmG,wBAA0B9I,EAAE2C,EAAGmG,wBAAwB3E,GAAGlE,GACxD,IAAIL,EAAK,CAAC+C,EAAGmG,yBAEf,IAAIlJ,EAAK,IAGd+C,EAAGmG,uBAAiC,IAAIlJ,EAAK,CAAC+C,EAAGmG,yBAC9C,IAAIlJ,EAAK,IAElB,OAAO,IAAIA,EAAK,KA4IhBmJ,QA1IF,SAAiB9I,GACf,IAAI+I,EAAU,GACVrG,EAAKlF,KAAK,GACd,IAAKkF,EAAM,OAAO,IAAI/C,EAAK,IAC3B,KAAO+C,EAAGmG,wBAAwB,CAChC,IAAID,EAAOlG,EAAGmG,uBACV7I,EACED,EAAE6I,GAAM1E,GAAGlE,IAAa+I,EAAQtI,KAAKmI,GAClCG,EAAQtI,KAAKmI,GACtBlG,EAAKkG,EAEP,OAAO,IAAIjJ,EAAKoJ,IAgIhBC,OA9HF,SAAgBhJ,GAId,IAHA,IAEIoE,EAAU,GACLvE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACP,OAJlBrC,KAIAqC,GAAGoJ,aACRjJ,EACED,EANGvC,KAMMqC,GAAGoJ,YAAY/E,GAAGlE,IAAaoE,EAAQ3D,KAN7CjD,KAMyDqC,GAAGoJ,YAEnE7E,EAAQ3D,KARDjD,KAQaqC,GAAGoJ,aAI7B,OAAOlJ,EAAEc,EAAOuD,KAkHhBA,QAhHF,SAAiBpE,GAIf,IAHA,IAEIoE,EAAU,GACLvE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IADA,IAAImJ,EAJOxL,KAISqC,GAAGoJ,WAChBD,GACDhJ,EACED,EAAEiJ,GAAQ9E,GAAGlE,IAAaoE,EAAQ3D,KAAKuI,GAE3C5E,EAAQ3D,KAAKuI,GAEfA,EAASA,EAAOC,WAGpB,OAAOlJ,EAAEc,EAAOuD,KAkGhB8E,QAhGF,SAAiBlJ,GACf,IAAIkJ,EAAU1L,KACd,YAAwB,IAAbwC,EACF,IAAIL,EAAK,KAEbuJ,EAAQhF,GAAGlE,KACdkJ,EAAUA,EAAQ9E,QAAQpE,GAAU8H,GAAG,IAElCoB,IAyFPC,KAvFF,SAAcnJ,GAIZ,IAHA,IAEIoJ,EAAgB,GACXvJ,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IADA,IAAIwJ,EAJO7L,KAIQqC,GAAG3B,iBAAiB8B,GAC9BsB,EAAI,EAAGA,EAAI+H,EAAMvJ,OAAQwB,GAAK,EACrC8H,EAAc3I,KAAK4I,EAAM/H,IAG7B,OAAO,IAAI3B,EAAKyJ,IA8EhB7K,SA5EF,SAAkByB,GAIhB,IAHA,IAEIzB,EAAW,GACNsB,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAGpC,IAFA,IAAIrB,EAJOhB,KAIaqC,GAAGrB,WAElB8C,EAAI,EAAGA,EAAI9C,EAAWsB,OAAQwB,GAAK,EACrCtB,EAEiC,IAA3BxB,EAAW8C,GAAGV,UAAkBb,EAAEvB,EAAW8C,IAAI4C,GAAGlE,IAC7DzB,EAASkC,KAAKjC,EAAW8C,IAFM,IAA3B9C,EAAW8C,GAAGV,UAAkBrC,EAASkC,KAAKjC,EAAW8C,IAMnE,OAAO,IAAI3B,EAAKkB,EAAOtC,KA8DvBmD,OA5DF,WAGE,IAFA,IAES7B,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAFzBrC,KAGAqC,GAAGoJ,YAHHzL,KAGwBqC,GAAGoJ,WAAWK,YAHtC9L,KAGyDqC,IAEtE,OAAOrC,MAuDPgE,IArDF,WAEE,IADA,IAAI8B,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAEzC,IACI1D,EACAyB,EACJ,IAAKzB,EAAI,EAAGA,EAAIyD,EAAKxD,OAAQD,GAAK,EAAG,CACnC,IAAI0J,EAAQxJ,EAAEuD,EAAKzD,IACnB,IAAKyB,EAAI,EAAGA,EAAIiI,EAAMzJ,OAAQwB,GAAK,EAL3B9D,KAAAA,KAMEsC,QAAUyJ,EAAMjI,GANlB9D,KAOFsC,QAAU,EAGlB,OAVUtC,MAkDVyI,OAhXF,WACE,OAAIzI,KAAK,GAAasB,EAAIM,iBAAiB5B,KAAK,GAAI,MAC7C,KAiXTgM,OAAOC,KAAKvI,GAASwI,QAAQ,SAAUC,GACrC5J,EAAEgB,GAAG4I,GAAczI,EAAQyI,KAG7B,IAkIUlL,EAJAA,EAVJmL,EApHFC,EAAQ,CACVC,YAAa,SAAqBC,GAChC,IAAIC,EAASD,EACbP,OAAOC,KAAKO,GAAQN,QAAQ,SAAUjH,GACpC,IACEuH,EAAOvH,GAAO,KACd,MAAOoB,IAGT,WACSmG,EAAOvH,GACd,MAAOoB,QAKboG,SAAU,SAAkBtE,EAAUuE,GAGpC,YAFe,IAAVA,IAAmBA,EAAQ,GAEzBzK,WAAWkG,EAAUuE,IAE9BC,IAAK,WACH,OAAO5K,KAAK4K,OAEdC,aAAc,SAAsB1H,EAAI2H,GAGtC,IAAIC,EACAC,EACAC,OAJU,IAATH,IAAkBA,EAAO,KAM9B,IAAII,EAAW3L,EAAIM,iBAAiBsD,EAAI,MA+BxC,OA7BI5D,EAAI4L,iBAE+B,GADrCH,EAAeE,EAAS5H,WAAa4H,EAAS1H,iBAC7BpC,MAAM,KAAKb,SAC1ByK,EAAeA,EAAa5J,MAAM,MAAMgK,IAAI,SAAUC,GAAK,OAAOA,EAAEC,QAAQ,IAAK,OAASC,KAAK,OAIjGN,EAAkB,IAAI1L,EAAI4L,gBAAiC,SAAjBH,EAA0B,GAAKA,IAGzED,GADAE,EAAkBC,EAASM,cAAgBN,EAASO,YAAcP,EAASQ,aAAeR,EAASS,aAAeT,EAAS5H,WAAa4H,EAASpL,iBAAiB,aAAawL,QAAQ,aAAc,uBAC5KM,WAAWxK,MAAM,KAG/B,MAAT0J,IAEyBE,EAAvBzL,EAAI4L,gBAAkCF,EAAgBY,IAE/B,KAAlBd,EAAOxK,OAAgCqG,WAAWmE,EAAO,KAE5CnE,WAAWmE,EAAO,KAE7B,MAATD,IAEyBE,EAAvBzL,EAAI4L,gBAAkCF,EAAgBa,IAE/B,KAAlBf,EAAOxK,OAAgCqG,WAAWmE,EAAO,KAE5CnE,WAAWmE,EAAO,KAEnCC,GAAgB,GAEzBe,cAAe,SAAuBC,GACpC,IAEI1L,EACA2L,EACAC,EACA3L,EALA4L,EAAQ,GACRC,EAAaJ,GAAOzM,EAAIF,SAASgN,KAKrC,GAA0B,iBAAfD,GAA2BA,EAAW7L,OAK/C,IAFAA,GADA0L,GADAG,GAAwC,EAA3BA,EAAWrL,QAAQ,KAAYqL,EAAWd,QAAQ,QAAS,IAAM,IAC1DlK,MAAM,KAAK4E,OAAO,SAAUsG,GAAc,MAAsB,KAAfA,KACrD/L,OAEXD,EAAI,EAAGA,EAAIC,EAAQD,GAAK,EAC3B4L,EAAQD,EAAO3L,GAAGgL,QAAQ,QAAS,IAAIlK,MAAM,KAC7C+K,EAAMI,mBAAmBL,EAAM,UAA2B,IAAbA,EAAM,QAAqBlH,EAAYuH,mBAAmBL,EAAM,KAAO,GAGxH,OAAOC,GAETK,SAAU,SAAkBC,GAC1B,MAAoB,iBAANA,GAAwB,OAANA,GAAcA,EAAEC,aAAeD,EAAEC,cAAgBzC,QAEnF0C,OAAQ,WAEN,IADA,IAAI5I,EAAO,GAAI6I,EAAQhK,UAAUrC,OACzBqM,KAAU7I,EAAM6I,GAAUhK,UAAWgK,GAG7C,IADA,IAAIC,EAAK5C,OAAOlG,EAAK,IACZzD,EAAI,EAAGA,EAAIyD,EAAKxD,OAAQD,GAAK,EAAG,CACvC,IAAIwM,EAAa/I,EAAKzD,GACtB,GAAIwM,MAAAA,EAEF,IADA,IAAIC,EAAY9C,OAAOC,KAAKD,OAAO6C,IAC1BE,EAAY,EAAGhJ,EAAM+I,EAAUxM,OAAQyM,EAAYhJ,EAAKgJ,GAAa,EAAG,CAC/E,IAAIC,EAAUF,EAAUC,GACpBE,EAAOjD,OAAOkD,yBAAyBL,EAAYG,QAC1CjI,IAATkI,GAAsBA,EAAKE,aACzB9C,EAAMkC,SAASK,EAAGI,KAAa3C,EAAMkC,SAASM,EAAWG,IAC3D3C,EAAMqC,OAAOE,EAAGI,GAAUH,EAAWG,KAC3B3C,EAAMkC,SAASK,EAAGI,KAAa3C,EAAMkC,SAASM,EAAWG,KACnEJ,EAAGI,GAAW,GACd3C,EAAMqC,OAAOE,EAAGI,GAAUH,EAAWG,KAErCJ,EAAGI,GAAWH,EAAWG,KAMnC,OAAOJ,IAIPQ,GACEhD,EAAUnM,EAAIa,cAAc,OACzB,CACLuO,MAAQ/N,EAAIgO,YAAqC,IAAxBhO,EAAIgO,UAAUD,UAC1B,iBAAkB/N,GAASA,EAAIiO,eAAiBtP,aAAeqB,EAAIiO,eAGhFC,iBAAkBlO,EAAIE,UAAUiO,iBAAkBnO,EAAIoO,cACtDC,wBAAyBrO,EAAIE,UAAUoO,iBAEvCpK,YACMvE,EAAQmL,EAAQnL,MACZ,eAAgBA,GAAS,qBAAsBA,GAAS,kBAAmBA,GAErF4O,aAAevO,EAAIgO,YAA+C,IAAlChO,EAAIgO,UAAUQ,kBACxC7O,EAAQmL,EAAQnL,MACZ,sBAAuBA,GAAS,mBAAoBA,GAAS,iBAAkBA,GAAS,kBAAmBA,GAAS,gBAAiBA,GAG/I8O,QAAU,WAGR,IAFA,IAAI9O,EAAQmL,EAAQnL,MAChBwH,EAAS,yKAA2KtF,MAAM,KACrLd,EAAI,EAAGA,EAAIoG,EAAOnG,OAAQD,GAAK,EACtC,GAAIoG,EAAOpG,KAAMpB,EAAS,OAAO,EAEnC,OAAO,EANA,GAST+O,SACU,qBAAsB1O,GAAO,2BAA4BA,EAGnE2O,gBAAkB,WAChB,IAAIC,GAAkB,EACtB,IACE,IAAIC,EAAOnE,OAAOoE,eAAe,GAAI,UAAW,CAE9CC,IAAK,WACHH,GAAkB,KAGtB5O,EAAIlB,iBAAiB,sBAAuB,KAAM+P,GAClD,MAAO9J,IAGT,OAAO6J,EAbQ,GAgBjBI,SACS,mBAAoBhP,IAK7BiP,EAAc,SAAqBvC,QACrB,IAAXA,IAAoBA,EAAS,IAElC,IAAIwC,EAAOxQ,KACXwQ,EAAKxC,OAASA,EAGdwC,EAAKC,gBAAkB,GAEnBD,EAAKxC,QAAUwC,EAAKxC,OAAOpI,IAC7BoG,OAAOC,KAAKuE,EAAKxC,OAAOpI,IAAIsG,QAAQ,SAAUwE,GAC5CF,EAAK5K,GAAG8K,EAAWF,EAAKxC,OAAOpI,GAAG8K,OAKpCC,EAAkB,CAAEC,WAAY,CAAEC,cAAc,IAEpDN,EAAY/M,UAAUoC,GAAK,SAAaoB,EAAQQ,EAASsJ,GACvD,IAAIN,EAAOxQ,KACX,GAAuB,mBAAZwH,EAA0B,OAAOgJ,EAC5C,IAAIO,EAASD,EAAW,UAAY,OAKpC,OAJA9J,EAAO7D,MAAM,KAAK+I,QAAQ,SAAU9E,GAC7BoJ,EAAKC,gBAAgBrJ,KAAUoJ,EAAKC,gBAAgBrJ,GAAS,IAClEoJ,EAAKC,gBAAgBrJ,GAAO2J,GAAQvJ,KAE/BgJ,GAGTD,EAAY/M,UAAUwN,KAAO,SAAehK,EAAQQ,EAASsJ,GAC3D,IAAIN,EAAOxQ,KACX,GAAuB,mBAAZwH,EAA0B,OAAOgJ,EAQ5C,OAAOA,EAAK5K,GAAGoB,EAPf,SAASiK,IAEL,IADA,IAAInL,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAE3CyB,EAAQb,MAAM6J,EAAM1K,GACpB0K,EAAKlJ,IAAIN,EAAQiK,IAEiBH,IAGtCP,EAAY/M,UAAU8D,IAAM,SAAcN,EAAQQ,GAChD,IAAIgJ,EAAOxQ,KACX,OAAKwQ,EAAKC,iBACVzJ,EAAO7D,MAAM,KAAK+I,QAAQ,SAAU9E,QACX,IAAZI,EACTgJ,EAAKC,gBAAgBrJ,GAAS,GACrBoJ,EAAKC,gBAAgBrJ,IAAUoJ,EAAKC,gBAAgBrJ,GAAO9E,QACpEkO,EAAKC,gBAAgBrJ,GAAO8E,QAAQ,SAAUgF,EAAc/G,GACtD+G,IAAiB1J,GACnBgJ,EAAKC,gBAAgBrJ,GAAOK,OAAO0C,EAAO,OAK3CqG,GAGTD,EAAY/M,UAAU2N,KAAO,WAEzB,IADA,IAAIrL,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAE3C,IAEIiB,EACAhC,EACAvC,EAJA+N,EAAOxQ,KACX,OAAKwQ,EAAKC,kBAIa,iBAAZ3K,EAAK,IAAmBsL,MAAMC,QAAQvL,EAAK,KACpDkB,EAASlB,EAAK,GACdd,EAAOc,EAAKwL,MAAM,EAAGxL,EAAKxD,QAC1BG,EAAU+N,IAEVxJ,EAASlB,EAAK,GAAGkB,OACjBhC,EAAOc,EAAK,GAAGd,KACfvC,EAAUqD,EAAK,GAAGrD,SAAW+N,IAEbY,MAAMC,QAAQrK,GAAUA,EAASA,EAAO7D,MAAM,MACpD+I,QAAQ,SAAU9E,GAC5B,GAAIoJ,EAAKC,iBAAmBD,EAAKC,gBAAgBrJ,GAAQ,CACvD,IAAIG,EAAW,GACfiJ,EAAKC,gBAAgBrJ,GAAO8E,QAAQ,SAAUgF,GAC5C3J,EAAStE,KAAKiO,KAEhB3J,EAAS2E,QAAQ,SAAUgF,GACzBA,EAAavK,MAAMlE,EAASuC,SAI3BwL,GAGTD,EAAY/M,UAAU+N,iBAAmB,SAA2BC,GAClE,IAAIC,EAAWzR,KACVyR,EAASC,SACd1F,OAAOC,KAAKwF,EAASC,SAASxF,QAAQ,SAAUyF,GAC9C,IAAI/R,EAAS6R,EAASC,QAAQC,GAE1B/R,EAAOoO,QACT3B,EAAMqC,OAAO8C,EAAgB5R,EAAOoO,WAK1CuC,EAAY/M,UAAUoO,WAAa,SAAqBC,QAC7B,IAAlBA,IAA2BA,EAAgB,IAElD,IAAIJ,EAAWzR,KACVyR,EAASC,SACd1F,OAAOC,KAAKwF,EAASC,SAASxF,QAAQ,SAAUyF,GAC9C,IAAI/R,EAAS6R,EAASC,QAAQC,GAC1BG,EAAeD,EAAcF,IAAe,GAE5C/R,EAAO6R,UACTzF,OAAOC,KAAKrM,EAAO6R,UAAUvF,QAAQ,SAAU6F,GAC7C,IAAIC,EAAapS,EAAO6R,SAASM,GAE/BN,EAASM,GADe,mBAAfC,EACkBA,EAAWC,KAAKR,GAEhBO,IAK7BpS,EAAOgG,IAAM6L,EAAS7L,IACxBoG,OAAOC,KAAKrM,EAAOgG,IAAIsG,QAAQ,SAAUgG,GACvCT,EAAS7L,GAAGsM,EAAiBtS,EAAOgG,GAAGsM,MAKvCtS,EAAOuS,QACTvS,EAAOuS,OAAOF,KAAKR,EAAnB7R,CAA6BkS,MAKnCnB,EAAgBC,WAAWwB,IAAM,SAAUxB,GAC7B5Q,KACDqS,KADCrS,KAENqS,IAAIzB,IAGZL,EAAY+B,cAAgB,SAAwB1S,GAEhD,IADA,IAAIoO,EAAS,GAAIjI,EAAMpB,UAAUrC,OAAS,EAC1B,EAARyD,KAAYiI,EAAQjI,GAAQpB,UAAWoB,EAAM,GAEvD,IAAItC,EAAQzD,KACPyD,EAAMD,UAAUkO,UAAWjO,EAAMD,UAAUkO,QAAU,IAC1D,IAAIa,EAAO3S,EAAO2S,MAAWvG,OAAOC,KAAKxI,EAAMD,UAAUkO,SAAe,OAAI,IAAOrF,EAAMM,MAkBzF,OAjBAlJ,EAAMD,UAAUkO,QAAQa,GAAQ3S,GAErB4S,OACTxG,OAAOC,KAAKrM,EAAO4S,OAAOtG,QAAQ,SAAUjH,GAC1CxB,EAAMD,UAAUyB,GAAOrF,EAAO4S,MAAMvN,KAIpCrF,EAAO6S,QACTzG,OAAOC,KAAKrM,EAAO6S,QAAQvG,QAAQ,SAAUjH,GAC3CxB,EAAMwB,GAAOrF,EAAO6S,OAAOxN,KAI3BrF,EAAO8S,SACT9S,EAAO8S,QAAQ/L,MAAMlD,EAAOuK,GAEvBvK,GAGT8M,EAAY8B,IAAM,SAAczS,GAE5B,IADA,IAAIoO,EAAS,GAAIjI,EAAMpB,UAAUrC,OAAS,EAC1B,EAARyD,KAAYiI,EAAQjI,GAAQpB,UAAWoB,EAAM,GAEvD,IAAItC,EAAQzD,KACZ,OAAIoR,MAAMC,QAAQzR,IAChBA,EAAOsM,QAAQ,SAAUyG,GAAK,OAAOlP,EAAM6O,cAAcK,KAClDlP,GAEFA,EAAM6O,cAAc3L,MAAMlD,EAAO,CAAE7D,GAASgT,OAAQ5E,KAG7DhC,OAAO6G,iBAAkBtC,EAAaI,GAskBtC,IAAImC,EAAS,CACXC,WArkBF,WACE,IACIC,EACAC,EAFAC,EAASlT,KAGTmT,EAAMD,EAAOC,IAEfH,OADiC,IAAxBE,EAAOlF,OAAOgF,MACfE,EAAOlF,OAAOgF,MAEdG,EAAI,GAAGC,YAGfH,OADkC,IAAzBC,EAAOlF,OAAOiF,OACdC,EAAOlF,OAAOiF,OAEdE,EAAI,GAAGE,aAEH,IAAVL,GAAeE,EAAOI,gBAA+B,IAAXL,GAAgBC,EAAOK,eAKtEP,EAAQA,EAAQQ,SAASL,EAAI1J,IAAI,gBAAiB,IAAM+J,SAASL,EAAI1J,IAAI,iBAAkB,IAC3FwJ,EAASA,EAASO,SAASL,EAAI1J,IAAI,eAAgB,IAAM+J,SAASL,EAAI1J,IAAI,kBAAmB,IAE7F4C,EAAMqC,OAAOwE,EAAQ,CACnBF,MAAOA,EACPC,OAAQA,EACRQ,KAAMP,EAAOI,eAAiBN,EAAQC,MA4iBxCS,aAxiBF,WACE,IAAIR,EAASlT,KACTgO,EAASkF,EAAOlF,OAEhB2F,EAAaT,EAAOS,WACpBC,EAAaV,EAAOO,KACpBI,EAAMX,EAAOY,aACbC,EAAWb,EAAOa,SAClBC,EAAYd,EAAOe,SAAWjG,EAAOiG,QAAQC,QAC7CC,EAAuBH,EAAYd,EAAOe,QAAQG,OAAO9R,OAAS4Q,EAAOkB,OAAO9R,OAChF8R,EAAST,EAAW5S,SAAU,IAAOmS,EAAOlF,OAAiB,YAC7DqG,EAAeL,EAAYd,EAAOe,QAAQG,OAAO9R,OAAS8R,EAAO9R,OACjEgS,EAAW,GACXC,EAAa,GACbC,EAAkB,GAElBC,EAAezG,EAAO0G,mBACE,mBAAjBD,IACTA,EAAezG,EAAO0G,mBAAmBpM,KAAK4K,IAGhD,IAAIyB,EAAc3G,EAAO4G,kBACE,mBAAhBD,IACTA,EAAc3G,EAAO4G,kBAAkBtM,KAAK4K,IAG9C,IAAI2B,EAAyB3B,EAAOoB,SAAShS,OACzCwS,EAA2B5B,EAAOoB,SAAShS,OAE3CyS,EAAe/G,EAAO+G,aACtBC,GAAiBP,EACjBQ,EAAgB,EAChB9K,EAAQ,EACZ,QAA0B,IAAfyJ,EAAX,CAaA,IAAIsB,EAaAC,EAvBwB,iBAAjBJ,GAA0D,GAA7BA,EAAajS,QAAQ,OAC3DiS,EAAgBpM,WAAWoM,EAAa1H,QAAQ,IAAK,KAAO,IAAOuG,GAGrEV,EAAOkC,aAAeL,EAGlBlB,EAAOO,EAAO3K,IAAI,CAAE4L,WAAY,GAAIC,UAAW,KAC5ClB,EAAO3K,IAAI,CAAE8L,YAAa,GAAIC,aAAc,KAGtB,EAAzBxH,EAAOyH,kBAEPP,EADEQ,KAAKC,MAAMtB,EAAerG,EAAOyH,mBAAqBpB,EAAenB,EAAOlF,OAAOyH,gBAC5DpB,EAEAqB,KAAKE,KAAKvB,EAAerG,EAAOyH,iBAAmBzH,EAAOyH,gBAExD,SAAzBzH,EAAO6H,eAA2D,QAA/B7H,EAAO8H,sBAC5CZ,EAAyBQ,KAAKK,IAAIb,EAAwBlH,EAAO6H,cAAgB7H,EAAOyH,mBAS5F,IAHA,IAqHIO,EArHAP,EAAkBzH,EAAOyH,gBACzBQ,EAAef,EAAyBO,EACxCS,EAAiBD,GAAiBjI,EAAOyH,gBAAkBQ,EAAgB5B,GACtEhS,EAAI,EAAGA,EAAIgS,EAAchS,GAAK,EAAG,CACxC8S,EAAY,EACZ,IAAIgB,EAAQ/B,EAAO9J,GAAGjI,GACtB,GAA6B,EAAzB2L,EAAOyH,gBAAqB,CAE9B,IAAIW,OAAqB,EACrBC,OAAS,EACTC,OAAM,EACyB,WAA/BtI,EAAO8H,qBAETQ,EAAMjU,GADNgU,EAASX,KAAKC,MAAMtT,EAAIoT,IACJA,GACPS,EAATG,GAA4BA,IAAWH,GAAkBI,IAAQb,EAAkB,IAE1EA,IADXa,GAAO,KAELA,EAAM,EACND,GAAU,GAGdD,EAAqBC,EAAWC,EAAMpB,EAA0BO,EAChEU,EACG1M,IAAI,CACH8M,4BAA6BH,EAC7BI,yBAA0BJ,EAC1BK,iBAAkBL,EAClBM,gBAAiBN,EACjBO,MAAOP,KAIXC,EAAShU,GADTiU,EAAMZ,KAAKC,MAAMtT,EAAI4T,IACDA,EAEtBE,EACG1M,IACE,WAAayJ,EAAOI,eAAiB,MAAQ,QACrC,IAARgD,GAAatI,EAAO+G,cAAoB/G,EAAmB,aAAI,MAEjEzJ,KAAK,qBAAsB8R,GAC3B9R,KAAK,kBAAmB+R,GAE7B,GAA6B,SAAzBH,EAAM1M,IAAI,WAAd,CAEA,GAA6B,SAAzBuE,EAAO6H,cAA0B,CACnC,IAAIe,EAActV,EAAIM,iBAAiBuU,EAAM,GAAI,MAC7CU,EAAmBV,EAAM,GAAGlV,MAAMoE,UAClCyR,EAAyBX,EAAM,GAAGlV,MAAMsE,gBACxCsR,IACFV,EAAM,GAAGlV,MAAMoE,UAAY,QAEzByR,IACFX,EAAM,GAAGlV,MAAMsE,gBAAkB,QAGjC4P,EADEnH,EAAO+I,aACG7D,EAAOI,eACf6C,EAAM5N,YAAW,GACjB4N,EAAMvN,aAAY,GAGlBsK,EAAOI,eACG6C,EAAM,GAAGnN,wBAAwBgK,MACzCrK,WAAWiO,EAAY/U,iBAAiB,gBACxC8G,WAAWiO,EAAY/U,iBAAiB,iBAEhCsU,EAAM,GAAGnN,wBAAwBiK,OACzCtK,WAAWiO,EAAY/U,iBAAiB,eACxC8G,WAAWiO,EAAY/U,iBAAiB,kBAG5CgV,IACFV,EAAM,GAAGlV,MAAMoE,UAAYwR,GAEzBC,IACFX,EAAM,GAAGlV,MAAMsE,gBAAkBuR,GAE/B9I,EAAO+I,eAAgB5B,EAAYO,KAAKC,MAAMR,SAElDA,GAAavB,GAAe5F,EAAO6H,cAAgB,GAAKd,GAAiB/G,EAAO6H,cAC5E7H,EAAO+I,eAAgB5B,EAAYO,KAAKC,MAAMR,IAE9Cf,EAAO/R,KACL6Q,EAAOI,eACTc,EAAO/R,GAAGpB,MAAM+R,MAAQmC,EAAY,KAEpCf,EAAO/R,GAAGpB,MAAMgS,OAASkC,EAAY,MAIvCf,EAAO/R,KACT+R,EAAO/R,GAAG2U,gBAAkB7B,GAE9BX,EAAgBvR,KAAKkS,GAGjBnH,EAAOiJ,gBACTjC,EAAgBA,EAAiBG,EAAY,EAAMF,EAAgB,EAAKF,EAClD,IAAlBE,GAA6B,IAAN5S,IAAW2S,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC/E,IAAN1S,IAAW2S,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC9DW,KAAKwB,IAAIlC,GAAiB,OAAYA,EAAgB,GACtDhH,EAAO+I,eAAgB/B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUhH,EAAOmJ,gBAAmB,GAAK7C,EAASrR,KAAK+R,GAC3DT,EAAWtR,KAAK+R,KAEZhH,EAAO+I,eAAgB/B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUhH,EAAOmJ,gBAAmB,GAAK7C,EAASrR,KAAK+R,GAC3DT,EAAWtR,KAAK+R,GAChBA,EAAgBA,EAAgBG,EAAYJ,GAG9C7B,EAAOkC,aAAeD,EAAYJ,EAElCE,EAAgBE,EAEhBhL,GAAS,GAcX,GAZA+I,EAAOkC,YAAcM,KAAKK,IAAI7C,EAAOkC,YAAaxB,GAAce,EAI9Dd,GAAOE,IAA+B,UAAlB/F,EAAOoJ,QAAwC,cAAlBpJ,EAAOoJ,SACxDzD,EAAWlK,IAAI,CAAEuJ,MAASE,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAEnE3F,EAAQW,UAAW/B,EAAOqJ,iBACzBnE,EAAOI,eAAkBK,EAAWlK,IAAI,CAAEuJ,MAASE,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAC5FpB,EAAWlK,IAAI,CAAEwJ,OAAUC,EAAOkC,YAAcpH,EAAO+G,aAAgB,QAGnD,EAAzB/G,EAAOyH,kBACTvC,EAAOkC,aAAeD,EAAYnH,EAAO+G,cAAgBG,EACzDhC,EAAOkC,YAAcM,KAAKE,KAAK1C,EAAOkC,YAAcpH,EAAOyH,iBAAmBzH,EAAO+G,aACjF7B,EAAOI,eAAkBK,EAAWlK,IAAI,CAAEuJ,MAASE,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAC5FpB,EAAWlK,IAAI,CAAEwJ,OAAUC,EAAOkC,YAAcpH,EAAO+G,aAAgB,OAC1E/G,EAAOiJ,gBAAgB,CACzBjB,EAAgB,GAChB,IAAK,IAAIsB,EAAM,EAAGA,EAAMhD,EAAShS,OAAQgV,GAAO,EAAG,CACjD,IAAIC,EAAiBjD,EAASgD,GAC1BtJ,EAAO+I,eAAgBQ,EAAiB7B,KAAKC,MAAM4B,IACnDjD,EAASgD,GAAOpE,EAAOkC,YAAcd,EAAS,IAAM0B,EAAc/S,KAAKsU,GAE7EjD,EAAW0B,EAKf,IAAKhI,EAAOiJ,eAAgB,CAC1BjB,EAAgB,GAChB,IAAK,IAAIwB,EAAM,EAAGA,EAAMlD,EAAShS,OAAQkV,GAAO,EAAG,CACjD,IAAIC,EAAmBnD,EAASkD,GAC5BxJ,EAAO+I,eAAgBU,EAAmB/B,KAAKC,MAAM8B,IACrDnD,EAASkD,IAAQtE,EAAOkC,YAAcxB,GACxCoC,EAAc/S,KAAKwU,GAGvBnD,EAAW0B,EACmF,EAA1FN,KAAKC,MAAMzC,EAAOkC,YAAcxB,GAAc8B,KAAKC,MAAMrB,EAASA,EAAShS,OAAS,KACtFgS,EAASrR,KAAKiQ,EAAOkC,YAAcxB,GAYvC,GATwB,IAApBU,EAAShS,SAAgBgS,EAAW,CAAC,IAEb,IAAxBtG,EAAO+G,eACL7B,EAAOI,eACLO,EAAOO,EAAO3K,IAAI,CAAE4L,WAAaN,EAAe,OAC7CX,EAAO3K,IAAI,CAAE8L,YAAcR,EAAe,OAC1CX,EAAO3K,IAAI,CAAE+L,aAAeT,EAAe,QAGlD/G,EAAO0J,yBAA0B,CACnC,IAAIC,EAAgB,EAKpB,GAJAnD,EAAgBtI,QAAQ,SAAU0L,GAChCD,GAAiBC,GAAkB5J,EAAO+G,aAAe/G,EAAO+G,aAAe,MAEjF4C,GAAiB3J,EAAO+G,cACJnB,EAAY,CAC9B,IAAIiE,GAAmBjE,EAAa+D,GAAiB,EACrDrD,EAASpI,QAAQ,SAAU4L,EAAMC,GAC/BzD,EAASyD,GAAaD,EAAOD,IAE/BtD,EAAWrI,QAAQ,SAAU4L,EAAMC,GACjCxD,EAAWwD,GAAaD,EAAOD,KAKrCxL,EAAMqC,OAAOwE,EAAQ,CACnBkB,OAAQA,EACRE,SAAUA,EACVC,WAAYA,EACZC,gBAAiBA,IAGfH,IAAiBF,GACnBjB,EAAO/B,KAAK,sBAEVmD,EAAShS,SAAWuS,IAClB3B,EAAOlF,OAAOgK,eAAiB9E,EAAO+E,gBAC1C/E,EAAO/B,KAAK,yBAEVoD,EAAWjS,SAAWwS,GACxB5B,EAAO/B,KAAK,2BAGVnD,EAAOkK,qBAAuBlK,EAAOmK,wBACvCjF,EAAOkF,uBAiSTC,iBA7RF,SAA2BC,GACzB,IAGIjW,EAHA6Q,EAASlT,KACTuY,EAAe,GACfC,EAAY,EAQhB,GANqB,iBAAVF,EACTpF,EAAOuF,cAAcH,IACF,IAAVA,GACTpF,EAAOuF,cAAcvF,EAAOlF,OAAOsK,OAGD,SAAhCpF,EAAOlF,OAAO6H,eAA0D,EAA9B3C,EAAOlF,OAAO6H,cAC1D,IAAKxT,EAAI,EAAGA,EAAIqT,KAAKE,KAAK1C,EAAOlF,OAAO6H,eAAgBxT,GAAK,EAAG,CAC9D,IAAI8H,EAAQ+I,EAAOwF,YAAcrW,EACjC,GAAI8H,EAAQ+I,EAAOkB,OAAO9R,OAAU,MACpCiW,EAAatV,KAAKiQ,EAAOkB,OAAO9J,GAAGH,GAAO,SAG5CoO,EAAatV,KAAKiQ,EAAOkB,OAAO9J,GAAG4I,EAAOwF,aAAa,IAIzD,IAAKrW,EAAI,EAAGA,EAAIkW,EAAajW,OAAQD,GAAK,EACxC,QAA+B,IAApBkW,EAAalW,GAAoB,CAC1C,IAAI4Q,EAASsF,EAAalW,GAAGwG,aAC7B2P,EAAqBA,EAATvF,EAAqBA,EAASuF,EAK1CA,GAAatF,EAAOS,WAAWlK,IAAI,SAAW+O,EAAY,OAgQ9DJ,mBA7PF,WAGE,IAFA,IACIhE,EADSpU,KACOoU,OACX/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EACtC+R,EAAO/R,GAAGsW,kBAHC3Y,KAG0BsT,eAAiBc,EAAO/R,GAAGuW,WAAaxE,EAAO/R,GAAGwW,WA0PzFC,qBAtPF,SAA+BC,QACV,IAAdA,IAAuBA,EAAa/Y,MAAQA,KAAK+Y,WAAc,GAEpE,IAAI7F,EAASlT,KACTgO,EAASkF,EAAOlF,OAEhBoG,EAASlB,EAAOkB,OAChBP,EAAMX,EAAOY,aAEjB,GAAsB,IAAlBM,EAAO9R,OAAX,MAC2C,IAAhC8R,EAAO,GAAGuE,mBAAqCzF,EAAOkF,qBAEjE,IAAIY,GAAgBD,EAChBlF,IAAOmF,EAAeD,GAG1B3E,EAAOnQ,YAAY+J,EAAOiL,mBAE1B/F,EAAOgG,qBAAuB,GAC9BhG,EAAOiG,cAAgB,GAEvB,IAAK,IAAI9W,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI8T,EAAQ/B,EAAO/R,GACf+W,GACDJ,GAAgBhL,EAAOiJ,eAAiB/D,EAAOmG,eAAiB,GAAMlD,EAAMwC,oBAC1ExC,EAAMa,gBAAkBhJ,EAAO+G,cACpC,GAAI/G,EAAOmK,sBAAuB,CAChC,IAAImB,IAAgBN,EAAe7C,EAAMwC,mBACrCY,EAAaD,EAAcpG,EAAOsB,gBAAgBnS,IACtB,GAAfiX,GAAoBA,EAAcpG,EAAOO,MAC/B,EAAb8F,GAAkBA,GAAcrG,EAAOO,MACvC6F,GAAe,GAAKC,GAAcrG,EAAOO,QAErDP,EAAOiG,cAAclW,KAAKkT,GAC1BjD,EAAOgG,qBAAqBjW,KAAKZ,GACjC+R,EAAO9J,GAAGjI,GAAGsB,SAASqK,EAAOiL,oBAGjC9C,EAAMqD,SAAW3F,GAAOuF,EAAgBA,EAE1ClG,EAAOiG,cAAgB5W,EAAE2Q,EAAOiG,iBA+MhCM,eA5MF,SAAyBV,QACJ,IAAdA,IAAuBA,EAAa/Y,MAAQA,KAAK+Y,WAAc,GAEpE,IAAI7F,EAASlT,KACTgO,EAASkF,EAAOlF,OAEhB0L,EAAiBxG,EAAOyG,eAAiBzG,EAAOmG,eAChDG,EAAWtG,EAAOsG,SAClBI,EAAc1G,EAAO0G,YACrBC,EAAQ3G,EAAO2G,MACfC,EAAeF,EACfG,EAASF,EACU,IAAnBH,EAGFG,EADAD,IADAJ,EAAW,IAKXI,GADAJ,GAAYT,EAAY7F,EAAOmG,gBAAkB,IACvB,EAC1BQ,EAAoB,GAAZL,GAEVnN,EAAMqC,OAAOwE,EAAQ,CACnBsG,SAAUA,EACVI,YAAaA,EACbC,MAAOA,KAGL7L,EAAOkK,qBAAuBlK,EAAOmK,wBAAyBjF,EAAO4F,qBAAqBC,GAE1Fa,IAAgBE,GAClB5G,EAAO/B,KAAK,yBAEV0I,IAAUE,GACZ7G,EAAO/B,KAAK,oBAET2I,IAAiBF,GAAiBG,IAAWF,IAChD3G,EAAO/B,KAAK,YAGd+B,EAAO/B,KAAK,WAAYqI,IAsKxBQ,oBAnKF,WACE,IAWIC,EAXA/G,EAASlT,KAEToU,EAASlB,EAAOkB,OAChBpG,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WACpB+E,EAAcxF,EAAOwF,YACrBwB,EAAYhH,EAAOgH,UACnBlG,EAAYd,EAAOe,SAAWjG,EAAOiG,QAAQC,QAEjDE,EAAOnQ,YAAc+J,EAAuB,iBAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAgC,0BAAI,IAAOA,EAA8B,wBAAI,IAAOA,EAA8B,0BAIvOiM,EADEjG,EACYd,EAAOS,WAAWhI,KAAM,IAAOqC,EAAiB,WAAI,6BAAgC0K,EAAc,MAElGtE,EAAO9J,GAAGoO,IAId/U,SAASqK,EAAOmM,kBAExBnM,EAAOoM,OAELH,EAAY9V,SAAS6J,EAAOqM,qBAC9B1G,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAiCkM,EAAY,MAC7HvW,SAASqK,EAAOsM,2BAEnB3G,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAgCkM,EAAY,MACvHvW,SAASqK,EAAOsM,4BAIvB,IAAIC,EAAYN,EAAY/O,QAAS,IAAO8C,EAAiB,YAAI1D,GAAG,GAAG3G,SAASqK,EAAOwM,gBACnFxM,EAAOoM,MAA6B,IAArBG,EAAUjY,SAC3BiY,EAAYnG,EAAO9J,GAAG,IACZ3G,SAASqK,EAAOwM,gBAG5B,IAAIC,EAAYR,EAAY3O,QAAS,IAAO0C,EAAiB,YAAI1D,GAAG,GAAG3G,SAASqK,EAAO0M,gBACnF1M,EAAOoM,MAA6B,IAArBK,EAAUnY,SAC3BmY,EAAYrG,EAAO9J,IAAI,IACb3G,SAASqK,EAAO0M,gBAExB1M,EAAOoM,OAELG,EAAUpW,SAAS6J,EAAOqM,qBAC5B1G,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkCuM,EAAUhW,KAAK,2BAA8B,MAC/JZ,SAASqK,EAAO2M,yBAEnBhH,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiCuM,EAAUhW,KAAK,2BAA8B,MACzJZ,SAASqK,EAAO2M,yBAEjBF,EAAUtW,SAAS6J,EAAOqM,qBAC5B1G,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkCyM,EAAUlW,KAAK,2BAA8B,MAC/JZ,SAASqK,EAAO4M,yBAEnBjH,EACG5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiCyM,EAAUlW,KAAK,2BAA8B,MACzJZ,SAASqK,EAAO4M,2BAoGvBC,kBA/FF,SAA4BC,GAC1B,IASI/C,EATA7E,EAASlT,KACT+Y,EAAY7F,EAAOY,aAAeZ,EAAO6F,WAAa7F,EAAO6F,UAC7DxE,EAAarB,EAAOqB,WACpBD,EAAWpB,EAAOoB,SAClBtG,EAASkF,EAAOlF,OAChB+M,EAAgB7H,EAAOwF,YACvBsC,EAAoB9H,EAAOgH,UAC3Be,EAAoB/H,EAAO6E,UAC3BW,EAAcoC,EAElB,QAA2B,IAAhBpC,EAA6B,CACtC,IAAK,IAAIrW,EAAI,EAAGA,EAAIkS,EAAWjS,OAAQD,GAAK,OACT,IAAtBkS,EAAWlS,EAAI,GACpB0W,GAAaxE,EAAWlS,IAAM0W,EAAYxE,EAAWlS,EAAI,IAAOkS,EAAWlS,EAAI,GAAKkS,EAAWlS,IAAM,EACvGqW,EAAcrW,EACL0W,GAAaxE,EAAWlS,IAAM0W,EAAYxE,EAAWlS,EAAI,KAClEqW,EAAcrW,EAAI,GAEX0W,GAAaxE,EAAWlS,KACjCqW,EAAcrW,GAId2L,EAAOkN,sBACLxC,EAAc,QAA4B,IAAhBA,KAA+BA,EAAc,GAS/E,IALEX,EADiC,GAA/BzD,EAASxR,QAAQiW,GACPzE,EAASxR,QAAQiW,GAEjBrD,KAAKC,MAAM+C,EAAc1K,EAAOmJ,kBAE7B7C,EAAShS,SAAUyV,EAAYzD,EAAShS,OAAS,GAC9DoW,IAAgBqC,EAApB,CASA,IAAIb,EAAY1G,SAASN,EAAOkB,OAAO9J,GAAGoO,GAAanU,KAAK,4BAA8BmU,EAAa,IAEvGrM,EAAMqC,OAAOwE,EAAQ,CACnB6E,UAAWA,EACXmC,UAAWA,EACXa,cAAeA,EACfrC,YAAaA,IAEfxF,EAAO/B,KAAK,qBACZ+B,EAAO/B,KAAK,mBACR6J,IAAsBd,GACxBhH,EAAO/B,KAAK,mBAEd+B,EAAO/B,KAAK,oBArBN4G,IAAckD,IAChB/H,EAAO6E,UAAYA,EACnB7E,EAAO/B,KAAK,qBA2DhBgK,mBArCF,SAA6B9U,GAC3B,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBmI,EAAQ5T,EAAE8D,EAAEC,QAAQoF,QAAS,IAAOsC,EAAiB,YAAI,GACzDoN,GAAa,EACjB,GAAIjF,EACF,IAAK,IAAI9T,EAAI,EAAGA,EAAI6Q,EAAOkB,OAAO9R,OAAQD,GAAK,EACzC6Q,EAAOkB,OAAO/R,KAAO8T,IAASiF,GAAa,GAInD,IAAIjF,IAASiF,EAUX,OAFAlI,EAAOmI,kBAAetU,OACtBmM,EAAOoI,kBAAevU,GARtBmM,EAAOmI,aAAelF,EAClBjD,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAC1ChB,EAAOoI,aAAe9H,SAASjR,EAAE4T,GAAO5R,KAAK,2BAA4B,IAEzE2O,EAAOoI,aAAe/Y,EAAE4T,GAAOhM,QAO/B6D,EAAOuN,0BAA+CxU,IAAxBmM,EAAOoI,cAA8BpI,EAAOoI,eAAiBpI,EAAOwF,aACpGxF,EAAOqI,wBAuFX,IAAIxC,EAAY,CACdnM,aAxEF,SAAuBC,QACP,IAATA,IAAkBA,EAAO7M,KAAKsT,eAAiB,IAAM,KAE1D,IAEItF,EAFShO,KAEOgO,OAChB6F,EAHS7T,KAGI8T,aACbiF,EAJS/Y,KAIU+Y,UACnBpF,EALS3T,KAKW2T,WAExB,GAAI3F,EAAOwN,iBACT,OAAO3H,GAAOkF,EAAYA,EAG5B,IAAI0C,EAAmBpP,EAAMO,aAAa+G,EAAW,GAAI9G,GAGzD,OAFIgH,IAAO4H,GAAoBA,GAExBA,GAAoB,GAwD3BC,aArDF,SAAuB3C,EAAW4C,GAChC,IAAIzI,EAASlT,KACT6T,EAAMX,EAAOY,aACb9F,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WACpB6F,EAAWtG,EAAOsG,SAClBoC,EAAI,EACJC,EAAI,EAGJ3I,EAAOI,eACTsI,EAAI/H,GAAOkF,EAAYA,EAEvB8C,EAAI9C,EAGF/K,EAAO+I,eACT6E,EAAIlG,KAAKC,MAAMiG,GACfC,EAAInG,KAAKC,MAAMkG,IAGZ7N,EAAOwN,mBACNpM,EAAQS,aAAgB8D,EAAWtO,UAAW,eAAiBuW,EAAI,OAASC,EAAI,YAC7ElI,EAAWtO,UAAW,aAAeuW,EAAI,OAASC,EAAI,QAE/D3I,EAAO4I,kBAAoB5I,EAAO6F,UAClC7F,EAAO6F,UAAY7F,EAAOI,eAAiBsI,EAAIC,EAI/C,IAAInC,EAAiBxG,EAAOyG,eAAiBzG,EAAOmG,gBAC7B,IAAnBK,EACY,GAECX,EAAY7F,EAAOmG,gBAAkB,KAElCG,GAClBtG,EAAOuG,eAAeV,GAGxB7F,EAAO/B,KAAK,eAAgB+B,EAAO6F,UAAW4C,IAc9CtC,aAXF,WACE,OAASrZ,KAAKsU,SAAS,IAWvBqF,aARF,WACE,OAAS3Z,KAAKsU,SAAStU,KAAKsU,SAAShS,OAAS,KAoFhD,IAAIyZ,EAAe,CACjBtD,cA3EF,SAAwBhT,EAAUkW,GACnB3b,KAEN2T,WAAWnO,WAAWC,GAFhBzF,KAINmR,KAAK,gBAAiB1L,EAAUkW,IAuEvCK,gBApEF,SAA0BC,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAI/I,EAASlT,KACT0Y,EAAcxF,EAAOwF,YACrB1K,EAASkF,EAAOlF,OAChB+M,EAAgB7H,EAAO6H,cACvB/M,EAAOmO,YACTjJ,EAAOmF,mBAGT,IAAI+D,EAAMF,EASV,GARKE,IACgCA,EAAjBrB,EAAdrC,EAAqC,OAChCA,EAAcqC,EAAuB,OACjC,SAGf7H,EAAO/B,KAAK,mBAER8K,GAAgBvD,IAAgBqC,EAAe,CACjD,GAAY,UAARqB,EAEF,YADAlJ,EAAO/B,KAAK,6BAGd+B,EAAO/B,KAAK,8BACA,SAARiL,EACFlJ,EAAO/B,KAAK,4BAEZ+B,EAAO/B,KAAK,8BAwChBjJ,cAnCF,SAA0B+T,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAI/I,EAASlT,KACT0Y,EAAcxF,EAAOwF,YACrBqC,EAAgB7H,EAAO6H,cAC3B7H,EAAOmJ,WAAY,EACnBnJ,EAAOuF,cAAc,GAErB,IAAI2D,EAAMF,EASV,GARKE,IACgCA,EAAjBrB,EAAdrC,EAAqC,OAChCA,EAAcqC,EAAuB,OACjC,SAGf7H,EAAO/B,KAAK,iBAER8K,GAAgBvD,IAAgBqC,EAAe,CACjD,GAAY,UAARqB,EAEF,YADAlJ,EAAO/B,KAAK,2BAGd+B,EAAO/B,KAAK,4BACA,SAARiL,EACFlJ,EAAO/B,KAAK,0BAEZ+B,EAAO/B,KAAK,6BA2QlB,IAAIgF,EAAQ,CACVmG,QAjQF,SAAkBnS,EAAOmO,EAAO2D,EAAcM,QAC7B,IAAVpS,IAAmBA,EAAQ,QACjB,IAAVmO,IAAmBA,EAAQtY,KAAKgO,OAAOsK,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAI/I,EAASlT,KACTwc,EAAarS,EACbqS,EAAa,IAAKA,EAAa,GAEnC,IAAIxO,EAASkF,EAAOlF,OAChBsG,EAAWpB,EAAOoB,SAClBC,EAAarB,EAAOqB,WACpBwG,EAAgB7H,EAAO6H,cACvBrC,EAAcxF,EAAOwF,YACrB7E,EAAMX,EAAOY,aACjB,GAAIZ,EAAOmJ,WAAarO,EAAOyO,+BAC7B,OAAO,EAGT,IAAI1E,EAAYrC,KAAKC,MAAM6G,EAAaxO,EAAOmJ,gBAC3CY,GAAazD,EAAShS,SAAUyV,EAAYzD,EAAShS,OAAS,IAE7DoW,GAAe1K,EAAO0O,cAAgB,MAAQ3B,GAAiB,IAAMkB,GACxE/I,EAAO/B,KAAK,0BAGd,IAuBI+K,EAvBAnD,GAAazE,EAASyD,GAM1B,GAHA7E,EAAOuG,eAAeV,GAGlB/K,EAAOkN,oBACT,IAAK,IAAI7Y,EAAI,EAAGA,EAAIkS,EAAWjS,OAAQD,GAAK,GACrCqT,KAAKC,MAAkB,IAAZoD,IAAoBrD,KAAKC,MAAsB,IAAhBpB,EAAWlS,MACxDma,EAAana,GAKnB,GAAI6Q,EAAOyJ,aAAeH,IAAe9D,EAAa,CACpD,IAAKxF,EAAO0J,gBAAkB7D,EAAY7F,EAAO6F,WAAaA,EAAY7F,EAAOmG,eAC/E,OAAO,EAET,IAAKnG,EAAO2J,gBAAkB9D,EAAY7F,EAAO6F,WAAaA,EAAY7F,EAAOyG,iBAC1EjB,GAAe,KAAO8D,EAAc,OAAO,EAWpD,OANgCN,EAAfxD,EAAb8D,EAAwC,OACnCA,EAAa9D,EAA2B,OAC9B,QAId7E,IAAQkF,IAAc7F,EAAO6F,YAAgBlF,GAAOkF,IAAc7F,EAAO6F,WAC5E7F,EAAO2H,kBAAkB2B,GAErBxO,EAAOmO,YACTjJ,EAAOmF,mBAETnF,EAAO8G,sBACe,UAAlBhM,EAAOoJ,QACTlE,EAAOwI,aAAa3C,GAEJ,UAAdmD,IACFhJ,EAAO8I,gBAAgBC,EAAcC,GACrChJ,EAAOhL,cAAc+T,EAAcC,KAE9B,IAGK,IAAV5D,GAAgBlJ,EAAQ5J,YAS1B0N,EAAOuF,cAAcH,GACrBpF,EAAOwI,aAAa3C,GACpB7F,EAAO2H,kBAAkB2B,GACzBtJ,EAAO8G,sBACP9G,EAAO/B,KAAK,wBAAyBmH,EAAOiE,GAC5CrJ,EAAO8I,gBAAgBC,EAAcC,GAChChJ,EAAOmJ,YACVnJ,EAAOmJ,WAAY,EACdnJ,EAAO4J,gCACV5J,EAAO4J,8BAAgC,SAAuBzW,GACvD6M,IAAUA,EAAO6J,WAClB1W,EAAEC,SAAWtG,OACjBkT,EAAOS,WAAW,GAAGtT,oBAAoB,gBAAiB6S,EAAO4J,+BACjE5J,EAAOS,WAAW,GAAGtT,oBAAoB,sBAAuB6S,EAAO4J,+BACvE5J,EAAO4J,8BAAgC,YAChC5J,EAAO4J,8BACd5J,EAAOhL,cAAc+T,EAAcC,MAGvChJ,EAAOS,WAAW,GAAGvT,iBAAiB,gBAAiB8S,EAAO4J,+BAC9D5J,EAAOS,WAAW,GAAGvT,iBAAiB,sBAAuB8S,EAAO4J,kCA5BtE5J,EAAOuF,cAAc,GACrBvF,EAAOwI,aAAa3C,GACpB7F,EAAO2H,kBAAkB2B,GACzBtJ,EAAO8G,sBACP9G,EAAO/B,KAAK,wBAAyBmH,EAAOiE,GAC5CrJ,EAAO8I,gBAAgBC,EAAcC,GACrChJ,EAAOhL,cAAc+T,EAAcC,KA0B9B,IAwJPc,YArJF,SAAsB7S,EAAOmO,EAAO2D,EAAcM,QACjC,IAAVpS,IAAmBA,EAAQ,QACjB,IAAVmO,IAAmBA,EAAQtY,KAAKgO,OAAOsK,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IACIgB,EAAW9S,EAKf,OANanK,KAEFgO,OAAOoM,OAChB6C,GAHWjd,KAGQkd,cAHRld,KAMCsc,QAAQW,EAAU3E,EAAO2D,EAAcM,IA2IrDY,UAvIF,SAAoB7E,EAAO2D,EAAcM,QACxB,IAAVjE,IAAmBA,EAAQtY,KAAKgO,OAAOsK,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAI/I,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBqO,EAAYnJ,EAAOmJ,UACvB,OAAIrO,EAAOoM,MACLiC,IACJnJ,EAAOkK,UAEPlK,EAAOmK,YAAcnK,EAAOS,WAAW,GAAGzK,WACnCgK,EAAOoJ,QAAQpJ,EAAOwF,YAAc1K,EAAOmJ,eAAgBmB,EAAO2D,EAAcM,IAElFrJ,EAAOoJ,QAAQpJ,EAAOwF,YAAc1K,EAAOmJ,eAAgBmB,EAAO2D,EAAcM,IA0HvFe,UAtHF,SAAoBhF,EAAO2D,EAAcM,QACxB,IAAVjE,IAAmBA,EAAQtY,KAAKgO,OAAOsK,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAI/I,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBqO,EAAYnJ,EAAOmJ,UACnB/H,EAAWpB,EAAOoB,SAClBC,EAAarB,EAAOqB,WACpBT,EAAeZ,EAAOY,aAE1B,GAAI9F,EAAOoM,KAAM,CACf,GAAIiC,EAAa,OAAO,EACxBnJ,EAAOkK,UAEPlK,EAAOmK,YAAcnK,EAAOS,WAAW,GAAGzK,WAG5C,SAASqU,EAAUC,GACjB,OAAIA,EAAM,GAAa9H,KAAKC,MAAMD,KAAKwB,IAAIsG,IACpC9H,KAAKC,MAAM6H,GAEpB,IAMIC,EANAC,EAAsBH,EALVzJ,EAAeZ,EAAO6F,WAAa7F,EAAO6F,WAMtD4E,EAAqBrJ,EAASnH,IAAI,SAAUqQ,GAAO,OAAOD,EAAUC,KAIpEI,GAHuBrJ,EAAWpH,IAAI,SAAUqQ,GAAO,OAAOD,EAAUC,KAE1DlJ,EAASqJ,EAAmB7a,QAAQ4a,IACvCpJ,EAASqJ,EAAmB7a,QAAQ4a,GAAuB,IAM1E,YAJwB,IAAbE,IACTH,EAAYlJ,EAAWzR,QAAQ8a,IACf,IAAKH,EAAYvK,EAAOwF,YAAc,GAEjDxF,EAAOoJ,QAAQmB,EAAWnF,EAAO2D,EAAcM,IAsFtDsB,WAlFF,SAAqBvF,EAAO2D,EAAcM,GAKxC,YAJe,IAAVjE,IAAmBA,EAAQtY,KAAKgO,OAAOsK,YACtB,IAAjB2D,IAA0BA,GAAe,GAEjCjc,KACCsc,QADDtc,KACgB0Y,YAAaJ,EAAO2D,EAAcM,IA8E/DuB,eA1EF,SAAyBxF,EAAO2D,EAAcM,QAC7B,IAAVjE,IAAmBA,EAAQtY,KAAKgO,OAAOsK,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAI/I,EAASlT,KACTmK,EAAQ+I,EAAOwF,YACfX,EAAYrC,KAAKC,MAAMxL,EAAQ+I,EAAOlF,OAAOmJ,gBAEjD,GAAIY,EAAY7E,EAAOoB,SAAShS,OAAS,EAAG,CAC1C,IAAIyW,EAAY7F,EAAOY,aAAeZ,EAAO6F,WAAa7F,EAAO6F,UAE7DgF,EAAc7K,EAAOoB,SAASyD,IACnB7E,EAAOoB,SAASyD,EAAY,GAECgG,GAAe,EAAtDhF,EAAYgF,IACf5T,EAAQ+I,EAAOlF,OAAOmJ,gBAI1B,OAAOjE,EAAOoJ,QAAQnS,EAAOmO,EAAO2D,EAAcM,IAwDlDhB,oBArDF,WACE,IAMIrB,EANAhH,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WAEpBkC,EAAyC,SAAzB7H,EAAO6H,cAA2B3C,EAAO8K,uBAAyBhQ,EAAO6H,cACzFoI,EAAe/K,EAAOoI,aAE1B,GAAItN,EAAOoM,KAAM,CACf,GAAIlH,EAAOmJ,UAAa,OACxBnC,EAAY1G,SAASjR,EAAE2Q,EAAOmI,cAAc9W,KAAK,2BAA4B,IACzEyJ,EAAOiJ,eAENgH,EAAe/K,EAAOgK,aAAgBrH,EAAgB,GACnDoI,EAAgB/K,EAAOkB,OAAO9R,OAAS4Q,EAAOgK,aAAiBrH,EAAgB,GAEnF3C,EAAOkK,UACPa,EAAetK,EACZ5S,SAAU,IAAOiN,EAAiB,WAAI,6BAAgCkM,EAAY,WAAelM,EAA0B,oBAAI,KAC/H1D,GAAG,GACHH,QAEHkC,EAAMI,SAAS,WACbyG,EAAOoJ,QAAQ2B,MAGjB/K,EAAOoJ,QAAQ2B,GAERA,EAAe/K,EAAOkB,OAAO9R,OAASuT,GAC/C3C,EAAOkK,UACPa,EAAetK,EACZ5S,SAAU,IAAOiN,EAAiB,WAAI,6BAAgCkM,EAAY,WAAelM,EAA0B,oBAAI,KAC/H1D,GAAG,GACHH,QAEHkC,EAAMI,SAAS,WACbyG,EAAOoJ,QAAQ2B,MAGjB/K,EAAOoJ,QAAQ2B,QAGjB/K,EAAOoJ,QAAQ2B,KA0GnB,IAAI7D,EAAO,CACT8D,WA7FF,WACE,IAAIhL,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WAExBA,EAAW5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,qBAAI9J,SAEtF,IAAIkQ,EAAST,EAAW5S,SAAU,IAAOiN,EAAiB,YAE1D,GAAIA,EAAOmQ,uBAAwB,CACjC,IAAIC,EAAiBpQ,EAAOmJ,eAAkB/C,EAAO9R,OAAS0L,EAAOmJ,eACrE,GAAIiH,IAAmBpQ,EAAOmJ,eAAgB,CAC5C,IAAK,IAAI9U,EAAI,EAAGA,EAAI+b,EAAgB/b,GAAK,EAAG,CAC1C,IAAIgc,EAAY9b,EAAEtC,EAAIa,cAAc,QAAQ6C,SAAWqK,EAAiB,WAAI,IAAOA,EAAsB,iBACzG2F,EAAWnJ,OAAO6T,GAEpBjK,EAAST,EAAW5S,SAAU,IAAOiN,EAAiB,aAI7B,SAAzBA,EAAO6H,eAA6B7H,EAAOkP,eAAgBlP,EAAOkP,aAAe9I,EAAO9R,QAE5F4Q,EAAOgK,aAAe1J,SAASxF,EAAOkP,cAAgBlP,EAAO6H,cAAe,IAC5E3C,EAAOgK,cAAgBlP,EAAOsQ,qBAC1BpL,EAAOgK,aAAe9I,EAAO9R,SAC/B4Q,EAAOgK,aAAe9I,EAAO9R,QAG/B,IAAIic,EAAgB,GAChBC,EAAe,GACnBpK,EAAOxK,KAAK,SAAUO,EAAOjF,GAC3B,IAAIiR,EAAQ5T,EAAE2C,GACViF,EAAQ+I,EAAOgK,cAAgBsB,EAAavb,KAAKiC,GACjDiF,EAAQiK,EAAO9R,QAAU6H,GAASiK,EAAO9R,OAAS4Q,EAAOgK,cAAgBqB,EAActb,KAAKiC,GAChGiR,EAAM5R,KAAK,0BAA2B4F,KAExC,IAAK,IAAImN,EAAM,EAAGA,EAAMkH,EAAalc,OAAQgV,GAAO,EAClD3D,EAAWnJ,OAAOjI,EAAEic,EAAalH,GAAKmH,WAAU,IAAO9a,SAASqK,EAAOqM,sBAEzE,IAAK,IAAI7C,EAAM+G,EAAcjc,OAAS,EAAU,GAAPkV,EAAUA,GAAO,EACxD7D,EAAW9I,QAAQtI,EAAEgc,EAAc/G,GAAKiH,WAAU,IAAO9a,SAASqK,EAAOqM,uBAsD3E+C,QAlDF,WACE,IASIH,EATA/J,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB0K,EAAcxF,EAAOwF,YACrBtE,EAASlB,EAAOkB,OAChB8I,EAAehK,EAAOgK,aACtBL,EAAiB3J,EAAO2J,eACxBD,EAAiB1J,EAAO0J,eACxBtI,EAAWpB,EAAOoB,SAClBT,EAAMX,EAAOY,aAEjBZ,EAAO2J,gBAAiB,EACxB3J,EAAO0J,gBAAiB,EAExB,IACI8B,GADiBpK,EAASoE,GACHxF,EAAOtG,eAI9B8L,EAAcwE,GAChBD,EAAY7I,EAAO9R,OAAyB,EAAf4a,EAAqBxE,EAClDuE,GAAYC,EACOhK,EAAOoJ,QAAQW,EAAU,GAAG,GAAO,IACzB,IAATyB,GAClBxL,EAAOwI,cAAc7H,GAAOX,EAAO6F,UAAY7F,EAAO6F,WAAa2F,KAElC,SAAzB1Q,EAAO6H,eAA0D,EAAfqH,GAAfxE,GAAqCA,GAAetE,EAAO9R,OAAS4a,KAEjHD,GAAY7I,EAAO9R,OAASoW,EAAcwE,EAC1CD,GAAYC,EACShK,EAAOoJ,QAAQW,EAAU,GAAG,GAAO,IACzB,IAATyB,GACpBxL,EAAOwI,cAAc7H,GAAOX,EAAO6F,UAAY7F,EAAO6F,WAAa2F,IAGvExL,EAAO2J,eAAiBA,EACxB3J,EAAO0J,eAAiBA,GAexB+B,YAZF,WACE,IACIhL,EADS3T,KACW2T,WACpB3F,EAFShO,KAEOgO,OAChBoG,EAHSpU,KAGOoU,OACpBT,EAAW5S,SAAU,IAAOiN,EAAiB,WAAI,IAAOA,EAA0B,qBAAI9J,SACtFkQ,EAAOtP,WAAW,6BAyBpB,IAAI8Z,EAAa,CACfC,cAjBF,SAAwBC,GAEtB,KAAI1P,EAAQC,QADCrP,KACgBgO,OAAO+Q,eADvB/e,KACgDgO,OAAOgK,eADvDhY,KAC+Egf,UAA5F,CACA,IAAI9Z,EAFSlF,KAEGkF,GAChBA,EAAGjE,MAAMge,OAAS,OAClB/Z,EAAGjE,MAAMge,OAASH,EAAS,mBAAqB,eAChD5Z,EAAGjE,MAAMge,OAASH,EAAS,eAAiB,YAC5C5Z,EAAGjE,MAAMge,OAASH,EAAS,WAAa,SAWxCI,gBARF,WAEM9P,EAAQC,OADCrP,KACgBgO,OAAOgK,eADvBhY,KAC+Cgf,WAD/Chf,KAENkF,GAAGjE,MAAMge,OAAS,MAqK3B,IAAIE,EAAe,CACjBC,YA9JF,SAAsBhL,GACpB,IAAIlB,EAASlT,KACT2T,EAAaT,EAAOS,WACpB3F,EAASkF,EAAOlF,OAIpB,GAHIA,EAAOoM,MACTlH,EAAOyL,cAEa,iBAAXvK,GAAuB,WAAYA,EAC5C,IAAK,IAAI/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAClC+R,EAAO/R,IAAMsR,EAAWnJ,OAAO4J,EAAO/R,SAG5CsR,EAAWnJ,OAAO4J,GAEhBpG,EAAOoM,MACTlH,EAAOgL,aAEHlQ,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,UA6ITuM,aAzIF,SAAuBjL,GACrB,IAAIlB,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WACpB+E,EAAcxF,EAAOwF,YAErB1K,EAAOoM,MACTlH,EAAOyL,cAET,IAAI7D,EAAiBpC,EAAc,EACnC,GAAsB,iBAAXtE,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAI/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAClC+R,EAAO/R,IAAMsR,EAAW9I,QAAQuJ,EAAO/R,IAE7CyY,EAAiBpC,EAActE,EAAO9R,YAEtCqR,EAAW9I,QAAQuJ,GAEjBpG,EAAOoM,MACTlH,EAAOgL,aAEHlQ,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,SAETI,EAAOoJ,QAAQxB,EAAgB,GAAG,IAkHlCwE,SA/GF,SAAmBnV,EAAOiK,GACxB,IAAIlB,EAASlT,KACT2T,EAAaT,EAAOS,WACpB3F,EAASkF,EAAOlF,OAEhBuR,EADcrM,EAAOwF,YAErB1K,EAAOoM,OACTmF,GAAqBrM,EAAOgK,aAC5BhK,EAAOyL,cACPzL,EAAOkB,OAAST,EAAW5S,SAAU,IAAOiN,EAAiB,aAE/D,IAAIwR,EAAatM,EAAOkB,OAAO9R,OAC/B,GAAI6H,GAAS,EACX+I,EAAOmM,aAAajL,QAGtB,GAAaoL,GAATrV,EACF+I,EAAOkM,YAAYhL,OADrB,CAOA,IAHA,IAAI0G,EAAqC3Q,EAApBoV,EAA4BA,EAAoB,EAAIA,EAErEE,EAAe,GACVpd,EAAImd,EAAa,EAAQrV,GAAL9H,EAAYA,GAAK,EAAG,CAC/C,IAAIqd,EAAexM,EAAOkB,OAAO9J,GAAGjI,GACpCqd,EAAaxb,SACbub,EAAahZ,QAAQiZ,GAGvB,GAAsB,iBAAXtL,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAIkD,EAAM,EAAGA,EAAMlD,EAAO9R,OAAQgV,GAAO,EACxClD,EAAOkD,IAAQ3D,EAAWnJ,OAAO4J,EAAOkD,IAE9CwD,EAAqC3Q,EAApBoV,EAA4BA,EAAoBnL,EAAO9R,OAASid,OAEjF5L,EAAWnJ,OAAO4J,GAGpB,IAAK,IAAIoD,EAAM,EAAGA,EAAMiI,EAAand,OAAQkV,GAAO,EAClD7D,EAAWnJ,OAAOiV,EAAajI,IAG7BxJ,EAAOoM,MACTlH,EAAOgL,aAEHlQ,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,SAEL9E,EAAOoM,KACTlH,EAAOoJ,QAAQxB,EAAiB5H,EAAOgK,aAAc,GAAG,GAExDhK,EAAOoJ,QAAQxB,EAAgB,GAAG,KA6DpC6E,YAzDF,SAAsBC,GACpB,IAAI1M,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB2F,EAAaT,EAAOS,WAGpB4L,EAFcrM,EAAOwF,YAGrB1K,EAAOoM,OACTmF,GAAqBrM,EAAOgK,aAC5BhK,EAAOyL,cACPzL,EAAOkB,OAAST,EAAW5S,SAAU,IAAOiN,EAAiB,aAE/D,IACI6R,EADA/E,EAAiByE,EAGrB,GAA6B,iBAAlBK,GAA8B,WAAYA,EAAe,CAClE,IAAK,IAAIvd,EAAI,EAAGA,EAAIud,EAActd,OAAQD,GAAK,EAC7Cwd,EAAgBD,EAAcvd,GAC1B6Q,EAAOkB,OAAOyL,IAAkB3M,EAAOkB,OAAO9J,GAAGuV,GAAe3b,SAChE2b,EAAgB/E,IAAkBA,GAAkB,GAE1DA,EAAiBpF,KAAKK,IAAI+E,EAAgB,QAE1C+E,EAAgBD,EACZ1M,EAAOkB,OAAOyL,IAAkB3M,EAAOkB,OAAO9J,GAAGuV,GAAe3b,SAChE2b,EAAgB/E,IAAkBA,GAAkB,GACxDA,EAAiBpF,KAAKK,IAAI+E,EAAgB,GAGxC9M,EAAOoM,MACTlH,EAAOgL,aAGHlQ,EAAOgC,UAAYZ,EAAQY,UAC/BkD,EAAOJ,SAEL9E,EAAOoM,KACTlH,EAAOoJ,QAAQxB,EAAiB5H,EAAOgK,aAAc,GAAG,GAExDhK,EAAOoJ,QAAQxB,EAAgB,GAAG,IAmBpCgF,gBAfF,WAIE,IAHA,IAEIF,EAAgB,GACXvd,EAAI,EAAGA,EAHHrC,KAGcoU,OAAO9R,OAAQD,GAAK,EAC7Cud,EAAc3c,KAAKZ,GAJRrC,KAMN2f,YAAYC,KAWjBG,EAAU,WACZ,IAAIC,EAAK1e,EAAIE,UAAUC,UAEnBwe,EAAS,CACXC,KAAK,EACLC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,QAASpf,EAAIof,SAAWpf,EAAIqf,SAC5BA,SAAUrf,EAAIof,SAAWpf,EAAIqf,UAG3BL,EAAUN,EAAG9c,MAAM,qCACnBid,EAAUH,EAAG9c,MAAM,+BACnBud,EAAOT,EAAG9c,MAAM,wBAChBsd,EAAOR,EAAG9c,MAAM,2BAChBqd,GAAUE,GAAQT,EAAG9c,MAAM,8BA+C/B,GA3CIod,IACFL,EAAOW,GAAK,UACZX,EAAOY,UAAYP,EAAQ,GAC3BL,EAAOK,SAAU,GAGfH,IAAYG,IACdL,EAAOW,GAAK,UACZX,EAAOY,UAAYV,EAAQ,GAC3BF,EAAOE,SAAU,EACjBF,EAAOG,cAAsD,GAAtCJ,EAAGc,cAAche,QAAQ,YAE9C2d,GAAQF,GAAUC,KACpBP,EAAOW,GAAK,MACZX,EAAOC,KAAM,GAGXK,IAAWC,IACbP,EAAOY,UAAYN,EAAO,GAAGlT,QAAQ,KAAM,KAC3C4S,EAAOM,QAAS,GAEdE,IACFR,EAAOY,UAAYJ,EAAK,GAAGpT,QAAQ,KAAM,KACzC4S,EAAOQ,MAAO,GAEZD,IACFP,EAAOY,UAAYL,EAAK,GAAKA,EAAK,GAAGnT,QAAQ,KAAM,KAAO,KAC1D4S,EAAOM,QAAS,GAGdN,EAAOC,KAAOD,EAAOY,WAAuC,GAA1Bb,EAAGld,QAAQ,aACR,OAAnCmd,EAAOY,UAAU1d,MAAM,KAAK,KAC9B8c,EAAOY,UAAYb,EAAGc,cAAc3d,MAAM,YAAY,GAAGA,MAAM,KAAK,IAKxE8c,EAAOI,UAAYJ,EAAOW,IAAMX,EAAOE,SAAWF,EAAOc,SAGzDd,EAAOc,SAAWR,GAAUE,GAAQD,IAASR,EAAG9c,MAAM,8BAGlD+c,EAAOW,IAAoB,QAAdX,EAAOW,GAAc,CACpC,IAAII,EAAef,EAAOY,UAAU1d,MAAM,KACtC8d,EAAehhB,EAAIQ,cAAc,yBACrCwf,EAAOiB,WAAajB,EAAOc,UACrBP,GAAQD,KACU,EAAlBS,EAAa,IAAW,EAA2B,GAAL,EAAlBA,EAAa,GAAoC,EAAJ,EAAlBA,EAAa,KACrEC,GAA8E,GAA9DA,EAAarc,aAAa,WAAW9B,QAAQ,cAOpE,OAHAmd,EAAOkB,WAAa7f,EAAI8f,kBAAoB,EAGrCnB,EAhFI,GAonBb,SAASoB,IACP,IAAInO,EAASlT,KAETgO,EAASkF,EAAOlF,OAChB9I,EAAKgO,EAAOhO,GAEhB,IAAIA,GAAyB,IAAnBA,EAAGwD,YAAb,CAGIsF,EAAOsT,aACTpO,EAAOqO,gBAIT,IAAI3E,EAAiB1J,EAAO0J,eACxBC,EAAiB3J,EAAO2J,eACxBvI,EAAWpB,EAAOoB,SAStB,GANApB,EAAO0J,gBAAiB,EACxB1J,EAAO2J,gBAAiB,EAExB3J,EAAOH,aACPG,EAAOQ,eAEH1F,EAAOwT,SAAU,CACnB,IAAIC,EAAe/L,KAAKgM,IAAIhM,KAAKK,IAAI7C,EAAO6F,UAAW7F,EAAOyG,gBAAiBzG,EAAOmG,gBACtFnG,EAAOwI,aAAa+F,GACpBvO,EAAO2H,oBACP3H,EAAO8G,sBAEHhM,EAAOmO,YACTjJ,EAAOmF,wBAGTnF,EAAO8G,uBACuB,SAAzBhM,EAAO6H,eAAmD,EAAvB7H,EAAO6H,gBAAsB3C,EAAO2G,QAAU3G,EAAOlF,OAAOiJ,eAClG/D,EAAOoJ,QAAQpJ,EAAOkB,OAAO9R,OAAS,EAAG,GAAG,GAAO,GAEnD4Q,EAAOoJ,QAAQpJ,EAAOwF,YAAa,GAAG,GAAO,GAIjDxF,EAAO2J,eAAiBA,EACxB3J,EAAO0J,eAAiBA,EAEpB1J,EAAOlF,OAAOgK,eAAiB1D,IAAapB,EAAOoB,UACrDpB,EAAO+E,iBAsGX,IAAIjR,EAAS,CACX2a,aAxFF,WACE,IAAIzO,EAASlT,KACTgO,EAASkF,EAAOlF,OAChB4T,EAAc1O,EAAO0O,YACrB1c,EAAKgO,EAAOhO,GACZ2c,EAAY3O,EAAO2O,UAGrB3O,EAAO4O,aAvmBX,SAAuB1a,GACrB,IAAI8L,EAASlT,KACTgF,EAAOkO,EAAO6O,gBACd/T,EAASkF,EAAOlF,OAChBgU,EAAU9O,EAAO8O,QACrB,IAAI9O,EAAOmJ,YAAarO,EAAOyO,+BAA/B,CAGA,IAAIpW,EAAIe,EAGR,GAFIf,EAAE4b,gBAAiB5b,EAAIA,EAAE4b,eAC7Bjd,EAAKkd,aAA0B,eAAX7b,EAAE8b,MACjBnd,EAAKkd,gBAAgB,UAAW7b,IAAiB,IAAZA,EAAE+b,WACvCpd,EAAKkd,cAAgB,WAAY7b,GAAgB,EAAXA,EAAEgc,QACzCrd,EAAKsd,WAAatd,EAAKud,SAC3B,GAAIvU,EAAOwU,WAAajgB,EAAE8D,EAAEC,QAAQoF,QAAQsC,EAAOyU,kBAAoBzU,EAAOyU,kBAAqB,IAAOzU,EAAqB,gBAAI,GACjIkF,EAAOwP,YAAa,OAGtB,IAAI1U,EAAO2U,cACJpgB,EAAE8D,GAAGqF,QAAQsC,EAAO2U,cAAc,GADzC,CAIAX,EAAQY,SAAsB,eAAXvc,EAAE8b,KAAwB9b,EAAEwc,cAAc,GAAGC,MAAQzc,EAAEyc,MAC1Ed,EAAQe,SAAsB,eAAX1c,EAAE8b,KAAwB9b,EAAEwc,cAAc,GAAGG,MAAQ3c,EAAE2c,MAC1E,IAAIC,EAASjB,EAAQY,SACjBM,EAASlB,EAAQe,SAIjBI,EAAqBnV,EAAOmV,oBAAsBnV,EAAOoV,sBACzDC,EAAqBrV,EAAOqV,oBAAsBrV,EAAOsV,sBAC7D,IACEH,KACKF,GAAUI,GACXJ,GAAU3hB,EAAIU,OAAOgR,MAAQqQ,GAHnC,CAuBA,GAfAhX,EAAMqC,OAAO1J,EAAM,CACjBsd,WAAW,EACXC,SAAS,EACTgB,qBAAqB,EACrBC,iBAAazc,EACb0c,iBAAa1c,IAGfib,EAAQiB,OAASA,EACjBjB,EAAQkB,OAASA,EACjBle,EAAK0e,eAAiBrX,EAAMM,MAC5BuG,EAAOwP,YAAa,EACpBxP,EAAOH,aACPG,EAAOyQ,oBAAiB5c,EACD,EAAnBiH,EAAO4V,YAAiB5e,EAAK6e,oBAAqB,GACvC,eAAXxd,EAAE8b,KAAuB,CAC3B,IAAI2B,GAAiB,EACjBvhB,EAAE8D,EAAEC,QAAQI,GAAG1B,EAAK+e,gBAAiBD,GAAiB,GAExD7jB,EAAIK,eACDiC,EAAEtC,EAAIK,eAAeoG,GAAG1B,EAAK+e,eAC7B9jB,EAAIK,gBAAkB+F,EAAEC,QAE3BrG,EAAIK,cAAcC,OAEhBujB,GAAkB5Q,EAAO8Q,gBAAkBhW,EAAOiW,0BACpD5d,EAAEyd,iBAGN5Q,EAAO/B,KAAK,aAAc9K,OAmiBW4L,KAAKiB,GACxCA,EAAOgR,YAjiBX,SAAsB9c,GACpB,IAAI8L,EAASlT,KACTgF,EAAOkO,EAAO6O,gBACd/T,EAASkF,EAAOlF,OAChBgU,EAAU9O,EAAO8O,QACjBnO,EAAMX,EAAOY,aACbzN,EAAIe,EAER,GADIf,EAAE4b,gBAAiB5b,EAAIA,EAAE4b,eACxBjd,EAAKsd,WAMV,IAAItd,EAAKkd,cAA2B,cAAX7b,EAAE8b,KAA3B,CACA,IAAIW,EAAmB,cAAXzc,EAAE8b,KAAuB9b,EAAEwc,cAAc,GAAGC,MAAQzc,EAAEyc,MAC9DE,EAAmB,cAAX3c,EAAE8b,KAAuB9b,EAAEwc,cAAc,GAAGG,MAAQ3c,EAAE2c,MAClE,GAAI3c,EAAE8d,wBAGJ,OAFAnC,EAAQiB,OAASH,OACjBd,EAAQkB,OAASF,GAGnB,IAAK9P,EAAO8Q,eAYV,OAVA9Q,EAAOwP,YAAa,OAChB1d,EAAKsd,YACPjW,EAAMqC,OAAOsT,EAAS,CACpBiB,OAAQH,EACRI,OAAQF,EACRJ,SAAUE,EACVC,SAAUC,IAEZhe,EAAK0e,eAAiBrX,EAAMM,QAIhC,GAAI3H,EAAKkd,cAAgBlU,EAAOoW,sBAAwBpW,EAAOoM,KAC7D,GAAIlH,EAAOK,cAET,GACGyP,EAAQhB,EAAQkB,QAAUhQ,EAAO6F,WAAa7F,EAAOyG,gBAClDqJ,EAAQhB,EAAQkB,QAAUhQ,EAAO6F,WAAa7F,EAAOmG,eAIzD,OAFArU,EAAKsd,WAAY,OACjBtd,EAAKud,SAAU,QAGZ,GACJO,EAAQd,EAAQiB,QAAU/P,EAAO6F,WAAa7F,EAAOyG,gBAClDmJ,EAAQd,EAAQiB,QAAU/P,EAAO6F,WAAa7F,EAAOmG,eAEzD,OAGJ,GAAIrU,EAAKkd,cAAgBjiB,EAAIK,eACvB+F,EAAEC,SAAWrG,EAAIK,eAAiBiC,EAAE8D,EAAEC,QAAQI,GAAG1B,EAAK+e,cAGxD,OAFA/e,EAAKud,SAAU,OACfrP,EAAOwP,YAAa,GAOxB,GAHI1d,EAAKue,qBACPrQ,EAAO/B,KAAK,YAAa9K,KAEvBA,EAAEwc,eAA0C,EAAzBxc,EAAEwc,cAAcvgB,QAAvC,CAEA0f,EAAQY,SAAWE,EACnBd,EAAQe,SAAWC,EAEnB,IAKMqB,EALFC,EAAQtC,EAAQY,SAAWZ,EAAQiB,OACnCsB,EAAQvC,EAAQe,SAAWf,EAAQkB,OACvC,KAAIhQ,EAAOlF,OAAO4V,WAAalO,KAAK8O,KAAM9O,KAAK+O,IAAKH,EAAO,GAAQ5O,KAAK+O,IAAKF,EAAO,IAAQrR,EAAOlF,OAAO4V,WAsB1G,QApBgC,IAArB5e,EAAKwe,cAETtQ,EAAOI,gBAAkB0O,EAAQe,WAAaf,EAAQkB,QAAYhQ,EAAOK,cAAgByO,EAAQY,WAAaZ,EAAQiB,OACzHje,EAAKwe,aAAc,EAGsB,IAApCc,EAAQA,EAAUC,EAAQA,IAC7BF,EAA6D,IAA/C3O,KAAKgP,MAAMhP,KAAKwB,IAAIqN,GAAQ7O,KAAKwB,IAAIoN,IAAiB5O,KAAKiP,GACzE3f,EAAKwe,YAActQ,EAAOI,eAAiB+Q,EAAarW,EAAOqW,WAAc,GAAKA,EAAarW,EAAOqW,aAIxGrf,EAAKwe,aACPtQ,EAAO/B,KAAK,oBAAqB9K,QAEH,IAArBrB,EAAKye,cACVzB,EAAQY,WAAaZ,EAAQiB,QAAUjB,EAAQe,WAAaf,EAAQkB,SACtEle,EAAKye,aAAc,IAGnBze,EAAKwe,YACPxe,EAAKsd,WAAY,OAGnB,GAAKtd,EAAKye,YAAV,CAGAvQ,EAAOwP,YAAa,EACpBrc,EAAEyd,iBACE9V,EAAO4W,2BAA6B5W,EAAO6W,QAC7Cxe,EAAEye,kBAGC9f,EAAKud,UACJvU,EAAOoM,MACTlH,EAAOkK,UAETpY,EAAK+f,eAAiB7R,EAAOtG,eAC7BsG,EAAOuF,cAAc,GACjBvF,EAAOmJ,WACTnJ,EAAOS,WAAWjM,QAAQ,qCAE5B1C,EAAKggB,qBAAsB,GAEvBhX,EAAO4Q,aAAyC,IAA1B1L,EAAO0J,iBAAqD,IAA1B1J,EAAO2J,gBACjE3J,EAAO2L,eAAc,GAEvB3L,EAAO/B,KAAK,kBAAmB9K,IAEjC6M,EAAO/B,KAAK,aAAc9K,GAC1BrB,EAAKud,SAAU,EAEf,IAAI7D,EAAOxL,EAAOI,eAAiBgR,EAAQC,EAC3CvC,EAAQtD,KAAOA,EAEfA,GAAQ1Q,EAAOiX,WACXpR,IAAO6K,GAAQA,GAEnBxL,EAAOyQ,eAAwB,EAAPjF,EAAW,OAAS,OAC5C1Z,EAAKyW,iBAAmBiD,EAAO1Z,EAAK+f,eAEpC,IAAIG,GAAsB,EACtBC,EAAkBnX,EAAOmX,gBA0B7B,GAzBInX,EAAOoW,sBACTe,EAAkB,GAER,EAAPzG,GAAY1Z,EAAKyW,iBAAmBvI,EAAOmG,gBAC9C6L,GAAsB,EAClBlX,EAAOoX,aAAcpgB,EAAKyW,iBAAoBvI,EAAOmG,eAAiB,EAAM3D,KAAK+O,KAAOvR,EAAOmG,eAAiBrU,EAAK+f,eAAiBrG,EAAOyG,KACxIzG,EAAO,GAAK1Z,EAAKyW,iBAAmBvI,EAAOyG,iBACpDuL,GAAsB,EAClBlX,EAAOoX,aAAcpgB,EAAKyW,iBAAoBvI,EAAOyG,eAAiB,EAAMjE,KAAK+O,IAAMvR,EAAOyG,eAAiB3U,EAAK+f,eAAiBrG,EAAOyG,KAG9ID,IACF7e,EAAE8d,yBAA0B,IAIzBjR,EAAO0J,gBAA4C,SAA1B1J,EAAOyQ,gBAA6B3e,EAAKyW,iBAAmBzW,EAAK+f,iBAC7F/f,EAAKyW,iBAAmBzW,EAAK+f,iBAE1B7R,EAAO2J,gBAA4C,SAA1B3J,EAAOyQ,gBAA6B3e,EAAKyW,iBAAmBzW,EAAK+f,iBAC7F/f,EAAKyW,iBAAmBzW,EAAK+f,gBAKR,EAAnB/W,EAAO4V,UAAe,CACxB,KAAIlO,KAAKwB,IAAIwH,GAAQ1Q,EAAO4V,WAAa5e,EAAK6e,oBAW5C,YADA7e,EAAKyW,iBAAmBzW,EAAK+f,gBAT7B,IAAK/f,EAAK6e,mBAMR,OALA7e,EAAK6e,oBAAqB,EAC1B7B,EAAQiB,OAASjB,EAAQY,SACzBZ,EAAQkB,OAASlB,EAAQe,SACzB/d,EAAKyW,iBAAmBzW,EAAK+f,oBAC7B/C,EAAQtD,KAAOxL,EAAOI,eAAiB0O,EAAQY,SAAWZ,EAAQiB,OAASjB,EAAQe,SAAWf,EAAQkB,QASvGlV,EAAOqX,gBAGRrX,EAAOwT,UAAYxT,EAAOkK,qBAAuBlK,EAAOmK,yBAC1DjF,EAAO2H,oBACP3H,EAAO8G,uBAELhM,EAAOwT,WAEsB,IAA3Bxc,EAAKsgB,WAAWhjB,QAClB0C,EAAKsgB,WAAWriB,KAAK,CACnBsiB,SAAUvD,EAAQ9O,EAAOI,eAAiB,SAAW,UACrDkS,KAAMxgB,EAAK0e,iBAGf1e,EAAKsgB,WAAWriB,KAAK,CACnBsiB,SAAUvD,EAAQ9O,EAAOI,eAAiB,WAAa,YACvDkS,KAAMnZ,EAAMM,SAIhBuG,EAAOuG,eAAezU,EAAKyW,kBAE3BvI,EAAOwI,aAAa1W,EAAKyW,2BA/LnBzW,EAAKye,aAAeze,EAAKwe,aAC3BtQ,EAAO/B,KAAK,oBAAqB9K,IAuhBF4L,KAAKiB,GACtCA,EAAOuS,WAvVX,SAAqBre,GACnB,IAAI8L,EAASlT,KACTgF,EAAOkO,EAAO6O,gBAEd/T,EAASkF,EAAOlF,OAChBgU,EAAU9O,EAAO8O,QACjBnO,EAAMX,EAAOY,aACbH,EAAaT,EAAOS,WACpBY,EAAarB,EAAOqB,WACpBD,EAAWpB,EAAOoB,SAClBjO,EAAIe,EAMR,GALIf,EAAE4b,gBAAiB5b,EAAIA,EAAE4b,eACzBjd,EAAKue,qBACPrQ,EAAO/B,KAAK,WAAY9K,GAE1BrB,EAAKue,qBAAsB,GACtBve,EAAKsd,UAMR,OALItd,EAAKud,SAAWvU,EAAO4Q,YACzB1L,EAAO2L,eAAc,GAEvB7Z,EAAKud,SAAU,OACfvd,EAAKye,aAAc,GAIjBzV,EAAO4Q,YAAc5Z,EAAKud,SAAWvd,EAAKsd,aAAwC,IAA1BpP,EAAO0J,iBAAqD,IAA1B1J,EAAO2J,iBACnG3J,EAAO2L,eAAc,GAIvB,IAmCI6G,EAnCAC,EAAetZ,EAAMM,MACrBiZ,EAAWD,EAAe3gB,EAAK0e,eAwBnC,GArBIxQ,EAAOwP,aACTxP,EAAOiI,mBAAmB9U,GAC1B6M,EAAO/B,KAAK,MAAO9K,GACfuf,EAAW,KAA6C,IAArCD,EAAe3gB,EAAK6gB,gBACrC7gB,EAAK8gB,cAAgB5jB,aAAa8C,EAAK8gB,cAC3C9gB,EAAK8gB,aAAezZ,EAAMI,SAAS,WAC5ByG,IAAUA,EAAO6J,WACtB7J,EAAO/B,KAAK,QAAS9K,IACpB,MAEDuf,EAAW,KAAQD,EAAe3gB,EAAK6gB,cAAiB,MACtD7gB,EAAK8gB,cAAgB5jB,aAAa8C,EAAK8gB,cAC3C5S,EAAO/B,KAAK,YAAa9K,KAI7BrB,EAAK6gB,cAAgBxZ,EAAMM,MAC3BN,EAAMI,SAAS,WACRyG,EAAO6J,YAAa7J,EAAOwP,YAAa,MAG1C1d,EAAKsd,YAActd,EAAKud,UAAYrP,EAAOyQ,gBAAmC,IAAjB3B,EAAQtD,MAAc1Z,EAAKyW,mBAAqBzW,EAAK+f,eAIrH,OAHA/f,EAAKsd,WAAY,EACjBtd,EAAKud,SAAU,OACfvd,EAAKye,aAAc,GAcrB,GAXAze,EAAKsd,WAAY,EACjBtd,EAAKud,SAAU,EACfvd,EAAKye,aAAc,EAIjBiC,EADE1X,EAAOqX,aACIxR,EAAMX,EAAO6F,WAAa7F,EAAO6F,WAEhC/T,EAAKyW,iBAGjBzN,EAAOwT,SAAX,CACE,GAAIkE,GAAcxS,EAAOmG,eAEvB,YADAnG,EAAOoJ,QAAQpJ,EAAOwF,aAGxB,GAAIgN,GAAcxS,EAAOyG,eAMvB,YALIzG,EAAOkB,OAAO9R,OAASgS,EAAShS,OAClC4Q,EAAOoJ,QAAQhI,EAAShS,OAAS,GAEjC4Q,EAAOoJ,QAAQpJ,EAAOkB,OAAO9R,OAAS,IAK1C,GAAI0L,EAAO+X,iBAAkB,CAC3B,GAA6B,EAAzB/gB,EAAKsgB,WAAWhjB,OAAY,CAC9B,IAAI0jB,EAAgBhhB,EAAKsgB,WAAWW,MAChCC,EAAgBlhB,EAAKsgB,WAAWW,MAEhCE,EAAWH,EAAcT,SAAWW,EAAcX,SAClDC,EAAOQ,EAAcR,KAAOU,EAAcV,KAC9CtS,EAAOkT,SAAWD,EAAWX,EAC7BtS,EAAOkT,UAAY,EACf1Q,KAAKwB,IAAIhE,EAAOkT,UAAYpY,EAAOqY,0BACrCnT,EAAOkT,SAAW,IAIT,IAAPZ,GAAmD,IAApCnZ,EAAMM,MAAQqZ,EAAcR,QAC7CtS,EAAOkT,SAAW,QAGpBlT,EAAOkT,SAAW,EAEpBlT,EAAOkT,UAAYpY,EAAOsY,8BAE1BthB,EAAKsgB,WAAWhjB,OAAS,EACzB,IAAIikB,EAAmB,IAAOvY,EAAOwY,sBACjCC,EAAmBvT,EAAOkT,SAAWG,EAErCG,EAAcxT,EAAO6F,UAAY0N,EACjC5S,IAAO6S,GAAeA,GAE1B,IACIC,EAEAC,EAHAC,GAAW,EAEXC,EAA2C,GAA5BpR,KAAKwB,IAAIhE,EAAOkT,UAAiBpY,EAAO+Y,4BAE3D,GAAIL,EAAcxT,EAAOyG,eACnB3L,EAAOgZ,wBACLN,EAAcxT,EAAOyG,gBAAkBmN,IACzCJ,EAAcxT,EAAOyG,eAAiBmN,GAExCH,EAAsBzT,EAAOyG,eAC7BkN,GAAW,EACX7hB,EAAKggB,qBAAsB,GAE3B0B,EAAcxT,EAAOyG,eAEnB3L,EAAOoM,MAAQpM,EAAOiJ,iBAAkB2P,GAAe,QACtD,GAAIF,EAAcxT,EAAOmG,eAC1BrL,EAAOgZ,wBACLN,EAAcxT,EAAOmG,eAAiByN,IACxCJ,EAAcxT,EAAOmG,eAAiByN,GAExCH,EAAsBzT,EAAOmG,eAC7BwN,GAAW,EACX7hB,EAAKggB,qBAAsB,GAE3B0B,EAAcxT,EAAOmG,eAEnBrL,EAAOoM,MAAQpM,EAAOiJ,iBAAkB2P,GAAe,QACtD,GAAI5Y,EAAOiZ,eAAgB,CAEhC,IADA,IAAI1M,EACKzW,EAAI,EAAGA,EAAIwQ,EAAShS,OAAQwB,GAAK,EACxC,GAAIwQ,EAASxQ,IAAM4iB,EAAa,CAC9BnM,EAAYzW,EACZ,MASJ4iB,IAJEA,EADEhR,KAAKwB,IAAI5C,EAASiG,GAAamM,GAAehR,KAAKwB,IAAI5C,EAASiG,EAAY,GAAKmM,IAA0C,SAA1BxT,EAAOyQ,eAC5FrP,EAASiG,GAETjG,EAASiG,EAAY,IAUvC,GANIqM,GACF1T,EAAOlC,KAAK,gBAAiB,WAC3BkC,EAAOkK,YAIa,IAApBlK,EAAOkT,SAEPG,EADE1S,EACiB6B,KAAKwB,MAAMwP,EAAcxT,EAAO6F,WAAa7F,EAAOkT,UAEpD1Q,KAAKwB,KAAKwP,EAAcxT,EAAO6F,WAAa7F,EAAOkT,eAEnE,GAAIpY,EAAOiZ,eAEhB,YADA/T,EAAO4K,iBAIL9P,EAAOgZ,wBAA0BH,GACnC3T,EAAOuG,eAAekN,GACtBzT,EAAOuF,cAAc8N,GACrBrT,EAAOwI,aAAagL,GACpBxT,EAAO8I,iBAAgB,EAAM9I,EAAOyQ,gBACpCzQ,EAAOmJ,WAAY,EACnB1I,EAAWzL,cAAc,WAClBgL,IAAUA,EAAO6J,WAAc/X,EAAKggB,sBACzC9R,EAAO/B,KAAK,kBAEZ+B,EAAOuF,cAAczK,EAAOsK,OAC5BpF,EAAOwI,aAAaiL,GACpBhT,EAAWzL,cAAc,WAClBgL,IAAUA,EAAO6J,WACtB7J,EAAOhL,sBAGFgL,EAAOkT,UAChBlT,EAAOuG,eAAeiN,GACtBxT,EAAOuF,cAAc8N,GACrBrT,EAAOwI,aAAagL,GACpBxT,EAAO8I,iBAAgB,EAAM9I,EAAOyQ,gBAC/BzQ,EAAOmJ,YACVnJ,EAAOmJ,WAAY,EACnB1I,EAAWzL,cAAc,WAClBgL,IAAUA,EAAO6J,WACtB7J,EAAOhL,oBAIXgL,EAAOuG,eAAeiN,GAGxBxT,EAAO2H,oBACP3H,EAAO8G,2BACF,GAAIhM,EAAOiZ,eAEhB,YADA/T,EAAO4K,mBAIJ9P,EAAO+X,kBAAoBH,GAAY5X,EAAOkZ,gBACjDhU,EAAOuG,iBACPvG,EAAO2H,oBACP3H,EAAO8G,2BAnJX,CA2JA,IAFA,IAAImN,EAAY,EACZC,EAAYlU,EAAOsB,gBAAgB,GAC9BnS,EAAI,EAAGA,EAAIkS,EAAWjS,OAAQD,GAAK2L,EAAOmJ,oBACI,IAA1C5C,EAAWlS,EAAI2L,EAAOmJ,gBAC3BuO,GAAcnR,EAAWlS,IAAMqjB,EAAanR,EAAWlS,EAAI2L,EAAOmJ,kBAEpEiQ,EAAY7S,GADZ4S,EAAY9kB,GACe2L,EAAOmJ,gBAAkB5C,EAAWlS,IAExDqjB,GAAcnR,EAAWlS,KAClC8kB,EAAY9kB,EACZ+kB,EAAY7S,EAAWA,EAAWjS,OAAS,GAAKiS,EAAWA,EAAWjS,OAAS,IAKnF,IAAI+kB,GAAS3B,EAAanR,EAAW4S,IAAcC,EAEnD,GAAIxB,EAAW5X,EAAOkZ,aAAc,CAElC,IAAKlZ,EAAOsZ,WAEV,YADApU,EAAOoJ,QAAQpJ,EAAOwF,aAGM,SAA1BxF,EAAOyQ,iBACL0D,GAASrZ,EAAOuZ,gBAAmBrU,EAAOoJ,QAAQ6K,EAAYnZ,EAAOmJ,gBAClEjE,EAAOoJ,QAAQ6K,IAEM,SAA1BjU,EAAOyQ,iBACL0D,EAAS,EAAIrZ,EAAOuZ,gBAAoBrU,EAAOoJ,QAAQ6K,EAAYnZ,EAAOmJ,gBACvEjE,EAAOoJ,QAAQ6K,QAEnB,CAEL,IAAKnZ,EAAOwZ,YAEV,YADAtU,EAAOoJ,QAAQpJ,EAAOwF,aAGM,SAA1BxF,EAAOyQ,gBACTzQ,EAAOoJ,QAAQ6K,EAAYnZ,EAAOmJ,gBAEN,SAA1BjE,EAAOyQ,gBACTzQ,EAAOoJ,QAAQ6K,MA6EclV,KAAKiB,GAGtCA,EAAOuU,QAxBT,SAAkBphB,GACHrG,KACD0iB,aADC1iB,KAEAgO,OAAO0Z,eAAiBrhB,EAAEyd,iBAF1B9jB,KAGAgO,OAAO2Z,0BAHP3nB,KAG0Cqc,YACnDhW,EAAEye,kBACFze,EAAEuhB,8BAkBmB3V,KAAKiB,GAE9B,IAAI5M,EAAsC,cAA7B0H,EAAO6Z,kBAAoC3iB,EAAK2c,EACzD1b,IAAY6H,EAAO6W,OAIrB,GAAKzV,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,sBAIjD,CACL,GAAIP,EAAQC,MAAO,CACjB,IAAIY,IAAwC,eAAtB2R,EAAYkG,QAA0B1Y,EAAQa,kBAAmBjC,EAAO+Z,mBAAmB,CAAEC,SAAS,EAAM7hB,SAAS,GAC3IG,EAAOlG,iBAAiBwhB,EAAYkG,MAAO5U,EAAO4O,aAAc7R,GAChE3J,EAAOlG,iBAAiBwhB,EAAYqG,KAAM/U,EAAOgR,YAAa9U,EAAQa,gBAAkB,CAAE+X,SAAS,EAAO7hB,QAASA,GAAYA,GAC/HG,EAAOlG,iBAAiBwhB,EAAYsG,IAAKhV,EAAOuS,WAAYxV,IAEzDjC,EAAO+Q,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAanS,EAAO+Q,gBAAkB3P,EAAQC,OAAS0Q,EAAOG,OAChH5Z,EAAOlG,iBAAiB,YAAa8S,EAAO4O,cAAc,GAC1D7hB,EAAIG,iBAAiB,YAAa8S,EAAOgR,YAAa/d,GACtDlG,EAAIG,iBAAiB,UAAW8S,EAAOuS,YAAY,SAbrDnf,EAAOlG,iBAAiBwhB,EAAYkG,MAAO5U,EAAO4O,cAAc,GAChE7hB,EAAIG,iBAAiBwhB,EAAYqG,KAAM/U,EAAOgR,YAAa/d,GAC3DlG,EAAIG,iBAAiBwhB,EAAYsG,IAAKhV,EAAOuS,YAAY,IAevDzX,EAAO0Z,eAAiB1Z,EAAO2Z,2BACjCrhB,EAAOlG,iBAAiB,QAAS8S,EAAOuU,SAAS,GAKrDvU,EAAOtN,GAAIma,EAAOG,KAAOH,EAAOI,QAAU,0CAA4C,wBAA0BkB,GAAU,IA6C1H8G,aA1CF,WACE,IAAIjV,EAASlT,KAETgO,EAASkF,EAAOlF,OAChB4T,EAAc1O,EAAO0O,YACrB1c,EAAKgO,EAAOhO,GACZ2c,EAAY3O,EAAO2O,UAEnBvb,EAAsC,cAA7B0H,EAAO6Z,kBAAoC3iB,EAAK2c,EACzD1b,IAAY6H,EAAO6W,OAIrB,GAAKzV,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,sBAIjD,CACL,GAAIP,EAAQC,MAAO,CACjB,IAAIY,IAAwC,iBAAtB2R,EAAYkG,QAA4B1Y,EAAQa,kBAAmBjC,EAAO+Z,mBAAmB,CAAEC,SAAS,EAAM7hB,SAAS,GAC7IG,EAAOjG,oBAAoBuhB,EAAYkG,MAAO5U,EAAO4O,aAAc7R,GACnE3J,EAAOjG,oBAAoBuhB,EAAYqG,KAAM/U,EAAOgR,YAAa/d,GACjEG,EAAOjG,oBAAoBuhB,EAAYsG,IAAKhV,EAAOuS,WAAYxV,IAE5DjC,EAAO+Q,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAanS,EAAO+Q,gBAAkB3P,EAAQC,OAAS0Q,EAAOG,OAChH5Z,EAAOjG,oBAAoB,YAAa6S,EAAO4O,cAAc,GAC7D7hB,EAAII,oBAAoB,YAAa6S,EAAOgR,YAAa/d,GACzDlG,EAAII,oBAAoB,UAAW6S,EAAOuS,YAAY,SAbxDnf,EAAOjG,oBAAoBuhB,EAAYkG,MAAO5U,EAAO4O,cAAc,GACnE7hB,EAAII,oBAAoBuhB,EAAYqG,KAAM/U,EAAOgR,YAAa/d,GAC9DlG,EAAII,oBAAoBuhB,EAAYsG,IAAKhV,EAAOuS,YAAY,IAe1DzX,EAAO0Z,eAAiB1Z,EAAO2Z,2BACjCrhB,EAAOjG,oBAAoB,QAAS6S,EAAOuU,SAAS,GAKxDvU,EAAO5L,IAAKyY,EAAOG,KAAOH,EAAOI,QAAU,0CAA4C,wBAA0BkB,KAiEnH,IAIQrB,EAJJsB,EAAc,CAAEC,cAzDpB,WACE,IAAIrO,EAASlT,KACT0Y,EAAcxF,EAAOwF,YACrBiE,EAAczJ,EAAOyJ,YACrBO,EAAehK,EAAOgK,kBAAoC,IAAjBA,IAA0BA,EAAe,GACtF,IAAIlP,EAASkF,EAAOlF,OAChBsT,EAActT,EAAOsT,YACzB,GAAKA,KAAgBA,GAAmD,IAApCtV,OAAOC,KAAKqV,GAAahf,QAA7D,CAEA,IAAI8lB,EAAalV,EAAOmV,cAAc/G,GACtC,GAAI8G,GAAclV,EAAOoV,oBAAsBF,EAAY,CACzD,IAAIG,EAAoBH,KAAc9G,EAAcA,EAAY8G,GAAclV,EAAOsV,eACjFC,EAAcza,EAAOoM,MAASmO,EAAkB1S,gBAAkB7H,EAAO6H,cAE7ExJ,EAAMqC,OAAOwE,EAAOlF,OAAQua,GAE5Blc,EAAMqC,OAAOwE,EAAQ,CACnB8Q,eAAgB9Q,EAAOlF,OAAOgW,eAC9BpH,eAAgB1J,EAAOlF,OAAO4O,eAC9BC,eAAgB3J,EAAOlF,OAAO6O,iBAGhC3J,EAAOoV,kBAAoBF,EAEvBK,GAAe9L,IACjBzJ,EAAOyL,cACPzL,EAAOgL,aACPhL,EAAOQ,eACPR,EAAOoJ,QAAS5D,EAAcwE,EAAgBhK,EAAOgK,aAAc,GAAG,IAExEhK,EAAO/B,KAAK,aAAcoX,MA2BoBF,cAvBlD,SAAwB/G,GAGtB,GAAKA,EAAL,CACA,IAAI8G,GAAa,EACbM,EAAS,GACb1c,OAAOC,KAAKqV,GAAapV,QAAQ,SAAUyc,GACzCD,EAAOzlB,KAAK0lB,KAEdD,EAAOE,KAAK,SAAUxb,EAAGyb,GAAK,OAAOrV,SAASpG,EAAG,IAAMoG,SAASqV,EAAG,MACnE,IAAK,IAAIxmB,EAAI,EAAGA,EAAIqmB,EAAOpmB,OAAQD,GAAK,EAAG,CACzC,IAAIsmB,EAAQD,EAAOrmB,GAVRrC,KAWAgO,OAAO8a,mBACZH,GAASrnB,EAAIynB,aACfX,EAAaO,GAENA,GAASrnB,EAAIynB,aAAeX,IACrCA,EAAaO,GAGjB,OAAOP,GAAc,SAKnBY,EAKK,CACLC,OAAQ3nB,EAAIE,UAAUC,UAAUyB,MAAM,eAAiB5B,EAAIE,UAAUC,UAAUyB,MAAM,SACrFgmB,SAAU5nB,EAAIE,UAAUC,UAAUyB,MAAM,SACxCimB,UANInJ,EAAK1e,EAAIE,UAAUC,UAAUqf,cACD,GAAxBd,EAAGld,QAAQ,WAAkBkd,EAAGld,QAAQ,UAAY,GAAKkd,EAAGld,QAAQ,WAAa,GAMzFsmB,YAAa,+CAA+CC,KAAK/nB,EAAIE,UAAUC,YAmInF,IAEI6nB,EAAW,CACbC,MAAM,EACNrN,UAAW,aACX2L,kBAAmB,YACnBnL,aAAc,EACdpE,MAAO,IAEPmE,gCAAgC,EAGhC0G,oBAAoB,EACpBE,mBAAoB,GAGpB7B,UAAU,EACVuE,kBAAkB,EAClBS,sBAAuB,EACvBQ,wBAAwB,EACxBD,4BAA6B,EAC7BT,8BAA+B,EAC/BW,gBAAgB,EAChBZ,wBAAyB,IAGzBlK,YAAY,EAGZ9E,gBAAgB,EAGhBmE,kBAAkB,EAGlBpE,OAAQ,QAGRkK,iBAAava,EACb+hB,oBAAoB,EAGpB/T,aAAc,EACdc,cAAe,EACfJ,gBAAiB,EACjBK,oBAAqB,SACrBqB,eAAgB,EAChBF,gBAAgB,EAChBvC,mBAAoB,EACpBE,kBAAmB,EACnBsG,qBAAqB,EACrBxD,0BAA0B,EAG1BM,eAAe,EAGfjB,cAAc,EAGdkO,WAAY,EACZZ,WAAY,GACZtF,eAAe,EACfyI,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBL,aAAc,IACd7B,cAAc,EACdrB,gBAAgB,EAChBJ,UAAW,EACXgB,0BAA0B,EAC1BX,0BAA0B,EAC1BG,qBAAqB,EAGrBoF,mBAAmB,EAGnBpE,YAAY,EACZD,gBAAiB,IAGjBjN,qBAAqB,EACrBC,uBAAuB,EAGvByG,YAAY,EAGZ8I,eAAe,EACfC,0BAA0B,EAC1BpM,qBAAqB,EAGrBkO,eAAe,EACfC,qBAAqB,EAGrBtP,MAAM,EACNkE,qBAAsB,EACtBpB,aAAc,KACdiB,wBAAwB,EAGxBtB,gBAAgB,EAChBD,gBAAgB,EAChB+F,aAAc,KACdH,WAAW,EACXmH,eAAgB,oBAChBlH,kBAAmB,KAGnBsF,kBAAkB,EAGlB6B,uBAAwB,oBACxBC,WAAY,eACZC,gBAAiB,+BACjB3P,iBAAkB,sBAClBG,0BAA2B,gCAC3BrB,kBAAmB,uBACnBoB,oBAAqB,yBACrBG,eAAgB,oBAChBG,wBAAyB,8BACzBD,eAAgB,oBAChBE,wBAAyB,8BACzBmP,aAAc,iBAGdC,oBAAoB,GAGlBC,EAAa,CACfnX,OAAQA,EACRiG,UAAWA,EACXvT,WAAYuW,EACZ5F,MAAOA,EACPiE,KAAMA,EACNwE,WAAYA,EACZO,aAAcA,EACdnY,OAAQA,EACRsa,YAAaA,EACbrJ,cA9IoB,CAAEA,cAjBxB,WACE,IAAI/E,EAASlT,KACTkqB,EAAYhX,EAAO8L,SAEvB9L,EAAO8L,SAAsC,IAA3B9L,EAAOoB,SAAShS,OAClC4Q,EAAO0J,gBAAkB1J,EAAO8L,SAChC9L,EAAO2J,gBAAkB3J,EAAO8L,SAG5BkL,IAAchX,EAAO8L,UAAY9L,EAAO/B,KAAK+B,EAAO8L,SAAW,OAAS,UAExEkL,GAAaA,IAAchX,EAAO8L,WACpC9L,EAAO2G,OAAQ,EACf3G,EAAOiX,WAAWrX,YAmJpBjP,QA3NY,CAAEumB,WAnDhB,WACE,IACIC,EADSrqB,KACWqqB,WACpBrc,EAFShO,KAEOgO,OAChB6F,EAHS7T,KAGI6T,IACbV,EAJSnT,KAIImT,IACbmX,EAAW,GAEfA,EAASrnB,KAAK+K,EAAOkO,WAEjBlO,EAAOwT,UACT8I,EAASrnB,KAAK,aAEXmM,EAAQW,SACXua,EAASrnB,KAAK,cAEZ+K,EAAOmO,YACTmO,EAASrnB,KAAK,cAEZ4Q,GACFyW,EAASrnB,KAAK,OAEa,EAAzB+K,EAAOyH,iBACT6U,EAASrnB,KAAK,YAEZ8c,EAAOI,SACTmK,EAASrnB,KAAK,WAEZ8c,EAAOG,KACToK,EAASrnB,KAAK,QAGX+lB,EAAQC,MAAQD,EAAQE,UAAY9Z,EAAQI,eAAiBJ,EAAQO,wBACxE2a,EAASrnB,KAAM,OAAU+K,EAAgB,WAG3Csc,EAASpe,QAAQ,SAAUqe,GACzBF,EAAWpnB,KAAK+K,EAAO4b,uBAAyBW,KAGlDpX,EAAIxP,SAAS0mB,EAAW/c,KAAK,OAWSkd,cARxC,WACE,IACIrX,EADSnT,KACImT,IACbkX,EAFSrqB,KAEWqqB,WAExBlX,EAAIlP,YAAYomB,EAAW/c,KAAK,QA+NhCmd,OAtKW,CACXC,UArDF,SAAoBC,EAASC,EAAKC,EAAQC,EAAOC,EAAkB5iB,GACjE,IAAI6iB,EACJ,SAASC,IACH9iB,GAAYA,IAEbwiB,EAAQO,UAAaH,EAmBxBE,IAlBIL,IACFI,EAAQ,IAAI1pB,EAAIQ,OACVqpB,OAASF,EACfD,EAAMI,QAAUH,EACZH,IACFE,EAAMF,MAAQA,GAEZD,IACFG,EAAMH,OAASA,GAEbD,IACFI,EAAMJ,IAAMA,IAGdK,KAkCJxB,cA1BF,WACE,IAAIvW,EAASlT,KAEb,SAASirB,IACH,MAAO/X,GAA8CA,IAAUA,EAAO6J,iBAC9ChW,IAAxBmM,EAAOmY,eAA8BnY,EAAOmY,cAAgB,GAC5DnY,EAAOmY,eAAiBnY,EAAOoY,aAAahpB,SAC1C4Q,EAAOlF,OAAO0b,qBAAuBxW,EAAOJ,SAChDI,EAAO/B,KAAK,iBANhB+B,EAAOoY,aAAepY,EAAOC,IAAIxH,KAAK,OAStC,IAAK,IAAItJ,EAAI,EAAGA,EAAI6Q,EAAOoY,aAAahpB,OAAQD,GAAK,EAAG,CACtD,IAAIsoB,EAAUzX,EAAOoY,aAAajpB,GAClC6Q,EAAOwX,UACLC,EACAA,EAAQY,YAAcZ,EAAQ/lB,aAAa,OAC3C+lB,EAAQE,QAAUF,EAAQ/lB,aAAa,UACvC+lB,EAAQG,OAASH,EAAQ/lB,aAAa,UACtC,EACAqmB,OA8KFO,EAAmB,GAEnBzrB,EAAU,SAAU0rB,GACtB,SAAS1rB,IAIP,IAHA,IAAI8F,EAIAX,EACA8I,EAHAlI,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAGrB,IAAhBD,EAAKxD,QAAgBwD,EAAK,GAAG2I,aAAe3I,EAAK,GAAG2I,cAAgBzC,OACtEgC,EAASlI,EAAK,IAEEZ,GAAfW,EAASC,GAAkB,GAAIkI,EAASnI,EAAO,IAE7CmI,IAAUA,EAAS,IAExBA,EAAS3B,EAAMqC,OAAO,GAAIV,GACtB9I,IAAO8I,EAAO9I,KAAM8I,EAAO9I,GAAKA,GAEpCumB,EAAenjB,KAAKtI,KAAMgO,GAE1BhC,OAAOC,KAAKge,GAAY/d,QAAQ,SAAUwf,GACxC1f,OAAOC,KAAKge,EAAWyB,IAAiBxf,QAAQ,SAAUyf,GACnD5rB,EAAOyD,UAAUmoB,KACpB5rB,EAAOyD,UAAUmoB,GAAe1B,EAAWyB,GAAgBC,QAMjE,IAAIzY,EAASlT,UACiB,IAAnBkT,EAAOxB,UAChBwB,EAAOxB,QAAU,IAEnB1F,OAAOC,KAAKiH,EAAOxB,SAASxF,QAAQ,SAAUyF,GAC5C,IAAI/R,EAASsT,EAAOxB,QAAQC,GAC5B,GAAI/R,EAAOoO,OAAQ,CACjB,IAAI4d,EAAkB5f,OAAOC,KAAKrM,EAAOoO,QAAQ,GAC7C8D,EAAelS,EAAOoO,OAAO4d,GACjC,GAA4B,iBAAjB9Z,GAA8C,OAAjBA,EAAyB,OACjE,KAAM8Z,KAAmB5d,GAAU,YAAa8D,GAAiB,QACjC,IAA5B9D,EAAO4d,KACT5d,EAAO4d,GAAmB,CAAE1X,SAAS,IAGF,iBAA5BlG,EAAO4d,IACT,YAAa5d,EAAO4d,KAEzB5d,EAAO4d,GAAiB1X,SAAU,GAE/BlG,EAAO4d,KAAoB5d,EAAO4d,GAAmB,CAAE1X,SAAS,OAKzE,IAAI2X,EAAexf,EAAMqC,OAAO,GAAI4a,GACpCpW,EAAO3B,iBAAiBsa,GAGxB3Y,EAAOlF,OAAS3B,EAAMqC,OAAO,GAAImd,EAAcL,EAAkBxd,GACjEkF,EAAOsV,eAAiBnc,EAAMqC,OAAO,GAAIwE,EAAOlF,QAChDkF,EAAO4Y,aAAezf,EAAMqC,OAAO,GAAIV,GAMvC,IAAImF,GAHJD,EAAO3Q,EAAIA,GAGC2Q,EAAOlF,OAAO9I,IAG1B,GAFAA,EAAKiO,EAAI,GAET,CAIA,GAAiB,EAAbA,EAAI7Q,OAAY,CAClB,IAAIypB,EAAU,GAKd,OAJA5Y,EAAIvJ,KAAK,SAAUO,EAAO6hB,GACxB,IAAIC,EAAY5f,EAAMqC,OAAO,GAAIV,EAAQ,CAAE9I,GAAI8mB,IAC/CD,EAAQ9oB,KAAK,IAAIlD,EAAOksB,MAEnBF,EAGT7mB,EAAGgO,OAASA,EACZC,EAAInO,KAAK,SAAUkO,GAGnB,IAmDQ7D,EACAgR,EApDJ1M,EAAaR,EAAIpS,SAAU,IAAOmS,EAAOlF,OAAmB,cAwHhE,OArHA3B,EAAMqC,OAAOwE,EAAQ,CACnBC,IAAKA,EACLjO,GAAIA,EACJyO,WAAYA,EACZkO,UAAWlO,EAAW,GAGtB0W,WAAY,GAGZjW,OAAQ7R,IACRgS,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAGjBlB,aAAc,WACZ,MAAmC,eAA5BJ,EAAOlF,OAAOkO,WAEvB3I,WAAY,WACV,MAAmC,aAA5BL,EAAOlF,OAAOkO,WAGvBrI,IAA+B,QAAzB3O,EAAGkX,IAAI0E,eAAoD,QAAzB3N,EAAI1J,IAAI,aAChDqK,aAA0C,eAA5BZ,EAAOlF,OAAOkO,YAAwD,QAAzBhX,EAAGkX,IAAI0E,eAAoD,QAAzB3N,EAAI1J,IAAI,cACrGsK,SAAwC,gBAA9BJ,EAAWlK,IAAI,WAGzBiP,YAAa,EACbwB,UAAW,EAGXN,aAAa,EACbC,OAAO,EAGPd,UAAW,EACX+C,kBAAmB,EACnBtC,SAAU,EACV4M,SAAU,EACV/J,WAAW,EAGXO,eAAgB1J,EAAOlF,OAAO4O,eAC9BC,eAAgB3J,EAAOlF,OAAO6O,eAG9B+E,aACMvS,EAAQ,CAAC,aAAc,YAAa,YACpCgR,EAAU,CAAC,YAAa,YAAa,WACrCjR,EAAQI,cACV6Q,EAAU,CAAC,cAAe,cAAe,aAChCjR,EAAQO,wBACjB0Q,EAAU,CAAC,gBAAiB,gBAAiB,gBAE/CnN,EAAOgZ,iBAAmB,CACxBpE,MAAOzY,EAAM,GACb4Y,KAAM5Y,EAAM,GACZ6Y,IAAK7Y,EAAM,IAEb6D,EAAOiZ,mBAAqB,CAC1BrE,MAAOzH,EAAQ,GACf4H,KAAM5H,EAAQ,GACd6H,IAAK7H,EAAQ,IAERjR,EAAQC,QAAU6D,EAAOlF,OAAO+Q,cAAgB7L,EAAOgZ,iBAAmBhZ,EAAOiZ,oBAE1FpK,gBAAiB,CACfO,eAAWvb,EACXwb,aAASxb,EACTwc,yBAAqBxc,EACrB2c,oBAAgB3c,EAChByc,iBAAazc,EACb0U,sBAAkB1U,EAClBge,oBAAgBhe,EAChB8c,wBAAoB9c,EAEpBgd,aAAc,iDAEd8B,cAAexZ,EAAMM,MACrBmZ,kBAAc/e,EAEdue,WAAY,GACZN,yBAAqBje,EACrBmb,kBAAcnb,EACd0c,iBAAa1c,GAIf2b,YAAY,EAGZsB,eAAgB9Q,EAAOlF,OAAOgW,eAE9BhC,QAAS,CACPiB,OAAQ,EACRC,OAAQ,EACRN,SAAU,EACVG,SAAU,EACVrE,KAAM,GAIR4M,aAAc,GACdD,aAAc,IAKhBnY,EAAOtB,aAGHsB,EAAOlF,OAAOub,MAChBrW,EAAOqW,OAIFrW,GAGJuY,IAAiB1rB,EAAOqsB,UAAYX,GAIzC,IAAI9a,EAAkB,CAAE6a,iBAAkB,CAAE3a,cAAc,GAAOyY,SAAU,CAAEzY,cAAc,GAAOpN,MAAO,CAAEoN,cAAc,GAAOtO,EAAG,CAAEsO,cAAc,IA4NnJ,QA/NA9Q,EAAOyD,UAAYwI,OAAOmG,OAAQsZ,GAAkBA,EAAejoB,YAClDiL,YAAc1O,GAIxByD,UAAUwa,qBAAuB,WACtC,IAAI9K,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBoG,EAASlB,EAAOkB,OAChBG,EAAarB,EAAOqB,WACpBX,EAAaV,EAAOO,KACpBiF,EAAcxF,EAAOwF,YACrB2T,EAAM,EACV,GAAIre,EAAOiJ,eAAgB,CAGzB,IAFA,IACIqV,EADAnX,EAAYf,EAAOsE,GAAa1B,gBAE3B3U,EAAIqW,EAAc,EAAGrW,EAAI+R,EAAO9R,OAAQD,GAAK,EAChD+R,EAAO/R,KAAOiqB,IAEhBD,GAAO,EACSzY,GAFhBuB,GAAaf,EAAO/R,GAAG2U,mBAEOsV,GAAY,IAG9C,IAAK,IAAIhV,EAAMoB,EAAc,EAAU,GAAPpB,EAAUA,GAAO,EAC3ClD,EAAOkD,KAASgV,IAElBD,GAAO,EACSzY,GAFhBuB,GAAaf,EAAOkD,GAAKN,mBAEKsV,GAAY,SAI9C,IAAK,IAAI9U,EAAMkB,EAAc,EAAGlB,EAAMpD,EAAO9R,OAAQkV,GAAO,EACtDjD,EAAWiD,GAAOjD,EAAWmE,GAAe9E,IAC9CyY,GAAO,GAIb,OAAOA,GAGTtsB,EAAOyD,UAAUsP,OAAS,WACxB,IAAII,EAASlT,KACb,GAAKkT,IAAUA,EAAO6J,UAAtB,CACA,IAAIzI,EAAWpB,EAAOoB,SAClBtG,EAASkF,EAAOlF,OAEhBA,EAAOsT,aACTpO,EAAOqO,gBAETrO,EAAOH,aACPG,EAAOQ,eACPR,EAAOuG,iBACPvG,EAAO8G,sBAUH9G,EAAOlF,OAAOwT,UAChB9F,IACIxI,EAAOlF,OAAOmO,YAChBjJ,EAAOmF,sBAG4B,SAAhCnF,EAAOlF,OAAO6H,eAA0D,EAA9B3C,EAAOlF,OAAO6H,gBAAsB3C,EAAO2G,QAAU3G,EAAOlF,OAAOiJ,eACnG/D,EAAOoJ,QAAQpJ,EAAOkB,OAAO9R,OAAS,EAAG,GAAG,GAAO,GAEnD4Q,EAAOoJ,QAAQpJ,EAAOwF,YAAa,GAAG,GAAO,KAG1DgD,IAGA1N,EAAOgK,eAAiB1D,IAAapB,EAAOoB,UAC9CpB,EAAO+E,gBAET/E,EAAO/B,KAAK,UA1BZ,SAASuK,IACP,IAAI6Q,EAAiBrZ,EAAOY,cAAmC,EAApBZ,EAAO6F,UAAiB7F,EAAO6F,UACtE0I,EAAe/L,KAAKgM,IAAIhM,KAAKK,IAAIwW,EAAgBrZ,EAAOyG,gBAAiBzG,EAAOmG,gBACpFnG,EAAOwI,aAAa+F,GACpBvO,EAAO2H,oBACP3H,EAAO8G,wBAwBXja,EAAOyD,UAAU+lB,KAAO,WACtB,IAAIrW,EAASlT,KACTkT,EAAOyJ,cAEXzJ,EAAO/B,KAAK,cAGR+B,EAAOlF,OAAOsT,aAChBpO,EAAOqO,gBAITrO,EAAOkX,aAGHlX,EAAOlF,OAAOoM,MAChBlH,EAAOgL,aAIThL,EAAOH,aAGPG,EAAOQ,eAEHR,EAAOlF,OAAOgK,eAChB9E,EAAO+E,gBAIL/E,EAAOlF,OAAO4Q,YAChB1L,EAAO2L,gBAGL3L,EAAOlF,OAAOyb,eAChBvW,EAAOuW,gBAILvW,EAAOlF,OAAOoM,KAChBlH,EAAOoJ,QAAQpJ,EAAOlF,OAAO0O,aAAexJ,EAAOgK,aAAc,EAAGhK,EAAOlF,OAAOgc,oBAElF9W,EAAOoJ,QAAQpJ,EAAOlF,OAAO0O,aAAc,EAAGxJ,EAAOlF,OAAOgc,oBAI9D9W,EAAOyO,eAGPzO,EAAOyJ,aAAc,EAGrBzJ,EAAO/B,KAAK,UAGdpR,EAAOyD,UAAUgpB,QAAU,SAAkBC,EAAgBC,QACnC,IAAnBD,IAA4BA,GAAiB,QAC7B,IAAhBC,IAAyBA,GAAc,GAE5C,IAAIxZ,EAASlT,KACTgO,EAASkF,EAAOlF,OAChBmF,EAAMD,EAAOC,IACbQ,EAAaT,EAAOS,WACpBS,EAASlB,EAAOkB,OAEpB,YAA6B,IAAlBlB,EAAOlF,QAA0BkF,EAAO6J,YAInD7J,EAAO/B,KAAK,iBAGZ+B,EAAOyJ,aAAc,EAGrBzJ,EAAOiV,eAGHna,EAAOoM,MACTlH,EAAOyL,cAIL+N,IACFxZ,EAAOsX,gBACPrX,EAAIrO,WAAW,SACf6O,EAAW7O,WAAW,SAClBsP,GAAUA,EAAO9R,QACnB8R,EACGnQ,YAAY,CACX+J,EAAOiL,kBACPjL,EAAOmM,iBACPnM,EAAOwM,eACPxM,EAAO0M,gBAAiBpN,KAAK,MAC9BxI,WAAW,SACXA,WAAW,2BACXA,WAAW,sBACXA,WAAW,oBAIlBoO,EAAO/B,KAAK,WAGZnF,OAAOC,KAAKiH,EAAOzC,iBAAiBvE,QAAQ,SAAUwE,GACpDwC,EAAO5L,IAAIoJ,MAGU,IAAnB+b,IACFvZ,EAAOC,IAAI,GAAGD,OAAS,KACvBA,EAAOC,IAAInO,KAAK,SAAU,MAC1BqH,EAAMC,YAAY4G,IAEpBA,EAAO6J,WAAY,GA/CV,MAoDXhd,EAAO4sB,eAAiB,SAAyBC,GAC/CvgB,EAAMqC,OAAO8c,EAAkBoB,IAGjCjc,EAAgB6a,iBAAiBnb,IAAM,WACrC,OAAOmb,GAGT7a,EAAgB2Y,SAASjZ,IAAM,WAC7B,OAAOiZ,GAGT3Y,EAAgBlN,MAAM4M,IAAM,WAC1B,OAAOob,GAGT9a,EAAgBpO,EAAE8N,IAAM,WACtB,OAAO9N,GAGTyJ,OAAO6G,iBAAkB9S,EAAQ4Q,GAE1B5Q,EAjbI,CAkbXwQ,GAEEsc,EAAW,CACbta,KAAM,SACNC,MAAO,CACLyN,OAAQF,GAEVtN,OAAQ,CACNwN,OAAQF,IAIR+M,EAAY,CACdva,KAAM,UACNC,MAAO,CACLua,QAAS3d,GAEXqD,OAAQ,CACNsa,QAAS3d,IAIT4d,EAAY,CACdza,KAAM,UACNC,MAAO,CACLya,QAASjE,GAEXvW,OAAQ,CACNwa,QAASjE,IAITkE,EAAS,CACX3a,KAAM,SACNJ,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBia,OAAQ,CACNC,cAAe,WACRla,IAAUA,EAAO6J,WAAc7J,EAAOyJ,cAC3CzJ,EAAO/B,KAAK,gBACZ+B,EAAO/B,KAAK,YAEdkc,yBAA0B,WACnBna,IAAUA,EAAO6J,WAAc7J,EAAOyJ,aAC3CzJ,EAAO/B,KAAK,0BAKpBvL,GAAI,CACF2jB,KAAM,WAGJjoB,EAAIlB,iBAAiB,SAFRJ,KAEyBmtB,OAAOC,eAG7C9rB,EAAIlB,iBAAiB,oBALRJ,KAKoCmtB,OAAOE,2BAE1Db,QAAS,WAEPlrB,EAAIjB,oBAAoB,SADXL,KAC4BmtB,OAAOC,eAChD9rB,EAAIjB,oBAAoB,oBAFXL,KAEuCmtB,OAAOE,6BAK7DC,EAAW,CACbC,KAAMjsB,EAAIksB,kBAAoBlsB,EAAImsB,uBAClCC,OAAQ,SAAgBpnB,EAAQqnB,QACb,IAAZA,IAAqBA,EAAU,IAEpC,IAAIza,EAASlT,KAGTgQ,EAAW,IADIsd,EAASC,KACI,SAAUK,GAIxC,GAAyB,IAArBA,EAAUtrB,OAAd,CAIA,IAAIurB,EAAiB,WACnB3a,EAAO/B,KAAK,iBAAkByc,EAAU,KAGtCtsB,EAAIwsB,sBACNxsB,EAAIwsB,sBAAsBD,GAE1BvsB,EAAIW,WAAW4rB,EAAgB,QAV/B3a,EAAO/B,KAAK,iBAAkByc,EAAU,MAc5C5d,EAAS+d,QAAQznB,EAAQ,CACvB0nB,gBAA0C,IAAvBL,EAAQK,YAAoCL,EAAQK,WACvEC,eAAwC,IAAtBN,EAAQM,WAAmCN,EAAQM,UACrEC,mBAAgD,IAA1BP,EAAQO,eAAuCP,EAAQO,gBAG/Ehb,EAAOlD,SAASme,UAAUlrB,KAAK+M,IAEjCuZ,KAAM,WACJ,IAAIrW,EAASlT,KACb,GAAKoP,EAAQY,UAAakD,EAAOlF,OAAOgC,SAAxC,CACA,GAAIkD,EAAOlF,OAAOogB,eAEhB,IADA,IAAIC,EAAmBnb,EAAOC,IAAIvM,UACzBvE,EAAI,EAAGA,EAAIgsB,EAAiB/rB,OAAQD,GAAK,EAChD6Q,EAAOlD,SAAS0d,OAAOW,EAAiBhsB,IAI5C6Q,EAAOlD,SAAS0d,OAAOxa,EAAOC,IAAI,GAAI,CAAE8a,WAAW,IAGnD/a,EAAOlD,SAAS0d,OAAOxa,EAAOS,WAAW,GAAI,CAAEqa,YAAY,MAE7DxB,QAAS,WACMxsB,KACNgQ,SAASme,UAAUjiB,QAAQ,SAAU8D,GAC1CA,EAASse,eAFEtuB,KAINgQ,SAASme,UAAY,KAI5BI,EAAa,CACfhc,KAAM,WACNvE,OAAQ,CACNgC,UAAU,EACVoe,gBAAgB,GAElBjc,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBgQ,SAAU,CACRuZ,KAAM+D,EAAS/D,KAAKtX,KAHXjS,MAIT0tB,OAAQJ,EAASI,OAAOzb,KAJfjS,MAKTwsB,QAASc,EAASd,QAAQva,KALjBjS,MAMTmuB,UAAW,OAIjBvoB,GAAI,CACF2jB,KAAM,WACSvpB,KACNgQ,SAASuZ,QAElBiD,QAAS,WACMxsB,KACNgQ,SAASwc,aAKlBgC,EAAU,CACZ1b,OAAQ,SAAgB2b,GACtB,IAAIvb,EAASlT,KACT0uB,EAAMxb,EAAOlF,OACb6H,EAAgB6Y,EAAI7Y,cACpBsB,EAAiBuX,EAAIvX,eACrBF,EAAiByX,EAAIzX,eACrB0X,EAAQzb,EAAOlF,OAAOiG,QACtB2a,EAAkBD,EAAMC,gBACxBC,EAAiBF,EAAME,eACvBC,EAAQ5b,EAAOe,QACf8a,EAAeD,EAAME,KACrBC,EAAaH,EAAMlgB,GACnBwF,EAAS0a,EAAM1a,OACf8a,EAAqBJ,EAAMva,WAC3B4a,EAAcL,EAAMK,YACpBC,EAAiBN,EAAMhmB,OAC3BoK,EAAO2H,oBACP,IAEIwU,EAIAC,EACAC,EAPA7W,EAAcxF,EAAOwF,aAAe,EAGb2W,EAAvBnc,EAAOY,aAA6B,QACpBZ,EAAOI,eAAiB,OAAS,MAIjD2D,GACFqY,EAAc5Z,KAAKC,MAAME,EAAgB,GAAKsB,EAAiByX,EAC/DW,EAAe7Z,KAAKC,MAAME,EAAgB,GAAKsB,EAAiB0X,IAEhES,EAAczZ,GAAiBsB,EAAiB,GAAKyX,EACrDW,EAAepY,EAAiB0X,GAElC,IAAIG,EAAOtZ,KAAKK,KAAK2C,GAAe,GAAK6W,EAAc,GACnD3gB,EAAK8G,KAAKgM,KAAKhJ,GAAe,GAAK4W,EAAalb,EAAO9R,OAAS,GAChEwG,GAAUoK,EAAOqB,WAAWya,IAAS,IAAM9b,EAAOqB,WAAW,IAAM,GASvE,SAASib,IACPtc,EAAOQ,eACPR,EAAOuG,iBACPvG,EAAO8G,sBACH9G,EAAOuc,MAAQvc,EAAOlF,OAAOyhB,KAAKvb,SACpChB,EAAOuc,KAAKC,OAIhB,GAhBArjB,EAAMqC,OAAOwE,EAAOe,QAAS,CAC3B+a,KAAMA,EACNpgB,GAAIA,EACJ9F,OAAQA,EACRyL,WAAYrB,EAAOqB,aAYjBwa,IAAiBC,GAAQC,IAAergB,IAAO6f,EAKjD,OAJIvb,EAAOqB,aAAe2a,GAAsBpmB,IAAWsmB,GACzDlc,EAAOkB,OAAO3K,IAAI4lB,EAAavmB,EAAS,WAE1CoK,EAAOuG,iBAGT,GAAIvG,EAAOlF,OAAOiG,QAAQ0b,eAcxB,OAbAzc,EAAOlF,OAAOiG,QAAQ0b,eAAernB,KAAK4K,EAAQ,CAChDpK,OAAQA,EACRkmB,KAAMA,EACNpgB,GAAIA,EACJwF,OAAS,WAEP,IADA,IAAIwb,EAAiB,GACZvtB,EAAI2sB,EAAM3sB,GAAKuM,EAAIvM,GAAK,EAC/ButB,EAAe3sB,KAAKmR,EAAO/R,IAE7B,OAAOutB,EALD,UAQVJ,IAGF,IAAIK,EAAiB,GACjBC,EAAgB,GACpB,GAAIrB,EACFvb,EAAOS,WAAWhI,KAAM,IAAOuH,EAAOlF,OAAiB,YAAI9J,cAE3D,IAAK,IAAI7B,EAAI0sB,EAAc1sB,GAAK4sB,EAAY5sB,GAAK,GAC3CA,EAAI2sB,GAAYpgB,EAAJvM,IACd6Q,EAAOS,WAAWhI,KAAM,IAAOuH,EAAOlF,OAAiB,WAAI,6BAAgC3L,EAAI,MAAQ6B,SAI7G,IAAK,IAAIoT,EAAM,EAAGA,EAAMlD,EAAO9R,OAAQgV,GAAO,EACjC0X,GAAP1X,GAAeA,GAAO1I,SACE,IAAfqgB,GAA8BR,EACvCqB,EAAc7sB,KAAKqU,IAET2X,EAAN3X,GAAoBwY,EAAc7sB,KAAKqU,GACvCA,EAAMyX,GAAgBc,EAAe5sB,KAAKqU,KAIpDwY,EAAc5jB,QAAQ,SAAU/B,GAC9B+I,EAAOS,WAAWnJ,OAAO2kB,EAAY/a,EAAOjK,GAAQA,MAEtD0lB,EAAejH,KAAK,SAAUxb,EAAGyb,GAAK,OAAOzb,EAAIyb,IAAM3c,QAAQ,SAAU/B,GACvE+I,EAAOS,WAAW9I,QAAQskB,EAAY/a,EAAOjK,GAAQA,MAEvD+I,EAAOS,WAAW5S,SAAS,iBAAiB0I,IAAI4lB,EAAavmB,EAAS,MACtE0mB,KAEFL,YAAa,SAAqBhZ,EAAOhM,GACvC,IAAI+I,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOiG,QAC3B,GAAIjG,EAAO+hB,OAAS7c,EAAOe,QAAQ8b,MAAM5lB,GACvC,OAAO+I,EAAOe,QAAQ8b,MAAM5lB,GAE9B,IAAI6lB,EAAWhiB,EAAOmhB,YAClB5sB,EAAEyL,EAAOmhB,YAAY7mB,KAAK4K,EAAQiD,EAAOhM,IACzC5H,EAAG,eAAmB2Q,EAAOlF,OAAiB,WAAI,8BAAkC7D,EAAQ,KAAQgM,EAAQ,UAGhH,OAFK6Z,EAASzrB,KAAK,4BAA8ByrB,EAASzrB,KAAK,0BAA2B4F,GACtF6D,EAAO+hB,QAAS7c,EAAOe,QAAQ8b,MAAM5lB,GAAS6lB,GAC3CA,GAET5Q,YAAa,SAAqBjJ,GACnBnW,KACNiU,QAAQG,OAAOnR,KAAKkT,GADdnW,KAENiU,QAAQnB,QAAO,IAExBuM,aAAc,SAAsBlJ,GAClC,IAAIjD,EAASlT,KAEb,GADAkT,EAAOe,QAAQG,OAAO3N,QAAQ0P,GAC1BjD,EAAOlF,OAAOiG,QAAQ8b,MAAO,CAC/B,IAAIA,EAAQ7c,EAAOe,QAAQ8b,MACvBE,EAAW,GACfjkB,OAAOC,KAAK8jB,GAAO7jB,QAAQ,SAAUgkB,GACnCD,EAASC,EAAc,GAAKH,EAAMG,KAEpChd,EAAOe,QAAQ8b,MAAQE,EAEzB/c,EAAOe,QAAQnB,QAAO,GACtBI,EAAOiK,UAAU,KAIjBgT,EAAY,CACd5d,KAAM,UACNvE,OAAQ,CACNiG,QAAS,CACPC,SAAS,EACTE,OAAQ,GACR2b,OAAO,EACPZ,YAAa,KACbQ,eAAgB,KAChBf,gBAAiB,EACjBC,eAAgB,IAGpB1c,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBe,QAAS,CACPnB,OAAQ0b,EAAQ1b,OAAOb,KAAKiB,GAC5BkM,YAAaoP,EAAQpP,YAAYnN,KAAKiB,GACtCmM,aAAcmP,EAAQnP,aAAapN,KAAKiB,GACxCic,YAAaX,EAAQW,YAAYld,KAAKiB,GACtCkB,OAAQlB,EAAOlF,OAAOiG,QAAQG,OAC9B2b,MAAO,OAIbnqB,GAAI,CACFwqB,WAAY,WACV,IAAIld,EAASlT,KACb,GAAKkT,EAAOlF,OAAOiG,QAAQC,QAA3B,CACAhB,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,WACjE,IAAIqiB,EAAkB,CACpBnY,qBAAqB,GAEvB7L,EAAMqC,OAAOwE,EAAOlF,OAAQqiB,GAC5BhkB,EAAMqC,OAAOwE,EAAOsV,eAAgB6H,GAEpCnd,EAAOe,QAAQnB,WAEjB4I,aAAc,WACC1b,KACDgO,OAAOiG,QAAQC,SADdlU,KAENiU,QAAQnB,YAKjBwd,EAAW,CACbC,OAAQ,SAAgBnpB,GACtB,IAAI8L,EAASlT,KACT6T,EAAMX,EAAOY,aACbzN,EAAIe,EACJf,EAAE4b,gBAAiB5b,EAAIA,EAAE4b,eAC7B,IAAIuO,EAAKnqB,EAAEoqB,SAAWpqB,EAAEqqB,SAExB,IAAKxd,EAAO0J,iBAAoB1J,EAAOI,gBAAyB,KAAPkd,GAAetd,EAAOK,cAAuB,KAAPid,GAC7F,OAAO,EAET,IAAKtd,EAAO2J,iBAAoB3J,EAAOI,gBAAyB,KAAPkd,GAAetd,EAAOK,cAAuB,KAAPid,GAC7F,OAAO,EAET,KAAInqB,EAAEsqB,UAAYtqB,EAAEuqB,QAAUvqB,EAAEwqB,SAAWxqB,EAAEyqB,SAGzC7wB,EAAIK,eAAiBL,EAAIK,cAAcE,WAA0D,UAA7CP,EAAIK,cAAcE,SAASsgB,eAA0E,aAA7C7gB,EAAIK,cAAcE,SAASsgB,gBAA3I,CAGA,GAAI5N,EAAOlF,OAAO+iB,SAASC,iBAA0B,KAAPR,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAY,CAC/F,IAAIS,GAAS,EAEb,GAAoE,EAAhE/d,EAAOC,IAAIvM,QAAS,IAAOsM,EAAOlF,OAAiB,YAAI1L,QAAsF,IAAxE4Q,EAAOC,IAAIvM,QAAS,IAAOsM,EAAOlF,OAAuB,kBAAI1L,OACpI,OAEF,IAAI4uB,EAAc5vB,EAAIynB,WAClBoI,EAAe7vB,EAAI8vB,YACnBC,EAAene,EAAOC,IAAIrK,SAC1B+K,IAAOwd,EAAa7nB,MAAQ0J,EAAOC,IAAI,GAAG9J,YAM9C,IALA,IAAIioB,EAAc,CAChB,CAACD,EAAa7nB,KAAM6nB,EAAa9nB,KACjC,CAAC8nB,EAAa7nB,KAAO0J,EAAOF,MAAOqe,EAAa9nB,KAChD,CAAC8nB,EAAa7nB,KAAM6nB,EAAa9nB,IAAM2J,EAAOD,QAC9C,CAACoe,EAAa7nB,KAAO0J,EAAOF,MAAOqe,EAAa9nB,IAAM2J,EAAOD,SACtD5Q,EAAI,EAAGA,EAAIivB,EAAYhvB,OAAQD,GAAK,EAAG,CAC9C,IAAIsmB,EAAQ2I,EAAYjvB,GAEV,GAAZsmB,EAAM,IAAWA,EAAM,IAAMuI,GACd,GAAZvI,EAAM,IAAWA,EAAM,IAAMwI,IAEhCF,GAAS,GAGb,IAAKA,EAAU,OAEb/d,EAAOI,gBACE,KAAPkd,GAAoB,KAAPA,IACXnqB,EAAEyd,eAAkBzd,EAAEyd,iBACnBzd,EAAEkrB,aAAc,IAEb,KAAPf,IAAc3c,GAAgB,KAAP2c,GAAa3c,IAAQX,EAAOiK,aAC5C,KAAPqT,IAAc3c,GAAgB,KAAP2c,GAAa3c,IAAQX,EAAOoK,cAE7C,KAAPkT,GAAoB,KAAPA,IACXnqB,EAAEyd,eAAkBzd,EAAEyd,iBACnBzd,EAAEkrB,aAAc,GAEd,KAAPf,GAAatd,EAAOiK,YACb,KAAPqT,GAAatd,EAAOoK,aAE1BpK,EAAO/B,KAAK,WAAYqf,KAG1BgB,OAAQ,WACOxxB,KACF+wB,SAAS7c,UACpB3R,EAAEtC,GAAK2F,GAAG,UAFG5F,KAEe+wB,SAASR,QAFxBvwB,KAGN+wB,SAAS7c,SAAU,IAE5Bud,QAAS,WACMzxB,KACD+wB,SAAS7c,UACrB3R,EAAEtC,GAAKqH,IAAI,UAFEtH,KAEgB+wB,SAASR,QAFzBvwB,KAGN+wB,SAAS7c,SAAU,KAI1Bwd,EAAa,CACfnf,KAAM,WACNvE,OAAQ,CACN+iB,SAAU,CACR7c,SAAS,EACT8c,gBAAgB,IAGpB7e,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnB+wB,SAAU,CACR7c,SAAS,EACTsd,OAAQlB,EAASkB,OAAOvf,KAJfjS,MAKTyxB,QAASnB,EAASmB,QAAQxf,KALjBjS,MAMTuwB,OAAQD,EAASC,OAAOte,KANfjS,UAUf4F,GAAI,CACF2jB,KAAM,WACSvpB,KACFgO,OAAO+iB,SAAS7c,SADdlU,KAEJ+wB,SAASS,UAGpBhF,QAAS,WACMxsB,KACF+wB,SAAS7c,SADPlU,KAEJ+wB,SAASU,aA6BxB,IAAIE,EAAa,CACfC,eAAgBvlB,EAAMM,MACtBvF,OACoD,EAA9C9F,EAAIE,UAAUC,UAAUqB,QAAQ,WAA0B,iBA1BlE,WACE,IAAI4N,EAAY,UACZmhB,EAAcnhB,KAAazQ,EAE/B,IAAK4xB,EAAa,CAChB,IAAIC,EAAU7xB,EAAIa,cAAc,OAChCgxB,EAAQ5wB,aAAawP,EAAW,WAChCmhB,EAA4C,mBAAvBC,EAAQphB,GAc/B,OAXKmhB,GACA5xB,EAAI8xB,gBACJ9xB,EAAI8xB,eAAeC,aAGuB,IAA1C/xB,EAAI8xB,eAAeC,WAAW,GAAI,MAGrCH,EAAc5xB,EAAI8xB,eAAeC,WAAW,eAAgB,QAGvDH,EAMEI,GAAqB,QAAU,aAExC1U,UAAW,SAAmBlX,GAE5B,IAII6rB,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAkDT,MA/CI,WAAYhsB,IACd8rB,EAAK9rB,EAAEuB,QAEL,eAAgBvB,IAClB8rB,GAAM9rB,EAAEisB,WAAa,KAEnB,gBAAiBjsB,IACnB8rB,GAAM9rB,EAAEksB,YAAc,KAEpB,gBAAiBlsB,IACnB6rB,GAAM7rB,EAAEmsB,YAAc,KAIpB,SAAUnsB,GAAKA,EAAEwG,OAASxG,EAAEosB,kBAC9BP,EAAKC,EACLA,EAAK,GAGPC,EA7BiB,GA6BZF,EACLG,EA9BiB,GA8BZF,EAED,WAAY9rB,IACdgsB,EAAKhsB,EAAEqsB,QAEL,WAAYrsB,IACd+rB,EAAK/rB,EAAEssB,SAGJP,GAAMC,IAAOhsB,EAAEusB,YACE,IAAhBvsB,EAAEusB,WACJR,GAxCc,GAyCdC,GAzCc,KA2CdD,GA1Cc,IA2CdC,GA3Cc,MAgDdD,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAEnBC,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAGhB,CACLQ,MAAOX,EACPY,MAAOX,EACPY,OAAQX,EACRY,OAAQX,IAGZY,iBAAkB,WACHjzB,KACNkzB,cAAe,GAExBC,iBAAkB,WACHnzB,KACNkzB,cAAe,GAExB3C,OAAQ,SAAgBnpB,GACtB,IAAIf,EAAIe,EACJ8L,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOolB,WAE3B,IAAKlgB,EAAOggB,eAAiBllB,EAAOqlB,eAAkB,OAAO,EAEzDhtB,EAAE4b,gBAAiB5b,EAAIA,EAAE4b,eAC7B,IAAIqR,EAAQ,EACRC,EAAYrgB,EAAOY,cAAgB,EAAI,EAEvC9O,EAAO2sB,EAAWpU,UAAUlX,GAEhC,GAAI2H,EAAOwlB,YACT,GAAItgB,EAAOI,eAAgB,CACzB,KAAIoC,KAAKwB,IAAIlS,EAAK+tB,QAAUrd,KAAKwB,IAAIlS,EAAKguB,SACnC,OAAO,EADuCM,EAAQtuB,EAAK+tB,OAASQ,MAEtE,CAAA,KAAI7d,KAAKwB,IAAIlS,EAAKguB,QAAUtd,KAAKwB,IAAIlS,EAAK+tB,SAC1C,OAAO,EAD8CO,EAAQtuB,EAAKguB,YAGzEM,EAAQ5d,KAAKwB,IAAIlS,EAAK+tB,QAAUrd,KAAKwB,IAAIlS,EAAKguB,SAAWhuB,EAAK+tB,OAASQ,GAAavuB,EAAKguB,OAG3F,GAAc,IAAVM,EAAe,OAAO,EAI1B,GAFItlB,EAAOylB,SAAUH,GAASA,GAEzBpgB,EAAOlF,OAAOwT,SAaZ,CAEDtO,EAAOlF,OAAOoM,MAChBlH,EAAOkK,UAET,IAAImI,EAAWrS,EAAOtG,eAAkB0mB,EAAQtlB,EAAO0lB,YACnD5Z,EAAe5G,EAAO0G,YACtBG,EAAS7G,EAAO2G,MA2BpB,GAzBI0L,GAAYrS,EAAOmG,iBAAkBkM,EAAWrS,EAAOmG,gBACvDkM,GAAYrS,EAAOyG,iBAAkB4L,EAAWrS,EAAOyG,gBAE3DzG,EAAOuF,cAAc,GACrBvF,EAAOwI,aAAa6J,GACpBrS,EAAOuG,iBACPvG,EAAO2H,oBACP3H,EAAO8G,wBAEDF,GAAgB5G,EAAO0G,cAAkBG,GAAU7G,EAAO2G,QAC9D3G,EAAO8G,sBAGL9G,EAAOlF,OAAOiZ,iBAChB/kB,aAAagR,EAAOkgB,WAAWO,SAC/BzgB,EAAOkgB,WAAWO,QAAUtnB,EAAMI,SAAS,WACzCyG,EAAO4K,kBACN,MAGL5K,EAAO/B,KAAK,SAAU9K,GAGlB6M,EAAOlF,OAAO4lB,UAAY1gB,EAAOlF,OAAO6lB,8BAAgC3gB,EAAO0gB,SAASE,OAExFvO,IAAarS,EAAOmG,gBAAkBkM,IAAarS,EAAOyG,eAAkB,OAAO,MA/C5D,CAC3B,GAAqD,GAAjDtN,EAAMM,MAAQuG,EAAOkgB,WAAWxB,eAClC,GAAI0B,EAAQ,EACV,GAAMpgB,EAAO2G,QAAS3G,EAAOlF,OAAOoM,MAAUlH,EAAOmJ,WAG9C,GAAIrO,EAAOqlB,eAAkB,OAAO,OAFzCngB,EAAOiK,YACPjK,EAAO/B,KAAK,SAAU9K,QAEnB,GAAM6M,EAAO0G,cAAe1G,EAAOlF,OAAOoM,MAAUlH,EAAOmJ,WAG3D,GAAIrO,EAAOqlB,eAAkB,OAAO,OAFzCngB,EAAOoK,YACPpK,EAAO/B,KAAK,SAAU9K,GAG1B6M,EAAOkgB,WAAWxB,gBAAiB,IAAKtwB,EAAIS,MAAQgyB,UAwCtD,OAFI1tB,EAAEyd,eAAkBzd,EAAEyd,iBACnBzd,EAAEkrB,aAAc,GAChB,GAETC,OAAQ,WACN,IAAIte,EAASlT,KACb,IAAK2xB,EAAWvqB,MAAS,OAAO,EAChC,GAAI8L,EAAOkgB,WAAWlf,QAAW,OAAO,EACxC,IAAI5N,EAAS4M,EAAOC,IAQpB,MAP8C,cAA1CD,EAAOlF,OAAOolB,WAAWY,eAC3B1tB,EAAS/D,EAAE2Q,EAAOlF,OAAOolB,WAAWY,eAEtC1tB,EAAOV,GAAG,aAAcsN,EAAOkgB,WAAWH,kBAC1C3sB,EAAOV,GAAG,aAAcsN,EAAOkgB,WAAWD,kBAC1C7sB,EAAOV,GAAG+rB,EAAWvqB,MAAO8L,EAAOkgB,WAAW7C,QAC9Crd,EAAOkgB,WAAWlf,SAAU,GAG9Bud,QAAS,WACP,IAAIve,EAASlT,KACb,IAAK2xB,EAAWvqB,MAAS,OAAO,EAChC,IAAK8L,EAAOkgB,WAAWlf,QAAW,OAAO,EACzC,IAAI5N,EAAS4M,EAAOC,IAMpB,MAL8C,cAA1CD,EAAOlF,OAAOolB,WAAWY,eAC3B1tB,EAAS/D,EAAE2Q,EAAOlF,OAAOolB,WAAWY,eAEtC1tB,EAAOgB,IAAIqqB,EAAWvqB,MAAO8L,EAAOkgB,WAAW7C,UAC/Crd,EAAOkgB,WAAWlf,SAAU,KA2C5B+f,EAAa,CACfnhB,OAAQ,WAEN,IAAII,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOmc,WAE3B,IAAIjX,EAAOlF,OAAOoM,KAAlB,CACA,IAAIsU,EAAMxb,EAAOiX,WACb+J,EAAUxF,EAAIwF,QACdC,EAAUzF,EAAIyF,QAEdA,GAA4B,EAAjBA,EAAQ7xB,SACjB4Q,EAAO0G,YACTua,EAAQxwB,SAASqK,EAAOomB,eAExBD,EAAQlwB,YAAY+J,EAAOomB,eAE7BD,EAAQjhB,EAAOlF,OAAOgK,eAAiB9E,EAAO8L,SAAW,WAAa,eAAehR,EAAOqmB,YAE1FH,GAA4B,EAAjBA,EAAQ5xB,SACjB4Q,EAAO2G,MACTqa,EAAQvwB,SAASqK,EAAOomB,eAExBF,EAAQjwB,YAAY+J,EAAOomB,eAE7BF,EAAQhhB,EAAOlF,OAAOgK,eAAiB9E,EAAO8L,SAAW,WAAa,eAAehR,EAAOqmB,cAGhG9K,KAAM,WACJ,IAII2K,EACAC,EALAjhB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOmc,YACrBnc,EAAOsmB,QAAUtmB,EAAOumB,UAI1BvmB,EAAOsmB,SACTJ,EAAU3xB,EAAEyL,EAAOsmB,QAEjBphB,EAAOlF,OAAOwb,mBACc,iBAAlBxb,EAAOsmB,QACG,EAAjBJ,EAAQ5xB,QACkC,IAA1C4Q,EAAOC,IAAIxH,KAAKqC,EAAOsmB,QAAQhyB,SAElC4xB,EAAUhhB,EAAOC,IAAIxH,KAAKqC,EAAOsmB,UAGjCtmB,EAAOumB,SACTJ,EAAU5xB,EAAEyL,EAAOumB,QAEjBrhB,EAAOlF,OAAOwb,mBACc,iBAAlBxb,EAAOumB,QACG,EAAjBJ,EAAQ7xB,QACkC,IAA1C4Q,EAAOC,IAAIxH,KAAKqC,EAAOumB,QAAQjyB,SAElC6xB,EAAUjhB,EAAOC,IAAIxH,KAAKqC,EAAOumB,UAIjCL,GAA4B,EAAjBA,EAAQ5xB,QACrB4xB,EAAQtuB,GAAG,QAAS,SAAUS,GAC5BA,EAAEyd,iBACE5Q,EAAO2G,QAAU3G,EAAOlF,OAAOoM,MACnClH,EAAOiK,cAGPgX,GAA4B,EAAjBA,EAAQ7xB,QACrB6xB,EAAQvuB,GAAG,QAAS,SAAUS,GAC5BA,EAAEyd,iBACE5Q,EAAO0G,cAAgB1G,EAAOlF,OAAOoM,MACzClH,EAAOoK,cAIXjR,EAAMqC,OAAOwE,EAAOiX,WAAY,CAC9B+J,QAASA,EACTI,OAAQJ,GAAWA,EAAQ,GAC3BC,QAASA,EACTI,OAAQJ,GAAWA,EAAQ,OAG/B3H,QAAS,WACP,IACIkC,EADS1uB,KACImqB,WACb+J,EAAUxF,EAAIwF,QACdC,EAAUzF,EAAIyF,QACdD,GAAWA,EAAQ5xB,SACrB4xB,EAAQ5sB,IAAI,SACZ4sB,EAAQjwB,YANGjE,KAMgBgO,OAAOmc,WAAWiK,gBAE3CD,GAAWA,EAAQ7xB,SACrB6xB,EAAQ7sB,IAAI,SACZ6sB,EAAQlwB,YAVGjE,KAUgBgO,OAAOmc,WAAWiK,kBA+D/CI,EAAa,CACf1hB,OAAQ,WAEN,IAAII,EAASlT,KACT6T,EAAMX,EAAOW,IACb7F,EAASkF,EAAOlF,OAAOymB,WAC3B,GAAKzmB,EAAO9I,IAAOgO,EAAOuhB,WAAWvvB,IAAOgO,EAAOuhB,WAAWthB,KAAwC,IAAjCD,EAAOuhB,WAAWthB,IAAI7Q,OAA3F,CACA,IAGIoyB,EAHArgB,EAAenB,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAAUhB,EAAOe,QAAQG,OAAO9R,OAAS4Q,EAAOkB,OAAO9R,OAC9G6Q,EAAMD,EAAOuhB,WAAWthB,IAGxBwhB,EAAQzhB,EAAOlF,OAAOoM,KAAO1E,KAAKE,MAAMvB,EAAsC,EAAtBnB,EAAOgK,cAAqBhK,EAAOlF,OAAOmJ,gBAAkBjE,EAAOoB,SAAShS,OAcxI,GAbI4Q,EAAOlF,OAAOoM,OAChBsa,EAAUhf,KAAKE,MAAM1C,EAAOwF,YAAcxF,EAAOgK,cAAgBhK,EAAOlF,OAAOmJ,iBACjE9C,EAAe,EAA2B,EAAtBnB,EAAOgK,eACvCwX,GAAYrgB,EAAsC,EAAtBnB,EAAOgK,cAEvByX,EAAQ,EAAlBD,IAAuBA,GAAWC,GAClCD,EAAU,GAAsC,YAAjCxhB,EAAOlF,OAAO4mB,iBAAgCF,EAAUC,EAAQD,IAEnFA,OADqC,IAArBxhB,EAAO6E,UACb7E,EAAO6E,UAEP7E,EAAOwF,aAAe,EAGd,YAAhB1K,EAAOmU,MAAsBjP,EAAOuhB,WAAWI,SAA8C,EAAnC3hB,EAAOuhB,WAAWI,QAAQvyB,OAAY,CAClG,IACIwyB,EACAC,EACAC,EAHAH,EAAU3hB,EAAOuhB,WAAWI,QAoBhC,GAhBI7mB,EAAOinB,iBACT/hB,EAAOuhB,WAAWS,WAAaL,EAAQvqB,GAAG,GAAG4I,EAAOI,eAAiB,aAAe,gBAAe,GACnGH,EAAI1J,IAAIyJ,EAAOI,eAAiB,QAAU,SAAYJ,EAAOuhB,WAAWS,YAAclnB,EAAOmnB,mBAAqB,GAAM,MACxF,EAA5BnnB,EAAOmnB,yBAAmDpuB,IAAzBmM,EAAO6H,gBAC1C7H,EAAOuhB,WAAWW,oBAAuBV,EAAUxhB,EAAO6H,cACtD7H,EAAOuhB,WAAWW,mBAAsBpnB,EAAOmnB,mBAAqB,EACtEjiB,EAAOuhB,WAAWW,mBAAqBpnB,EAAOmnB,mBAAqB,EAC1DjiB,EAAOuhB,WAAWW,mBAAqB,IAChDliB,EAAOuhB,WAAWW,mBAAqB,IAG3CN,EAAaJ,EAAUxhB,EAAOuhB,WAAWW,mBAEzCJ,IADAD,EAAYD,GAAcpf,KAAKgM,IAAImT,EAAQvyB,OAAQ0L,EAAOmnB,oBAAsB,IACxDL,GAAc,GAExCD,EAAQ5wB,YAAc+J,EAAwB,kBAAI,IAAOA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAC9O,EAAbmF,EAAI7Q,OACNuyB,EAAQjrB,KAAK,SAAUO,EAAOkrB,GAC5B,IAAIC,EAAU/yB,EAAE8yB,GACZE,EAAcD,EAAQnrB,QACtBorB,IAAgBb,GAClBY,EAAQ3xB,SAASqK,EAAOwnB,mBAEtBxnB,EAAOinB,iBACUH,GAAfS,GAA6BA,GAAeR,GAC9CO,EAAQ3xB,SAAWqK,EAAwB,kBAAI,SAE7CunB,IAAgBT,GAClBQ,EACGlqB,OACAzH,SAAWqK,EAAwB,kBAAI,SACvC5C,OACAzH,SAAWqK,EAAwB,kBAAI,cAExCunB,IAAgBR,GAClBO,EACGtqB,OACArH,SAAWqK,EAAwB,kBAAI,SACvChD,OACArH,SAAWqK,EAAwB,kBAAI,sBAOhD,GAFc6mB,EAAQvqB,GAAGoqB,GACjB/wB,SAASqK,EAAOwnB,mBACpBxnB,EAAOinB,eAAgB,CAGzB,IAFA,IAAIQ,EAAwBZ,EAAQvqB,GAAGwqB,GACnCY,EAAuBb,EAAQvqB,GAAGyqB,GAC7B1yB,EAAIyyB,EAAYzyB,GAAK0yB,EAAW1yB,GAAK,EAC5CwyB,EAAQvqB,GAAGjI,GAAGsB,SAAWqK,EAAwB,kBAAI,SAEvDynB,EACGrqB,OACAzH,SAAWqK,EAAwB,kBAAI,SACvC5C,OACAzH,SAAWqK,EAAwB,kBAAI,cAC1C0nB,EACG1qB,OACArH,SAAWqK,EAAwB,kBAAI,SACvChD,OACArH,SAAWqK,EAAwB,kBAAI,cAG9C,GAAIA,EAAOinB,eAAgB,CACzB,IAAIU,EAAuBjgB,KAAKgM,IAAImT,EAAQvyB,OAAQ0L,EAAOmnB,mBAAqB,GAC5ES,GAAmB1iB,EAAOuhB,WAAWS,WAAaS,EAAyBziB,EAAOuhB,WAAqB,YAAK,EAAMO,EAAW9hB,EAAOuhB,WAAWS,WAC/I7F,EAAaxb,EAAM,QAAU,OACjCghB,EAAQprB,IAAIyJ,EAAOI,eAAiB+b,EAAa,MAAQuG,EAAgB,OAO7E,GAJoB,aAAhB5nB,EAAOmU,OACThP,EAAIxH,KAAM,IAAOqC,EAAmB,cAAInE,KAAKmE,EAAO6nB,sBAAsBnB,EAAU,IACpFvhB,EAAIxH,KAAM,IAAOqC,EAAiB,YAAInE,KAAKmE,EAAO8nB,oBAAoBnB,KAEpD,gBAAhB3mB,EAAOmU,KAAwB,CACjC,IAAI4T,EAEFA,EADE/nB,EAAOgoB,oBACc9iB,EAAOI,eAAiB,WAAa,aAErCJ,EAAOI,eAAiB,aAAe,WAEhE,IAAI2iB,GAASvB,EAAU,GAAKC,EACxBuB,EAAS,EACTC,EAAS,EACgB,eAAzBJ,EACFG,EAASD,EAETE,EAASF,EAEX9iB,EAAIxH,KAAM,IAAOqC,EAA2B,sBAAI3I,UAAW,6BAA+B6wB,EAAS,YAAcC,EAAS,KAAM3wB,WAAW0N,EAAOlF,OAAOsK,OAEvI,WAAhBtK,EAAOmU,MAAqBnU,EAAOooB,cACrCjjB,EAAIvQ,KAAKoL,EAAOooB,aAAaljB,EAAQwhB,EAAU,EAAGC,IAClDzhB,EAAO/B,KAAK,mBAAoB+B,EAAQC,EAAI,KAE5CD,EAAO/B,KAAK,mBAAoB+B,EAAQC,EAAI,IAE9CA,EAAID,EAAOlF,OAAOgK,eAAiB9E,EAAO8L,SAAW,WAAa,eAAehR,EAAOqmB,aAE1FgC,OAAQ,WAEN,IAAInjB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOymB,WAC3B,GAAKzmB,EAAO9I,IAAOgO,EAAOuhB,WAAWvvB,IAAOgO,EAAOuhB,WAAWthB,KAAwC,IAAjCD,EAAOuhB,WAAWthB,IAAI7Q,OAA3F,CACA,IAAI+R,EAAenB,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAAUhB,EAAOe,QAAQG,OAAO9R,OAAS4Q,EAAOkB,OAAO9R,OAE9G6Q,EAAMD,EAAOuhB,WAAWthB,IACxBmjB,EAAiB,GACrB,GAAoB,YAAhBtoB,EAAOmU,KAAoB,CAE7B,IADA,IAAIoU,EAAkBrjB,EAAOlF,OAAOoM,KAAO1E,KAAKE,MAAMvB,EAAsC,EAAtBnB,EAAOgK,cAAqBhK,EAAOlF,OAAOmJ,gBAAkBjE,EAAOoB,SAAShS,OACzID,EAAI,EAAGA,EAAIk0B,EAAiBl0B,GAAK,EACpC2L,EAAOwoB,aACTF,GAAkBtoB,EAAOwoB,aAAaluB,KAAK4K,EAAQ7Q,EAAG2L,EAAOyoB,aAE7DH,GAAkB,IAAOtoB,EAAoB,cAAI,WAAeA,EAAkB,YAAI,OAAWA,EAAoB,cAAI,IAG7HmF,EAAIvQ,KAAK0zB,GACTpjB,EAAOuhB,WAAWI,QAAU1hB,EAAIxH,KAAM,IAAOqC,EAAkB,aAE7C,aAAhBA,EAAOmU,OAEPmU,EADEtoB,EAAO0oB,eACQ1oB,EAAO0oB,eAAepuB,KAAK4K,EAAQlF,EAAO2oB,aAAc3oB,EAAO4oB,YAE/D,gBAAoB5oB,EAAmB,aAAI,4BAEtCA,EAAiB,WAAI,YAE7CmF,EAAIvQ,KAAK0zB,IAES,gBAAhBtoB,EAAOmU,OAEPmU,EADEtoB,EAAO6oB,kBACQ7oB,EAAO6oB,kBAAkBvuB,KAAK4K,EAAQlF,EAAO8oB,sBAE7C,gBAAoB9oB,EAA2B,qBAAI,YAEtEmF,EAAIvQ,KAAK0zB,IAES,WAAhBtoB,EAAOmU,MACTjP,EAAO/B,KAAK,mBAAoB+B,EAAOuhB,WAAWthB,IAAI,MAG1DoW,KAAM,WACJ,IAAIrW,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOymB,WAC3B,GAAKzmB,EAAO9I,GAAZ,CAEA,IAAIiO,EAAM5Q,EAAEyL,EAAO9I,IACA,IAAfiO,EAAI7Q,SAGN4Q,EAAOlF,OAAOwb,mBACU,iBAAdxb,EAAO9I,IACD,EAAbiO,EAAI7Q,QACkC,IAAtC4Q,EAAOC,IAAIxH,KAAKqC,EAAO9I,IAAI5C,SAE9B6Q,EAAMD,EAAOC,IAAIxH,KAAKqC,EAAO9I,KAGX,YAAhB8I,EAAOmU,MAAsBnU,EAAO+oB,WACtC5jB,EAAIxP,SAASqK,EAAOgpB,gBAGtB7jB,EAAIxP,SAASqK,EAAOipB,cAAgBjpB,EAAOmU,MAEvB,YAAhBnU,EAAOmU,MAAsBnU,EAAOinB,iBACtC9hB,EAAIxP,SAAU,GAAMqK,EAAoB,cAAKA,EAAW,KAAI,YAC5DkF,EAAOuhB,WAAWW,mBAAqB,EACnCpnB,EAAOmnB,mBAAqB,IAC9BnnB,EAAOmnB,mBAAqB,IAGZ,gBAAhBnnB,EAAOmU,MAA0BnU,EAAOgoB,qBAC1C7iB,EAAIxP,SAASqK,EAAOkpB,0BAGlBlpB,EAAO+oB,WACT5jB,EAAIvN,GAAG,QAAU,IAAOoI,EAAkB,YAAI,SAAiB3H,GAC7DA,EAAEyd,iBACF,IAAI3Z,EAAQ5H,EAAEvC,MAAMmK,QAAU+I,EAAOlF,OAAOmJ,eACxCjE,EAAOlF,OAAOoM,OAAQjQ,GAAS+I,EAAOgK,cAC1ChK,EAAOoJ,QAAQnS,KAInBkC,EAAMqC,OAAOwE,EAAOuhB,WAAY,CAC9BthB,IAAKA,EACLjO,GAAIiO,EAAI,QAGZqZ,QAAS,WACP,IAAItZ,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOymB,WAC3B,GAAKzmB,EAAO9I,IAAOgO,EAAOuhB,WAAWvvB,IAAOgO,EAAOuhB,WAAWthB,KAAwC,IAAjCD,EAAOuhB,WAAWthB,IAAI7Q,OAA3F,CACA,IAAI6Q,EAAMD,EAAOuhB,WAAWthB,IAE5BA,EAAIlP,YAAY+J,EAAOmpB,aACvBhkB,EAAIlP,YAAY+J,EAAOipB,cAAgBjpB,EAAOmU,MAC1CjP,EAAOuhB,WAAWI,SAAW3hB,EAAOuhB,WAAWI,QAAQ5wB,YAAY+J,EAAOwnB,mBAC1ExnB,EAAO+oB,WACT5jB,EAAI7L,IAAI,QAAU,IAAO0G,EAAkB,gBAoG7CopB,EAAY,CACd1b,aAAc,WACZ,IAAIxI,EAASlT,KACb,GAAKkT,EAAOlF,OAAOqpB,UAAUnyB,IAAOgO,EAAOmkB,UAAUnyB,GAArD,CACA,IAAImyB,EAAYnkB,EAAOmkB,UACnBxjB,EAAMX,EAAOY,aACb0F,EAAWtG,EAAOsG,SAClB8d,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UACtBC,EAAUH,EAAUG,QACpBrkB,EAAMkkB,EAAUlkB,IAChBnF,EAASkF,EAAOlF,OAAOqpB,UAEvBI,EAAUH,EACVI,GAAUH,EAAYD,GAAY9d,EAClC3F,EAEW,GADb6jB,GAAUA,IAERD,EAAUH,EAAWI,EACrBA,EAAS,GACqBH,GAApBG,EAASJ,IACnBG,EAAUF,EAAYG,GAEfA,EAAS,GAClBD,EAAUH,EAAWI,EACrBA,EAAS,GACoBH,EAApBG,EAASJ,IAClBG,EAAUF,EAAYG,GAEpBxkB,EAAOI,gBACLlE,EAAQS,aACV2nB,EAAQnyB,UAAW,eAAiBqyB,EAAS,aAE7CF,EAAQnyB,UAAW,cAAgBqyB,EAAS,OAE9CF,EAAQ,GAAGv2B,MAAM+R,MAAQykB,EAAU,OAE/BroB,EAAQS,aACV2nB,EAAQnyB,UAAW,oBAAsBqyB,EAAS,UAElDF,EAAQnyB,UAAW,cAAgBqyB,EAAS,OAE9CF,EAAQ,GAAGv2B,MAAMgS,OAASwkB,EAAU,MAElCzpB,EAAO2pB,OACTz1B,aAAagR,EAAOmkB,UAAU1D,SAC9BxgB,EAAI,GAAGlS,MAAM22B,QAAU,EACvB1kB,EAAOmkB,UAAU1D,QAAU1xB,WAAW,WACpCkR,EAAI,GAAGlS,MAAM22B,QAAU,EACvBzkB,EAAI3N,WAAW,MACd,QAGPiT,cAAe,SAAuBhT,GACvBzF,KACDgO,OAAOqpB,UAAUnyB,IADhBlF,KAC8Bq3B,UAAUnyB,IADxClF,KAENq3B,UAAUG,QAAQhyB,WAAWC,IAEtCsN,WAAY,WACV,IAAIG,EAASlT,KACb,GAAKkT,EAAOlF,OAAOqpB,UAAUnyB,IAAOgO,EAAOmkB,UAAUnyB,GAArD,CAEA,IAAImyB,EAAYnkB,EAAOmkB,UACnBG,EAAUH,EAAUG,QACpBrkB,EAAMkkB,EAAUlkB,IAEpBqkB,EAAQ,GAAGv2B,MAAM+R,MAAQ,GACzBwkB,EAAQ,GAAGv2B,MAAMgS,OAAS,GAC1B,IAIIqkB,EAJAC,EAAYrkB,EAAOI,eAAiBH,EAAI,GAAGzK,YAAcyK,EAAI,GAAGtK,aAEhEgvB,EAAU3kB,EAAOO,KAAOP,EAAOkC,YAC/B0iB,EAAcD,GAAWN,EAAYrkB,EAAOO,MAG9C6jB,EADuC,SAArCpkB,EAAOlF,OAAOqpB,UAAUC,SACfC,EAAYM,EAEZrkB,SAASN,EAAOlF,OAAOqpB,UAAUC,SAAU,IAGpDpkB,EAAOI,eACTkkB,EAAQ,GAAGv2B,MAAM+R,MAAQskB,EAAW,KAEpCE,EAAQ,GAAGv2B,MAAMgS,OAASqkB,EAAW,KAIrCnkB,EAAI,GAAGlS,MAAM82B,QADA,GAAXF,EACqB,OAEA,GAErB3kB,EAAOlF,OAAOgqB,gBAChB7kB,EAAI,GAAGlS,MAAM22B,QAAU,GAEzBvrB,EAAMqC,OAAO2oB,EAAW,CACtBE,UAAWA,EACXM,QAASA,EACTC,YAAaA,EACbR,SAAUA,IAEZD,EAAUlkB,IAAID,EAAOlF,OAAOgK,eAAiB9E,EAAO8L,SAAW,WAAa,eAAe9L,EAAOlF,OAAOqpB,UAAUhD,aAErH4D,gBAAiB,SAAyB5xB,GACxC,IAaI6xB,EAbAhlB,EAASlT,KACTq3B,EAAYnkB,EAAOmkB,UACnBxjB,EAAMX,EAAOY,aACbX,EAAMkkB,EAAUlkB,IAChBmkB,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UAS1BW,IANIhlB,EAAOI,eACsB,eAAXjN,EAAE8b,MAAoC,cAAX9b,EAAE8b,KAAwB9b,EAAEwc,cAAc,GAAGC,MAAQzc,EAAEyc,OAASzc,EAAE8xB,QAElF,eAAX9xB,EAAE8b,MAAoC,cAAX9b,EAAE8b,KAAwB9b,EAAEwc,cAAc,GAAGG,MAAQ3c,EAAE2c,OAAS3c,EAAE+xB,SAG9EjlB,EAAIrK,SAASoK,EAAOI,eAAiB,OAAS,OAAUgkB,EAAW,IAAOC,EAAYD,GAC3HY,EAAgBxiB,KAAKK,IAAIL,KAAKgM,IAAIwW,EAAe,GAAI,GACjDrkB,IACFqkB,EAAgB,EAAIA,GAGtB,IAAI3S,EAAWrS,EAAOmG,gBAAmBnG,EAAOyG,eAAiBzG,EAAOmG,gBAAkB6e,EAE1FhlB,EAAOuG,eAAe8L,GACtBrS,EAAOwI,aAAa6J,GACpBrS,EAAO2H,oBACP3H,EAAO8G,uBAETqe,YAAa,SAAqBhyB,GAChC,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOqpB,UACvBA,EAAYnkB,EAAOmkB,UACnB1jB,EAAaT,EAAOS,WACpBR,EAAMkkB,EAAUlkB,IAChBqkB,EAAUH,EAAUG,QACxBtkB,EAAOmkB,UAAU/U,WAAY,EAC7Bjc,EAAEyd,iBACFzd,EAAEye,kBAEFnR,EAAWnO,WAAW,KACtBgyB,EAAQhyB,WAAW,KACnB6xB,EAAUY,gBAAgB5xB,GAE1BnE,aAAagR,EAAOmkB,UAAUiB,aAE9BnlB,EAAI3N,WAAW,GACXwI,EAAO2pB,MACTxkB,EAAI1J,IAAI,UAAW,GAErByJ,EAAO/B,KAAK,qBAAsB9K,IAEpCkyB,WAAY,SAAoBlyB,GAC9B,IACIgxB,EADSr3B,KACUq3B,UACnB1jB,EAFS3T,KAEW2T,WACpBR,EAAMkkB,EAAUlkB,IAChBqkB,EAAUH,EAAUG,QAJXx3B,KAMDq3B,UAAU/U,YAClBjc,EAAEyd,eAAkBzd,EAAEyd,iBACnBzd,EAAEkrB,aAAc,EACvB8F,EAAUY,gBAAgB5xB,GAC1BsN,EAAWnO,WAAW,GACtB2N,EAAI3N,WAAW,GACfgyB,EAAQhyB,WAAW,GAZNxF,KAaNmR,KAAK,oBAAqB9K,KAEnCmyB,UAAW,SAAmBnyB,GAC5B,IAAI6M,EAASlT,KAETgO,EAASkF,EAAOlF,OAAOqpB,UAEvBlkB,EADYD,EAAOmkB,UACHlkB,IAEfD,EAAOmkB,UAAU/U,YACtBpP,EAAOmkB,UAAU/U,WAAY,EACzBtU,EAAO2pB,OACTz1B,aAAagR,EAAOmkB,UAAUiB,aAC9BplB,EAAOmkB,UAAUiB,YAAcjsB,EAAMI,SAAS,WAC5C0G,EAAI1J,IAAI,UAAW,GACnB0J,EAAI3N,WAAW,MACd,MAEL0N,EAAO/B,KAAK,mBAAoB9K,GAC5B2H,EAAOyqB,eACTvlB,EAAO4K,mBAGX4a,gBAAiB,WACf,IAAIxlB,EAASlT,KACb,GAAKkT,EAAOlF,OAAOqpB,UAAUnyB,GAA7B,CACA,IAAImyB,EAAYnkB,EAAOmkB,UACnBzV,EAAc1O,EAAO0O,YACrBuK,EAAqBjZ,EAAOiZ,mBAC5Bne,EAASkF,EAAOlF,OAEhB1H,EADM+wB,EAAUlkB,IACH,GACbwlB,KAAiBvpB,EAAQa,kBAAmBjC,EAAO+Z,mBAAmB,CAAEC,SAAS,EAAO7hB,SAAS,GACjG8J,KAAkBb,EAAQa,kBAAmBjC,EAAO+Z,mBAAmB,CAAEC,SAAS,EAAM7hB,SAAS,GAChGiJ,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,uBAKlDP,EAAQC,QACV/I,EAAOlG,iBAAiBwhB,EAAYkG,MAAO5U,EAAOmkB,UAAUgB,YAAaM,GACzEryB,EAAOlG,iBAAiBwhB,EAAYqG,KAAM/U,EAAOmkB,UAAUkB,WAAYI,GACvEryB,EAAOlG,iBAAiBwhB,EAAYsG,IAAKhV,EAAOmkB,UAAUmB,UAAWvoB,KAElEjC,EAAO+Q,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAanS,EAAO+Q,gBAAkB3P,EAAQC,OAAS0Q,EAAOG,OAChH5Z,EAAOlG,iBAAiB,YAAa8S,EAAOmkB,UAAUgB,YAAaM,GACnE14B,EAAIG,iBAAiB,YAAa8S,EAAOmkB,UAAUkB,WAAYI,GAC/D14B,EAAIG,iBAAiB,UAAW8S,EAAOmkB,UAAUmB,UAAWvoB,MAZ9D3J,EAAOlG,iBAAiB+rB,EAAmBrE,MAAO5U,EAAOmkB,UAAUgB,YAAaM,GAChF14B,EAAIG,iBAAiB+rB,EAAmBlE,KAAM/U,EAAOmkB,UAAUkB,WAAYI,GAC3E14B,EAAIG,iBAAiB+rB,EAAmBjE,IAAKhV,EAAOmkB,UAAUmB,UAAWvoB,MAc7E2oB,iBAAkB,WAChB,IAAI1lB,EAASlT,KACb,GAAKkT,EAAOlF,OAAOqpB,UAAUnyB,GAA7B,CACA,IAAImyB,EAAYnkB,EAAOmkB,UACnBzV,EAAc1O,EAAO0O,YACrBuK,EAAqBjZ,EAAOiZ,mBAC5Bne,EAASkF,EAAOlF,OAEhB1H,EADM+wB,EAAUlkB,IACH,GACbwlB,KAAiBvpB,EAAQa,kBAAmBjC,EAAO+Z,mBAAmB,CAAEC,SAAS,EAAO7hB,SAAS,GACjG8J,KAAkBb,EAAQa,kBAAmBjC,EAAO+Z,mBAAmB,CAAEC,SAAS,EAAM7hB,SAAS,GAChGiJ,EAAQC,QAAUD,EAAQI,gBAAiBJ,EAAQO,uBAKlDP,EAAQC,QACV/I,EAAOjG,oBAAoBuhB,EAAYkG,MAAO5U,EAAOmkB,UAAUgB,YAAaM,GAC5EryB,EAAOjG,oBAAoBuhB,EAAYqG,KAAM/U,EAAOmkB,UAAUkB,WAAYI,GAC1EryB,EAAOjG,oBAAoBuhB,EAAYsG,IAAKhV,EAAOmkB,UAAUmB,UAAWvoB,KAErEjC,EAAO+Q,gBAAkBgB,EAAOG,MAAQH,EAAOI,SAAanS,EAAO+Q,gBAAkB3P,EAAQC,OAAS0Q,EAAOG,OAChH5Z,EAAOjG,oBAAoB,YAAa6S,EAAOmkB,UAAUgB,YAAaM,GACtE14B,EAAII,oBAAoB,YAAa6S,EAAOmkB,UAAUkB,WAAYI,GAClE14B,EAAII,oBAAoB,UAAW6S,EAAOmkB,UAAUmB,UAAWvoB,MAZjE3J,EAAOjG,oBAAoB8rB,EAAmBrE,MAAO5U,EAAOmkB,UAAUgB,YAAaM,GACnF14B,EAAII,oBAAoB8rB,EAAmBlE,KAAM/U,EAAOmkB,UAAUkB,WAAYI,GAC9E14B,EAAII,oBAAoB8rB,EAAmBjE,IAAKhV,EAAOmkB,UAAUmB,UAAWvoB,MAchFsZ,KAAM,WACJ,IAAIrW,EAASlT,KACb,GAAKkT,EAAOlF,OAAOqpB,UAAUnyB,GAA7B,CACA,IAAImyB,EAAYnkB,EAAOmkB,UACnBwB,EAAY3lB,EAAOC,IACnBnF,EAASkF,EAAOlF,OAAOqpB,UAEvBlkB,EAAM5Q,EAAEyL,EAAO9I,IACfgO,EAAOlF,OAAOwb,mBAA0C,iBAAdxb,EAAO9I,IAAgC,EAAbiO,EAAI7Q,QAAmD,IAArCu2B,EAAUltB,KAAKqC,EAAO9I,IAAI5C,SAClH6Q,EAAM0lB,EAAUltB,KAAKqC,EAAO9I,KAG9B,IAAIsyB,EAAUrkB,EAAIxH,KAAM,IAAOuH,EAAOlF,OAAOqpB,UAAmB,WACzC,IAAnBG,EAAQl1B,SACVk1B,EAAUj1B,EAAG,eAAmB2Q,EAAOlF,OAAOqpB,UAAmB,UAAI,YACrElkB,EAAI3I,OAAOgtB,IAGbnrB,EAAMqC,OAAO2oB,EAAW,CACtBlkB,IAAKA,EACLjO,GAAIiO,EAAI,GACRqkB,QAASA,EACTsB,OAAQtB,EAAQ,KAGdxpB,EAAO+qB,WACT1B,EAAUqB,oBAGdlM,QAAS,WACMxsB,KACNq3B,UAAUuB,qBAwEjBI,EAAW,CACbC,aAAc,SAAsB/zB,EAAIsU,GACtC,IACI3F,EADS7T,KACI6T,IAEbV,EAAM5Q,EAAE2C,GACRquB,EAAY1f,GAAO,EAAI,EAEvBqlB,EAAI/lB,EAAI5O,KAAK,yBAA2B,IACxCqX,EAAIzI,EAAI5O,KAAK,0BACbsX,EAAI1I,EAAI5O,KAAK,0BACb0xB,EAAQ9iB,EAAI5O,KAAK,8BACjBqzB,EAAUzkB,EAAI5O,KAAK,gCAwBvB,GAtBIqX,GAAKC,GACPD,EAAIA,GAAK,IACTC,EAAIA,GAAK,KAdE7b,KAeKsT,gBAChBsI,EAAIsd,EACJrd,EAAI,MAEJA,EAAIqd,EACJtd,EAAI,KAIJA,EADsB,GAApB,EAAI9Y,QAAQ,KACT0Q,SAASoI,EAAG,IAAMpC,EAAW+Z,EAAa,IAE1C3X,EAAIpC,EAAW+Z,EAAa,KAGjC1X,EADsB,GAApB,EAAI/Y,QAAQ,KACT0Q,SAASqI,EAAG,IAAMrC,EAAY,IAE9BqC,EAAIrC,EAAY,KAGnB,MAAOoe,EAA6C,CACtD,IAAIuB,EAAiBvB,GAAYA,EAAU,IAAM,EAAIliB,KAAKwB,IAAIsC,IAC9DrG,EAAI,GAAGlS,MAAM22B,QAAUuB,EAEzB,GAAI,MAAOlD,EACT9iB,EAAI9N,UAAW,eAAiBuW,EAAI,KAAOC,EAAI,cAC1C,CACL,IAAIud,EAAenD,GAAUA,EAAQ,IAAM,EAAIvgB,KAAKwB,IAAIsC,IACxDrG,EAAI9N,UAAW,eAAiBuW,EAAI,KAAOC,EAAI,gBAAkBud,EAAe,OAGpF1d,aAAc,WACZ,IAAIxI,EAASlT,KACTmT,EAAMD,EAAOC,IACbiB,EAASlB,EAAOkB,OAChBoF,EAAWtG,EAAOsG,SAClBlF,EAAWpB,EAAOoB,SACtBnB,EAAIpS,SAAS,8EACV6I,KAAK,SAAUO,EAAOjF,GACrBgO,EAAOmmB,SAASJ,aAAa/zB,EAAIsU,KAErCpF,EAAOxK,KAAK,SAAU4S,EAAY8c,GAChC,IAAIlgB,EAAgBkgB,EAAQ9f,SACO,EAA/BtG,EAAOlF,OAAOmJ,gBAAsD,SAAhCjE,EAAOlF,OAAO6H,gBACpDuD,GAAiB1D,KAAKE,KAAK4G,EAAa,GAAMhD,GAAYlF,EAAShS,OAAS,IAE9E8W,EAAgB1D,KAAKgM,IAAIhM,KAAKK,IAAIqD,GAAgB,GAAI,GACtD7W,EAAE+2B,GAAS3tB,KAAK,8EACb/B,KAAK,SAAUO,EAAOjF,GACrBgO,EAAOmmB,SAASJ,aAAa/zB,EAAIkU,QAIzCX,cAAe,SAAuBhT,QAClB,IAAbA,IAAsBA,EAAWzF,KAAKgO,OAAOsK,OAErCtY,KACImT,IACbxH,KAAK,8EACN/B,KAAK,SAAUO,EAAOovB,GACrB,IAAIC,EAAcj3B,EAAEg3B,GAChBE,EAAmBjmB,SAASgmB,EAAYj1B,KAAK,iCAAkC,KAAOkB,EACzE,IAAbA,IAAkBg0B,EAAmB,GACzCD,EAAYh0B,WAAWi0B,OA+C3BC,EAAO,CAETC,0BAA2B,SAAmCtzB,GAC5D,GAAIA,EAAEwc,cAAcvgB,OAAS,EAAK,OAAO,EACzC,IAAIs3B,EAAKvzB,EAAEwc,cAAc,GAAGC,MACxB+W,EAAKxzB,EAAEwc,cAAc,GAAGG,MACxB8W,EAAKzzB,EAAEwc,cAAc,GAAGC,MACxBiX,EAAK1zB,EAAEwc,cAAc,GAAGG,MAE5B,OADetN,KAAK8O,KAAM9O,KAAK+O,IAAMqV,EAAKF,EAAK,GAAQlkB,KAAK+O,IAAMsV,EAAKF,EAAK,KAI9EG,eAAgB,SAAwB3zB,GACtC,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOisB,KACvBA,EAAO/mB,EAAO+mB,KACdC,EAAUD,EAAKC,QAGnB,GAFAD,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,GACnBhrB,EAAQkB,SAAU,CACrB,GAAe,eAAXjK,EAAE8b,MAAqC,eAAX9b,EAAE8b,MAAyB9b,EAAEwc,cAAcvgB,OAAS,EAClF,OAEF23B,EAAKE,oBAAqB,EAC1BD,EAAQG,WAAaX,EAAKC,0BAA0BtzB,GAEjD6zB,EAAQlK,UAAakK,EAAQlK,SAAS1tB,SACzC43B,EAAQlK,SAAWztB,EAAE8D,EAAEC,QAAQoF,QAAQ,iBACP,IAA5BwuB,EAAQlK,SAAS1tB,SAAgB43B,EAAQlK,SAAW9c,EAAOkB,OAAO9J,GAAG4I,EAAOwF,cAChFwhB,EAAQI,SAAWJ,EAAQlK,SAASrkB,KAAK,oBACzCuuB,EAAQK,aAAeL,EAAQI,SAAS9uB,OAAQ,IAAOwC,EAAqB,gBAC5EksB,EAAQM,SAAWN,EAAQK,aAAah2B,KAAK,qBAAuByJ,EAAOwsB,SACvC,IAAhCN,EAAQK,aAAaj4B,SAK3B43B,EAAQI,SAAS90B,WAAW,GAC5B0N,EAAO+mB,KAAKQ,WAAY,GALpBP,EAAQI,cAAWvzB,GAOzB2zB,gBAAiB,SAAyBr0B,GACxC,IACI2H,EADShO,KACOgO,OAAOisB,KACvBA,EAFSj6B,KAEKi6B,KACdC,EAAUD,EAAKC,QACnB,IAAK9qB,EAAQkB,SAAU,CACrB,GAAe,cAAXjK,EAAE8b,MAAoC,cAAX9b,EAAE8b,MAAwB9b,EAAEwc,cAAcvgB,OAAS,EAChF,OAEF23B,EAAKG,kBAAmB,EACxBF,EAAQS,UAAYjB,EAAKC,0BAA0BtzB,GAEhD6zB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASh4B,SACtC8M,EAAQkB,SAZCtQ,KAaJi6B,KAAKhE,MAAQ5vB,EAAE4vB,MAAQgE,EAAKb,aAEnCa,EAAKhE,MAASiE,EAAQS,UAAYT,EAAQG,WAAcJ,EAAKb,aAE3Da,EAAKhE,MAAQiE,EAAQM,WACvBP,EAAKhE,MAASiE,EAAQM,SAAW,EAAM9kB,KAAK+O,IAAOwV,EAAKhE,MAAQiE,EAAQM,SAAY,EAAI,KAEtFP,EAAKhE,MAAQjoB,EAAO4sB,WACtBX,EAAKhE,MAASjoB,EAAO4sB,SAAW,EAAMllB,KAAK+O,IAAOzW,EAAO4sB,SAAWX,EAAKhE,MAAS,EAAI,KAExFiE,EAAQI,SAASj1B,UAAW,4BAA+B40B,EAAU,MAAI,OAE3EY,aAAc,SAAsBx0B,GAClC,IACI2H,EADShO,KACOgO,OAAOisB,KACvBA,EAFSj6B,KAEKi6B,KACdC,EAAUD,EAAKC,QACnB,IAAK9qB,EAAQkB,SAAU,CACrB,IAAK2pB,EAAKE,qBAAuBF,EAAKG,iBACpC,OAEF,GAAe,aAAX/zB,EAAE8b,MAAmC,aAAX9b,EAAE8b,MAAuB9b,EAAEy0B,eAAex4B,OAAS,IAAMyd,EAAOI,QAC5F,OAEF8Z,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,EAErBF,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASh4B,SAC1C23B,EAAKhE,MAAQvgB,KAAKK,IAAIL,KAAKgM,IAAIuY,EAAKhE,MAAOiE,EAAQM,UAAWxsB,EAAO4sB,UACrEV,EAAQI,SAAS90B,WAhBJxF,KAgBsBgO,OAAOsK,OAAOjT,UAAW,4BAA+B40B,EAAU,MAAI,KACzGA,EAAKb,aAAea,EAAKhE,MACzBgE,EAAKQ,WAAY,EACE,IAAfR,EAAKhE,QAAeiE,EAAQlK,cAAWjpB,KAE7C+a,aAAc,SAAsBzb,GAClC,IACI4zB,EADSj6B,KACKi6B,KACdC,EAAUD,EAAKC,QACflP,EAAQiP,EAAKjP,MACZkP,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASh4B,SACtC0oB,EAAM1I,YACNvC,EAAOI,SAAW9Z,EAAEyd,iBACxBkH,EAAM1I,WAAY,EAClB0I,EAAM+P,aAAanf,EAAe,eAAXvV,EAAE8b,KAAwB9b,EAAEwc,cAAc,GAAGC,MAAQzc,EAAEyc,MAC9EkI,EAAM+P,aAAalf,EAAe,eAAXxV,EAAE8b,KAAwB9b,EAAEwc,cAAc,GAAGG,MAAQ3c,EAAE2c,SAEhFkB,YAAa,SAAqB7d,GAChC,IAAI6M,EAASlT,KACTi6B,EAAO/mB,EAAO+mB,KACdC,EAAUD,EAAKC,QACflP,EAAQiP,EAAKjP,MACb5E,EAAW6T,EAAK7T,SACpB,GAAK8T,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASh4B,SAC1C4Q,EAAOwP,YAAa,EACfsI,EAAM1I,WAAc4X,EAAQlK,UAAjC,CAEKhF,EAAMzI,UACTyI,EAAMhY,MAAQknB,EAAQI,SAAS,GAAG5xB,YAClCsiB,EAAM/X,OAASinB,EAAQI,SAAS,GAAGzxB,aACnCmiB,EAAM/H,OAAS5W,EAAMO,aAAastB,EAAQK,aAAa,GAAI,MAAQ,EACnEvP,EAAM9H,OAAS7W,EAAMO,aAAastB,EAAQK,aAAa,GAAI,MAAQ,EACnEL,EAAQc,WAAad,EAAQlK,SAAS,GAAGtnB,YACzCwxB,EAAQe,YAAcf,EAAQlK,SAAS,GAAGnnB,aAC1CqxB,EAAQK,aAAa/0B,WAAW,GAC5B0N,EAAOW,MACTmX,EAAM/H,QAAU+H,EAAM/H,OACtB+H,EAAM9H,QAAU8H,EAAM9H,SAI1B,IAAIgY,EAAclQ,EAAMhY,MAAQinB,EAAKhE,MACjCkF,EAAenQ,EAAM/X,OAASgnB,EAAKhE,MAEvC,KAAIiF,EAAchB,EAAQc,YAAcG,EAAejB,EAAQe,aAA/D,CAUA,GARAjQ,EAAMoQ,KAAO1lB,KAAKgM,IAAMwY,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtElQ,EAAMqQ,MAAQrQ,EAAMoQ,KACpBpQ,EAAMsQ,KAAO5lB,KAAKgM,IAAMwY,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxEnQ,EAAMuQ,MAAQvQ,EAAMsQ,KAEpBtQ,EAAMwQ,eAAe5f,EAAe,cAAXvV,EAAE8b,KAAuB9b,EAAEwc,cAAc,GAAGC,MAAQzc,EAAEyc,MAC/EkI,EAAMwQ,eAAe3f,EAAe,cAAXxV,EAAE8b,KAAuB9b,EAAEwc,cAAc,GAAGG,MAAQ3c,EAAE2c,OAE1EgI,EAAMzI,UAAY0X,EAAKQ,UAAW,CACrC,GACEvnB,EAAOI,iBAEJoC,KAAKC,MAAMqV,EAAMoQ,QAAU1lB,KAAKC,MAAMqV,EAAM/H,SAAW+H,EAAMwQ,eAAe5f,EAAIoP,EAAM+P,aAAanf,GAChGlG,KAAKC,MAAMqV,EAAMqQ,QAAU3lB,KAAKC,MAAMqV,EAAM/H,SAAW+H,EAAMwQ,eAAe5f,EAAIoP,EAAM+P,aAAanf,GAIzG,YADAoP,EAAM1I,WAAY,GAElB,IACCpP,EAAOI,iBAELoC,KAAKC,MAAMqV,EAAMsQ,QAAU5lB,KAAKC,MAAMqV,EAAM9H,SAAW8H,EAAMwQ,eAAe3f,EAAImP,EAAM+P,aAAalf,GAChGnG,KAAKC,MAAMqV,EAAMuQ,QAAU7lB,KAAKC,MAAMqV,EAAM9H,SAAW8H,EAAMwQ,eAAe3f,EAAImP,EAAM+P,aAAalf,GAIzG,YADAmP,EAAM1I,WAAY,GAItBjc,EAAEyd,iBACFzd,EAAEye,kBAEFkG,EAAMzI,SAAU,EAChByI,EAAMpI,SAAYoI,EAAMwQ,eAAe5f,EAAIoP,EAAM+P,aAAanf,EAAKoP,EAAM/H,OACzE+H,EAAMjI,SAAYiI,EAAMwQ,eAAe3f,EAAImP,EAAM+P,aAAalf,EAAKmP,EAAM9H,OAErE8H,EAAMpI,SAAWoI,EAAMoQ,OACzBpQ,EAAMpI,SAAYoI,EAAMoQ,KAAO,EAAM1lB,KAAK+O,IAAOuG,EAAMoQ,KAAOpQ,EAAMpI,SAAY,EAAI,KAElFoI,EAAMpI,SAAWoI,EAAMqQ,OACzBrQ,EAAMpI,SAAYoI,EAAMqQ,KAAO,EAAM3lB,KAAK+O,IAAOuG,EAAMpI,SAAWoI,EAAMqQ,KAAQ,EAAI,KAGlFrQ,EAAMjI,SAAWiI,EAAMsQ,OACzBtQ,EAAMjI,SAAYiI,EAAMsQ,KAAO,EAAM5lB,KAAK+O,IAAOuG,EAAMsQ,KAAOtQ,EAAMjI,SAAY,EAAI,KAElFiI,EAAMjI,SAAWiI,EAAMuQ,OACzBvQ,EAAMjI,SAAYiI,EAAMuQ,KAAO,EAAM7lB,KAAK+O,IAAOuG,EAAMjI,SAAWiI,EAAMuQ,KAAQ,EAAI,KAIjFnV,EAASqV,gBAAiBrV,EAASqV,cAAgBzQ,EAAMwQ,eAAe5f,GACxEwK,EAASsV,gBAAiBtV,EAASsV,cAAgB1Q,EAAMwQ,eAAe3f,GACxEuK,EAASuV,WAAYvV,EAASuV,SAAW55B,KAAK4K,OACnDyZ,EAASxK,GAAKoP,EAAMwQ,eAAe5f,EAAIwK,EAASqV,gBAAkB15B,KAAK4K,MAAQyZ,EAASuV,UAAY,EACpGvV,EAASvK,GAAKmP,EAAMwQ,eAAe3f,EAAIuK,EAASsV,gBAAkB35B,KAAK4K,MAAQyZ,EAASuV,UAAY,EAChGjmB,KAAKwB,IAAI8T,EAAMwQ,eAAe5f,EAAIwK,EAASqV,eAAiB,IAAKrV,EAASxK,EAAI,GAC9ElG,KAAKwB,IAAI8T,EAAMwQ,eAAe3f,EAAIuK,EAASsV,eAAiB,IAAKtV,EAASvK,EAAI,GAClFuK,EAASqV,cAAgBzQ,EAAMwQ,eAAe5f,EAC9CwK,EAASsV,cAAgB1Q,EAAMwQ,eAAe3f,EAC9CuK,EAASuV,SAAW55B,KAAK4K,MAEzButB,EAAQK,aAAal1B,UAAW,eAAkB2lB,EAAc,SAAI,OAAUA,EAAc,SAAI,YAElGvF,WAAY,WACV,IACIwU,EADSj6B,KACKi6B,KACdC,EAAUD,EAAKC,QACflP,EAAQiP,EAAKjP,MACb5E,EAAW6T,EAAK7T,SACpB,GAAK8T,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASh4B,OAA1C,CACA,IAAK0oB,EAAM1I,YAAc0I,EAAMzI,QAG7B,OAFAyI,EAAM1I,WAAY,OAClB0I,EAAMzI,SAAU,GAGlByI,EAAM1I,WAAY,EAClB0I,EAAMzI,SAAU,EAChB,IAAIqZ,EAAoB,IACpBC,EAAoB,IACpBC,EAAoB1V,EAASxK,EAAIggB,EACjCG,EAAe/Q,EAAMpI,SAAWkZ,EAChCE,EAAoB5V,EAASvK,EAAIggB,EACjCI,EAAejR,EAAMjI,SAAWiZ,EAGjB,IAAf5V,EAASxK,IAAWggB,EAAoBlmB,KAAKwB,KAAK6kB,EAAe/Q,EAAMpI,UAAYwD,EAASxK,IAC7E,IAAfwK,EAASvK,IAAWggB,EAAoBnmB,KAAKwB,KAAK+kB,EAAejR,EAAMjI,UAAYqD,EAASvK,IAChG,IAAI0K,EAAmB7Q,KAAKK,IAAI6lB,EAAmBC,GAEnD7Q,EAAMpI,SAAWmZ,EACjB/Q,EAAMjI,SAAWkZ,EAGjB,IAAIf,EAAclQ,EAAMhY,MAAQinB,EAAKhE,MACjCkF,EAAenQ,EAAM/X,OAASgnB,EAAKhE,MACvCjL,EAAMoQ,KAAO1lB,KAAKgM,IAAMwY,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtElQ,EAAMqQ,MAAQrQ,EAAMoQ,KACpBpQ,EAAMsQ,KAAO5lB,KAAKgM,IAAMwY,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxEnQ,EAAMuQ,MAAQvQ,EAAMsQ,KACpBtQ,EAAMpI,SAAWlN,KAAKK,IAAIL,KAAKgM,IAAIsJ,EAAMpI,SAAUoI,EAAMqQ,MAAOrQ,EAAMoQ,MACtEpQ,EAAMjI,SAAWrN,KAAKK,IAAIL,KAAKgM,IAAIsJ,EAAMjI,SAAUiI,EAAMuQ,MAAOvQ,EAAMsQ,MAEtEpB,EAAQK,aAAa/0B,WAAW+gB,GAAkBlhB,UAAW,eAAkB2lB,EAAc,SAAI,OAAUA,EAAc,SAAI,WAE/HkR,gBAAiB,WACf,IACIjC,EADSj6B,KACKi6B,KACdC,EAAUD,EAAKC,QACfA,EAAQlK,UAHChwB,KAGkB+a,gBAHlB/a,KAG2C0Y,cACtDwhB,EAAQI,SAASj1B,UAAU,+BAC3B60B,EAAQK,aAAal1B,UAAU,sBAC/B60B,EAAQlK,cAAWjpB,EACnBmzB,EAAQI,cAAWvzB,EACnBmzB,EAAQK,kBAAexzB,EAEvBkzB,EAAKhE,MAAQ,EACbgE,EAAKb,aAAe,IAIxB90B,OAAQ,SAAgB+B,GACtB,IACI4zB,EADSj6B,KACKi6B,KAEdA,EAAKhE,OAAwB,IAAfgE,EAAKhE,MAErBgE,EAAKkC,MAGLlC,EAAKmC,GAAG/1B,IAGZ+1B,GAAI,SAAc/1B,GAChB,IAgBIg2B,EACAC,EAGAhY,EACAC,EACAgY,EACAC,EACAC,EACAC,EACAxB,EACAC,EACAwB,EACAC,EACAC,EACAC,EACA9B,EACAC,EAjCA/nB,EAASlT,KAETi6B,EAAO/mB,EAAO+mB,KACdjsB,EAASkF,EAAOlF,OAAOisB,KACvBC,EAAUD,EAAKC,QACflP,EAAQiP,EAAKjP,OAEZkP,EAAQlK,WACXkK,EAAQlK,SAAW9c,EAAOmI,aAAe9Y,EAAE2Q,EAAOmI,cAAgBnI,EAAOkB,OAAO9J,GAAG4I,EAAOwF,aAC1FwhB,EAAQI,SAAWJ,EAAQlK,SAASrkB,KAAK,oBACzCuuB,EAAQK,aAAeL,EAAQI,SAAS9uB,OAAQ,IAAOwC,EAAqB,iBAEzEksB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASh4B,UAE1C43B,EAAQlK,SAASrsB,SAAU,GAAMqK,EAAuB,uBAqBpB,IAAzBgd,EAAM+P,aAAanf,GAAqBvV,GACjDg2B,EAAoB,aAAXh2B,EAAE8b,KAAsB9b,EAAEy0B,eAAe,GAAGhY,MAAQzc,EAAEyc,MAC/DwZ,EAAoB,aAAXj2B,EAAE8b,KAAsB9b,EAAEy0B,eAAe,GAAG9X,MAAQ3c,EAAE2c,QAE/DqZ,EAASrR,EAAM+P,aAAanf,EAC5B0gB,EAAStR,EAAM+P,aAAalf,GAG9Boe,EAAKhE,MAAQiE,EAAQK,aAAah2B,KAAK,qBAAuByJ,EAAOwsB,SACrEP,EAAKb,aAAec,EAAQK,aAAah2B,KAAK,qBAAuByJ,EAAOwsB,SACxEn0B,GACF20B,EAAad,EAAQlK,SAAS,GAAGtnB,YACjCuyB,EAAcf,EAAQlK,SAAS,GAAGnnB,aAGlCyb,EAFU4V,EAAQlK,SAASlnB,SAASU,KAEhBwxB,EAAa,EAAMqB,EACvC9X,EAFU2V,EAAQlK,SAASlnB,SAASS,IAEhB0xB,EAAc,EAAMqB,EAExCG,EAAavC,EAAQI,SAAS,GAAG5xB,YACjCg0B,EAAcxC,EAAQI,SAAS,GAAGzxB,aAClCqyB,EAAcuB,EAAaxC,EAAKhE,MAChCkF,EAAeuB,EAAczC,EAAKhE,MAIlC4G,IAFAF,EAAgBjnB,KAAKgM,IAAMsZ,EAAa,EAAME,EAAc,EAAK,IAGjE4B,IAFAF,EAAgBlnB,KAAKgM,IAAMuZ,EAAc,EAAME,EAAe,EAAK,KAInEoB,EAAajY,EAAQ2V,EAAKhE,OAGT0G,IACfJ,EAAaI,GAEEE,EAAbN,IACFA,EAAaM,IANfL,EAAajY,EAAQ0V,EAAKhE,OAST2G,IACfJ,EAAaI,GAEEE,EAAbN,IACFA,EAAaM,IAIfN,EADAD,EAAa,EAGfrC,EAAQK,aAAa/0B,WAAW,KAAKH,UAAW,eAAiBk3B,EAAa,OAASC,EAAa,SACpGtC,EAAQI,SAAS90B,WAAW,KAAKH,UAAW,4BAA+B40B,EAAU,MAAI,OAE3FkC,IAAK,WACH,IAAIjpB,EAASlT,KAETi6B,EAAO/mB,EAAO+mB,KACdjsB,EAASkF,EAAOlF,OAAOisB,KACvBC,EAAUD,EAAKC,QAEdA,EAAQlK,WACXkK,EAAQlK,SAAW9c,EAAOmI,aAAe9Y,EAAE2Q,EAAOmI,cAAgBnI,EAAOkB,OAAO9J,GAAG4I,EAAOwF,aAC1FwhB,EAAQI,SAAWJ,EAAQlK,SAASrkB,KAAK,oBACzCuuB,EAAQK,aAAeL,EAAQI,SAAS9uB,OAAQ,IAAOwC,EAAqB,iBAEzEksB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASh4B,SAE1C23B,EAAKhE,MAAQ,EACbgE,EAAKb,aAAe,EACpBc,EAAQK,aAAa/0B,WAAW,KAAKH,UAAU,sBAC/C60B,EAAQI,SAAS90B,WAAW,KAAKH,UAAU,+BAC3C60B,EAAQlK,SAAS/rB,YAAa,GAAM+J,EAAuB,kBAC3DksB,EAAQlK,cAAWjpB,IAGrByqB,OAAQ,WACN,IAAIte,EAASlT,KACTi6B,EAAO/mB,EAAO+mB,KAClB,IAAIA,EAAK/lB,QAAT,CACA+lB,EAAK/lB,SAAU,EAEf,IAAIjE,IAA+C,eAA7BiD,EAAO0O,YAAYkG,QAA0B1Y,EAAQa,kBAAmBiD,EAAOlF,OAAO+Z,mBAAmB,CAAEC,SAAS,EAAM7hB,SAAS,GAGrJiJ,EAAQkB,UACV4C,EAAOS,WAAW/N,GAAG,eAAgB,gBAAiBq0B,EAAKD,eAAgB/pB,GAC3EiD,EAAOS,WAAW/N,GAAG,gBAAiB,gBAAiBq0B,EAAKS,gBAAiBzqB,GAC7EiD,EAAOS,WAAW/N,GAAG,aAAc,gBAAiBq0B,EAAKY,aAAc5qB,IACjC,eAA7BiD,EAAO0O,YAAYkG,QAC5B5U,EAAOS,WAAW/N,GAAGsN,EAAO0O,YAAYkG,MAAO,gBAAiBmS,EAAKD,eAAgB/pB,GACrFiD,EAAOS,WAAW/N,GAAGsN,EAAO0O,YAAYqG,KAAM,gBAAiBgS,EAAKS,gBAAiBzqB,GACrFiD,EAAOS,WAAW/N,GAAGsN,EAAO0O,YAAYsG,IAAK,gBAAiB+R,EAAKY,aAAc5qB,IAInFiD,EAAOS,WAAW/N,GAAGsN,EAAO0O,YAAYqG,KAAO,IAAO/U,EAAOlF,OAAOisB,KAAmB,eAAIA,EAAK/V,eAElGuN,QAAS,WACP,IAAIve,EAASlT,KACTi6B,EAAO/mB,EAAO+mB,KAClB,GAAKA,EAAK/lB,QAAV,CAEAhB,EAAO+mB,KAAK/lB,SAAU,EAEtB,IAAIjE,IAA+C,eAA7BiD,EAAO0O,YAAYkG,QAA0B1Y,EAAQa,kBAAmBiD,EAAOlF,OAAO+Z,mBAAmB,CAAEC,SAAS,EAAM7hB,SAAS,GAGrJiJ,EAAQkB,UACV4C,EAAOS,WAAWrM,IAAI,eAAgB,gBAAiB2yB,EAAKD,eAAgB/pB,GAC5EiD,EAAOS,WAAWrM,IAAI,gBAAiB,gBAAiB2yB,EAAKS,gBAAiBzqB,GAC9EiD,EAAOS,WAAWrM,IAAI,aAAc,gBAAiB2yB,EAAKY,aAAc5qB,IAClC,eAA7BiD,EAAO0O,YAAYkG,QAC5B5U,EAAOS,WAAWrM,IAAI4L,EAAO0O,YAAYkG,MAAO,gBAAiBmS,EAAKD,eAAgB/pB,GACtFiD,EAAOS,WAAWrM,IAAI4L,EAAO0O,YAAYqG,KAAM,gBAAiBgS,EAAKS,gBAAiBzqB,GACtFiD,EAAOS,WAAWrM,IAAI4L,EAAO0O,YAAYsG,IAAK,gBAAiB+R,EAAKY,aAAc5qB,IAIpFiD,EAAOS,WAAWrM,IAAI4L,EAAO0O,YAAYqG,KAAO,IAAO/U,EAAOlF,OAAOisB,KAAmB,eAAIA,EAAK/V,gBAkGjG6Y,EAAO,CACTC,YAAa,SAAqB7yB,EAAO8yB,QACd,IAApBA,IAA6BA,GAAkB,GAEpD,IAAI/pB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOyhB,KAC3B,QAAqB,IAAVtlB,GACkB,IAAzB+I,EAAOkB,OAAO9R,OAAlB,CACA,IAEI0tB,EAFY9c,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QAGpDhB,EAAOS,WAAW5S,SAAU,IAAOmS,EAAOlF,OAAiB,WAAI,6BAAgC7D,EAAQ,MACvG+I,EAAOkB,OAAO9J,GAAGH,GAEjB+yB,EAAUlN,EAASrkB,KAAM,IAAOqC,EAAmB,aAAI,SAAYA,EAAkB,YAAI,UAAaA,EAAmB,aAAI,MAC7HgiB,EAAS7rB,SAAS6J,EAAOmvB,eAAkBnN,EAAS7rB,SAAS6J,EAAOovB,cAAiBpN,EAAS7rB,SAAS6J,EAAOqvB,gBAChHH,EAAUA,EAAQl5B,IAAIgsB,EAAS,KAEV,IAAnBkN,EAAQ56B,QAEZ46B,EAAQtzB,KAAK,SAAU0zB,EAAY3S,GACjC,IAAI2P,EAAW/3B,EAAEooB,GACjB2P,EAAS32B,SAASqK,EAAOqvB,cAEzB,IAAIE,EAAajD,EAAS/1B,KAAK,mBAC3BqmB,EAAM0P,EAAS/1B,KAAK,YACpBsmB,EAASyP,EAAS/1B,KAAK,eACvBumB,EAAQwP,EAAS/1B,KAAK,cAE1B2O,EAAOwX,UAAU4P,EAAS,GAAK1P,GAAO2S,EAAa1S,EAAQC,GAAO,EAAO,WACvE,GAAI,MAAO5X,GAA8CA,KAAWA,GAAWA,EAAOlF,UAAWkF,EAAO6J,UAAxG,CAqBA,GApBIwgB,GACFjD,EAAS7wB,IAAI,mBAAqB,QAAW8zB,EAAa,MAC1DjD,EAASx1B,WAAW,qBAEhB+lB,IACFyP,EAAS/1B,KAAK,SAAUsmB,GACxByP,EAASx1B,WAAW,gBAElBgmB,IACFwP,EAAS/1B,KAAK,QAASumB,GACvBwP,EAASx1B,WAAW,eAElB8lB,IACF0P,EAAS/1B,KAAK,MAAOqmB,GACrB0P,EAASx1B,WAAW,cAIxBw1B,EAAS32B,SAASqK,EAAOovB,aAAan5B,YAAY+J,EAAOqvB,cACzDrN,EAASrkB,KAAM,IAAOqC,EAAqB,gBAAI9J,SAC3CgP,EAAOlF,OAAOoM,MAAQ6iB,EAAiB,CACzC,IAAIO,EAAqBxN,EAASzrB,KAAK,2BACvC,GAAIyrB,EAAS7rB,SAAS+O,EAAOlF,OAAOqM,qBAAsB,CACxD,IAAIojB,EAAgBvqB,EAAOS,WAAW5S,SAAU,6BAAgCy8B,EAAqB,WAAetqB,EAAOlF,OAA0B,oBAAI,KACzJkF,EAAOuc,KAAKuN,YAAYS,EAActzB,SAAS,OAC1C,CACL,IAAIuzB,EAAkBxqB,EAAOS,WAAW5S,SAAU,IAAOmS,EAAOlF,OAA0B,oBAAI,6BAAgCwvB,EAAqB,MACnJtqB,EAAOuc,KAAKuN,YAAYU,EAAgBvzB,SAAS,IAGrD+I,EAAO/B,KAAK,iBAAkB6e,EAAS,GAAIsK,EAAS,OAGtDpnB,EAAO/B,KAAK,gBAAiB6e,EAAS,GAAIsK,EAAS,QAGvD5K,KAAM,WACJ,IAAIxc,EAASlT,KACT2T,EAAaT,EAAOS,WACpBkY,EAAe3Y,EAAOlF,OACtBoG,EAASlB,EAAOkB,OAChBsE,EAAcxF,EAAOwF,YACrB1E,EAAYd,EAAOe,SAAW4X,EAAa5X,QAAQC,QACnDlG,EAAS6d,EAAa4D,KAEtB5Z,EAAgBgW,EAAahW,cAKjC,SAAS8nB,EAAWxzB,GAClB,GAAI6J,GACF,GAAIL,EAAW5S,SAAU,IAAO8qB,EAAuB,WAAI,6BAAgC1hB,EAAQ,MAAQ7H,OACzG,OAAO,OAEJ,GAAI8R,EAAOjK,GAAU,OAAO,EACnC,OAAO,EAET,SAASqS,EAAW8c,GAClB,OAAItlB,EACKzR,EAAE+2B,GAAS/0B,KAAK,2BAElBhC,EAAE+2B,GAASnvB,QAIpB,GApBsB,SAAlB0L,IACFA,EAAgB,GAkBb3C,EAAOuc,KAAKmO,qBAAsB1qB,EAAOuc,KAAKmO,oBAAqB,GACpE1qB,EAAOlF,OAAOmK,sBAChBxE,EAAW5S,SAAU,IAAO8qB,EAA8B,mBAAIjiB,KAAK,SAAUi0B,EAASvE,GACpF,IAAInvB,EAAQ6J,EAAYzR,EAAE+2B,GAAS/0B,KAAK,2BAA6BhC,EAAE+2B,GAASnvB,QAChF+I,EAAOuc,KAAKuN,YAAY7yB,UAErB,GAAoB,EAAhB0L,EACT,IAAK,IAAIxT,EAAIqW,EAAarW,EAAIqW,EAAc7C,EAAexT,GAAK,EAC1Ds7B,EAAWt7B,IAAM6Q,EAAOuc,KAAKuN,YAAY36B,QAG/C6Q,EAAOuc,KAAKuN,YAAYtkB,GAE1B,GAAI1K,EAAO8vB,aACT,GAAoB,EAAhBjoB,GAAsB7H,EAAO+vB,oBAAkD,EAA5B/vB,EAAO+vB,mBAAyB,CAMrF,IALA,IAAIC,EAAShwB,EAAO+vB,mBAChB1R,EAAMxW,EACNooB,EAAWvoB,KAAKgM,IAAIhJ,EAAc2T,EAAM3W,KAAKK,IAAIioB,EAAQ3R,GAAMjY,EAAO9R,QACtE47B,EAAWxoB,KAAKK,IAAI2C,EAAchD,KAAKK,IAAIsW,EAAK2R,GAAS,GAEpD1mB,EAAMoB,EAAc7C,EAAeyB,EAAM2mB,EAAU3mB,GAAO,EAC7DqmB,EAAWrmB,IAAQpE,EAAOuc,KAAKuN,YAAY1lB,GAGjD,IAAK,IAAIE,EAAM0mB,EAAU1mB,EAAMkB,EAAalB,GAAO,EAC7CmmB,EAAWnmB,IAAQtE,EAAOuc,KAAKuN,YAAYxlB,OAE5C,CACL,IAAI+C,EAAY5G,EAAW5S,SAAU,IAAO8qB,EAA2B,gBAChD,EAAnBtR,EAAUjY,QAAc4Q,EAAOuc,KAAKuN,YAAYxgB,EAAWjC,IAE/D,IAAIE,EAAY9G,EAAW5S,SAAU,IAAO8qB,EAA2B,gBAChD,EAAnBpR,EAAUnY,QAAc4Q,EAAOuc,KAAKuN,YAAYxgB,EAAW/B,OAiFnE0jB,EAAa,CACfC,aAAc,SAAsBxiB,EAAGC,GACrC,IACMoiB,EACAC,EACAG,EAqBFC,EACAC,EAzBAC,EAIK,SAAUC,EAAOjhB,GAGtB,IAFA0gB,GAAY,EACZD,EAAWQ,EAAMn8B,OACY,EAAtB27B,EAAWC,GAEZO,EADJJ,EAAQJ,EAAWC,GAAY,IACX1gB,EAClB0gB,EAAWG,EAEXJ,EAAWI,EAGf,OAAOJ,GAuBX,OApBAj+B,KAAK4b,EAAIA,EACT5b,KAAK6b,EAAIA,EACT7b,KAAK+0B,UAAYnZ,EAAEtZ,OAAS,EAO5BtC,KAAK0+B,YAAc,SAAqB5E,GACtC,OAAKA,GAGLyE,EAAKC,EAAax+B,KAAK4b,EAAGke,GAC1BwE,EAAKC,EAAK,GAIAzE,EAAK95B,KAAK4b,EAAE0iB,KAAQt+B,KAAK6b,EAAE0iB,GAAMv+B,KAAK6b,EAAEyiB,KAASt+B,KAAK4b,EAAE2iB,GAAMv+B,KAAK4b,EAAE0iB,IAAQt+B,KAAK6b,EAAEyiB,IAR5E,GAUbt+B,MAGT2+B,uBAAwB,SAAgCC,GACtD,IAAI1rB,EAASlT,KACRkT,EAAO2rB,WAAWC,SACrB5rB,EAAO2rB,WAAWC,OAAS5rB,EAAOlF,OAAOoM,KACrC,IAAI+jB,EAAWC,aAAalrB,EAAOqB,WAAYqqB,EAAErqB,YACjD,IAAI4pB,EAAWC,aAAalrB,EAAOoB,SAAUsqB,EAAEtqB,YAGvDoH,aAAc,SAAsBqjB,EAAgBpjB,GAClD,IAEIqjB,EACAC,EAHA/rB,EAASlT,KACTk/B,EAAahsB,EAAO2rB,WAAWM,QAGnC,SAASC,EAAuBR,GAK9B,IAAI7lB,EAAY7F,EAAOY,cAAgBZ,EAAO6F,UAAY7F,EAAO6F,UAC7B,UAAhC7F,EAAOlF,OAAO6wB,WAAWQ,KAC3BnsB,EAAO2rB,WAAWF,uBAAuBC,GAGzCK,GAAuB/rB,EAAO2rB,WAAWC,OAAOJ,aAAa3lB,IAG1DkmB,GAAuD,cAAhC/rB,EAAOlF,OAAO6wB,WAAWQ,KACnDL,GAAcJ,EAAEjlB,eAAiBilB,EAAEvlB,iBAAmBnG,EAAOyG,eAAiBzG,EAAOmG,gBACrF4lB,GAAwBlmB,EAAY7F,EAAOmG,gBAAkB2lB,EAAcJ,EAAEvlB,gBAG3EnG,EAAOlF,OAAO6wB,WAAWS,UAC3BL,EAAsBL,EAAEjlB,eAAiBslB,GAE3CL,EAAEnlB,eAAewlB,GACjBL,EAAEljB,aAAaujB,EAAqB/rB,GACpC0rB,EAAE/jB,oBACF+jB,EAAE5kB,sBAEJ,GAAI5I,MAAMC,QAAQ6tB,GAChB,IAAK,IAAI78B,EAAI,EAAGA,EAAI68B,EAAW58B,OAAQD,GAAK,EACtC68B,EAAW78B,KAAOsZ,GAAgBujB,EAAW78B,aAActC,GAC7Dq/B,EAAuBF,EAAW78B,SAG7B68B,aAAsBn/B,GAAU4b,IAAiBujB,GAC1DE,EAAuBF,IAG3BzmB,cAAe,SAAuBhT,EAAUkW,GAC9C,IAEItZ,EAFA6Q,EAASlT,KACTk/B,EAAahsB,EAAO2rB,WAAWM,QAEnC,SAASI,EAAwBX,GAC/BA,EAAEnmB,cAAchT,EAAUyN,GACT,IAAbzN,IACFm5B,EAAE5iB,kBACE4iB,EAAE5wB,OAAOmO,YACX9P,EAAMI,SAAS,WACbmyB,EAAEvmB,qBAGNumB,EAAEjrB,WAAWzL,cAAc,WACpBg3B,IACDN,EAAE5wB,OAAOoM,MAAwC,UAAhClH,EAAOlF,OAAO6wB,WAAWQ,IAC5CT,EAAExhB,UAEJwhB,EAAE12B,oBAIR,GAAIkJ,MAAMC,QAAQ6tB,GAChB,IAAK78B,EAAI,EAAGA,EAAI68B,EAAW58B,OAAQD,GAAK,EAClC68B,EAAW78B,KAAOsZ,GAAgBujB,EAAW78B,aAActC,GAC7Dw/B,EAAwBL,EAAW78B,SAG9B68B,aAAsBn/B,GAAU4b,IAAiBujB,GAC1DK,EAAwBL,KA8D1BM,EAAO,CACTC,gBAAiB,SAAyBtsB,GAExC,OADAA,EAAI5O,KAAK,WAAY,KACd4O,GAETusB,UAAW,SAAmBvsB,EAAKwsB,GAEjC,OADAxsB,EAAI5O,KAAK,OAAQo7B,GACVxsB,GAETysB,WAAY,SAAoBzsB,EAAK0sB,GAEnC,OADA1sB,EAAI5O,KAAK,aAAcs7B,GAChB1sB,GAET2sB,UAAW,SAAmB3sB,GAE5B,OADAA,EAAI5O,KAAK,iBAAiB,GACnB4O,GAET4sB,SAAU,SAAkB5sB,GAE1B,OADAA,EAAI5O,KAAK,iBAAiB,GACnB4O,GAET6sB,WAAY,SAAoB35B,GAC9B,IAAI6M,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOwxB,KAC3B,GAAkB,KAAdn5B,EAAEoqB,QAAN,CACA,IAAIwP,EAAY19B,EAAE8D,EAAEC,QAChB4M,EAAOiX,YAAcjX,EAAOiX,WAAW+J,SAAW+L,EAAUv5B,GAAGwM,EAAOiX,WAAW+J,WAC7EhhB,EAAO2G,QAAU3G,EAAOlF,OAAOoM,MACnClH,EAAOiK,YAELjK,EAAO2G,MACT3G,EAAOssB,KAAKU,OAAOlyB,EAAOmyB,kBAE1BjtB,EAAOssB,KAAKU,OAAOlyB,EAAOoyB,mBAG1BltB,EAAOiX,YAAcjX,EAAOiX,WAAWgK,SAAW8L,EAAUv5B,GAAGwM,EAAOiX,WAAWgK,WAC7EjhB,EAAO0G,cAAgB1G,EAAOlF,OAAOoM,MACzClH,EAAOoK,YAELpK,EAAO0G,YACT1G,EAAOssB,KAAKU,OAAOlyB,EAAOqyB,mBAE1BntB,EAAOssB,KAAKU,OAAOlyB,EAAOsyB,mBAG1BptB,EAAOuhB,YAAcwL,EAAUv5B,GAAI,IAAOwM,EAAOlF,OAAOymB,WAAsB,cAChFwL,EAAU,GAAGM,UAGjBL,OAAQ,SAAgBM,GACtB,IACIC,EADSzgC,KACaw/B,KAAKkB,WACH,IAAxBD,EAAan+B,SACjBm+B,EAAa79B,KAAK,IAClB69B,EAAa79B,KAAK49B,KAEpBG,iBAAkB,WAChB,IAAIztB,EAASlT,KAEb,IAAIkT,EAAOlF,OAAOoM,KAAlB,CACA,IAAIsU,EAAMxb,EAAOiX,WACb+J,EAAUxF,EAAIwF,QACdC,EAAUzF,EAAIyF,QAEdA,GAA4B,EAAjBA,EAAQ7xB,SACjB4Q,EAAO0G,YACT1G,EAAOssB,KAAKM,UAAU3L,GAEtBjhB,EAAOssB,KAAKO,SAAS5L,IAGrBD,GAA4B,EAAjBA,EAAQ5xB,SACjB4Q,EAAO2G,MACT3G,EAAOssB,KAAKM,UAAU5L,GAEtBhhB,EAAOssB,KAAKO,SAAS7L,MAI3B0M,iBAAkB,WAChB,IAAI1tB,EAASlT,KACTgO,EAASkF,EAAOlF,OAAOwxB,KACvBtsB,EAAOuhB,YAAcvhB,EAAOlF,OAAOymB,WAAWsC,WAAa7jB,EAAOuhB,WAAWI,SAAW3hB,EAAOuhB,WAAWI,QAAQvyB,QACpH4Q,EAAOuhB,WAAWI,QAAQjrB,KAAK,SAAU2rB,EAAasL,GACpD,IAAIC,EAAYv+B,EAAEs+B,GAClB3tB,EAAOssB,KAAKC,gBAAgBqB,GAC5B5tB,EAAOssB,KAAKE,UAAUoB,EAAW,UACjC5tB,EAAOssB,KAAKI,WAAWkB,EAAW9yB,EAAO+yB,wBAAwB1zB,QAAQ,YAAayzB,EAAU32B,QAAU,OAIhHof,KAAM,WACJ,IAAIrW,EAASlT,KAEbkT,EAAOC,IAAI3I,OAAO0I,EAAOssB,KAAKkB,YAG9B,IACIxM,EACAC,EAFAnmB,EAASkF,EAAOlF,OAAOwxB,KAGvBtsB,EAAOiX,YAAcjX,EAAOiX,WAAW+J,UACzCA,EAAUhhB,EAAOiX,WAAW+J,SAE1BhhB,EAAOiX,YAAcjX,EAAOiX,WAAWgK,UACzCA,EAAUjhB,EAAOiX,WAAWgK,SAE1BD,IACFhhB,EAAOssB,KAAKC,gBAAgBvL,GAC5BhhB,EAAOssB,KAAKE,UAAUxL,EAAS,UAC/BhhB,EAAOssB,KAAKI,WAAW1L,EAASlmB,EAAOoyB,kBACvClM,EAAQtuB,GAAG,UAAWsN,EAAOssB,KAAKQ,aAEhC7L,IACFjhB,EAAOssB,KAAKC,gBAAgBtL,GAC5BjhB,EAAOssB,KAAKE,UAAUvL,EAAS,UAC/BjhB,EAAOssB,KAAKI,WAAWzL,EAASnmB,EAAOsyB,kBACvCnM,EAAQvuB,GAAG,UAAWsN,EAAOssB,KAAKQ,aAIhC9sB,EAAOuhB,YAAcvhB,EAAOlF,OAAOymB,WAAWsC,WAAa7jB,EAAOuhB,WAAWI,SAAW3hB,EAAOuhB,WAAWI,QAAQvyB,QACpH4Q,EAAOuhB,WAAWthB,IAAIvN,GAAG,UAAY,IAAOsN,EAAOlF,OAAOymB,WAAsB,YAAIvhB,EAAOssB,KAAKQ,aAGpGxT,QAAS,WACP,IAGI0H,EACAC,EAJAjhB,EAASlT,KACTkT,EAAOssB,KAAKkB,YAA8C,EAAhCxtB,EAAOssB,KAAKkB,WAAWp+B,QAAc4Q,EAAOssB,KAAKkB,WAAWx8B,SAItFgP,EAAOiX,YAAcjX,EAAOiX,WAAW+J,UACzCA,EAAUhhB,EAAOiX,WAAW+J,SAE1BhhB,EAAOiX,YAAcjX,EAAOiX,WAAWgK,UACzCA,EAAUjhB,EAAOiX,WAAWgK,SAE1BD,GACFA,EAAQ5sB,IAAI,UAAW4L,EAAOssB,KAAKQ,YAEjC7L,GACFA,EAAQ7sB,IAAI,UAAW4L,EAAOssB,KAAKQ,YAIjC9sB,EAAOuhB,YAAcvhB,EAAOlF,OAAOymB,WAAWsC,WAAa7jB,EAAOuhB,WAAWI,SAAW3hB,EAAOuhB,WAAWI,QAAQvyB,QACpH4Q,EAAOuhB,WAAWthB,IAAI7L,IAAI,UAAY,IAAO4L,EAAOlF,OAAOymB,WAAsB,YAAIvhB,EAAOssB,KAAKQ,cA0DnGgB,EAAU,CACZzX,KAAM,WACJ,IAAIrW,EAASlT,KACb,GAAKkT,EAAOlF,OAAOtM,QAAnB,CACA,IAAKJ,EAAII,UAAYJ,EAAII,QAAQu/B,UAG/B,OAFA/tB,EAAOlF,OAAOtM,QAAQwS,SAAU,OAChChB,EAAOlF,OAAOkzB,eAAehtB,SAAU,GAGzC,IAAIxS,EAAUwR,EAAOxR,QACrBA,EAAQib,aAAc,EACtBjb,EAAQy/B,MAAQH,EAAQI,iBACnB1/B,EAAQy/B,MAAMl8B,KAAQvD,EAAQy/B,MAAM18B,SACzC/C,EAAQ2/B,cAAc,EAAG3/B,EAAQy/B,MAAM18B,MAAOyO,EAAOlF,OAAOgc,oBACvD9W,EAAOlF,OAAOtM,QAAQ4/B,cACzBhgC,EAAIlB,iBAAiB,WAAY8S,EAAOxR,QAAQ6/B,uBAGpD/U,QAAS,WACMxsB,KACDgO,OAAOtM,QAAQ4/B,cACzBhgC,EAAIjB,oBAAoB,WAFbL,KAEgC0B,QAAQ6/B,qBAGvDA,mBAAoB,WACLvhC,KACN0B,QAAQy/B,MAAQH,EAAQI,gBADlBphC,KAEN0B,QAAQ2/B,cAFFrhC,KAEuBgO,OAAOsK,MAF9BtY,KAE4C0B,QAAQy/B,MAAM18B,OAAO,IAEhF28B,cAAe,WACb,IAAII,EAAYlgC,EAAIF,SAASqgC,SAASnwB,MAAM,GAAGnO,MAAM,KAAK4E,OAAO,SAAU25B,GAAQ,MAAgB,KAATA,IACtF/M,EAAQ6M,EAAUl/B,OAGtB,MAAO,CAAE2C,IAFCu8B,EAAU7M,EAAQ,GAETlwB,MADP+8B,EAAU7M,EAAQ,KAGhCgN,WAAY,SAAoB18B,EAAKkF,GAEnC,GADanK,KACD0B,QAAQib,aADP3c,KAC8BgO,OAAOtM,QAAQwS,QAA1D,CACA,IAAIiC,EAFSnW,KAEMoU,OAAO9J,GAAGH,GACzB1F,EAAQu8B,EAAQY,QAAQzrB,EAAM5R,KAAK,iBAClCjD,EAAIF,SAASqgC,SAASI,SAAS58B,KAClCR,EAAQQ,EAAM,IAAMR,GAEtB,IAAIq9B,EAAexgC,EAAII,QAAQqgC,MAC3BD,GAAgBA,EAAar9B,QAAUA,IAR9BzE,KAWFgO,OAAOtM,QAAQ4/B,aACxBhgC,EAAII,QAAQ4/B,aAAa,CAAE78B,MAAOA,GAAS,KAAMA,GAEjDnD,EAAII,QAAQu/B,UAAU,CAAEx8B,MAAOA,GAAS,KAAMA,MAGlDm9B,QAAS,SAAiB/3B,GACxB,OAAOA,EAAK8D,WAAWmT,cACpBzT,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAEpBg0B,cAAe,SAAuB/oB,EAAO7T,EAAOwX,GAClD,IAAI/I,EAASlT,KACb,GAAIyE,EACF,IAAK,IAAIpC,EAAI,EAAGC,EAAS4Q,EAAOkB,OAAO9R,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CACjE,IAAI8T,EAAQjD,EAAOkB,OAAO9J,GAAGjI,GAE7B,GADmB2+B,EAAQY,QAAQzrB,EAAM5R,KAAK,mBACzBE,IAAU0R,EAAMhS,SAAS+O,EAAOlF,OAAOqM,qBAAsB,CAChF,IAAIlQ,EAAQgM,EAAMhM,QAClB+I,EAAOoJ,QAAQnS,EAAOmO,EAAO2D,SAIjC/I,EAAOoJ,QAAQ,EAAGhE,EAAO2D,KAgD3B+lB,EAAiB,CACnBC,YAAa,WACX,IAAI/uB,EAASlT,KACTkiC,EAAUjiC,EAAImB,SAASC,KAAKgM,QAAQ,IAAK,IAE7C,GAAI60B,IADkBhvB,EAAOkB,OAAO9J,GAAG4I,EAAOwF,aAAanU,KAAK,aAC/B,CAC/B,IAAI0Y,EAAW/J,EAAOS,WAAW5S,SAAU,IAAOmS,EAAOlF,OAAiB,WAAI,eAAkBk0B,EAAU,MAAQ/3B,QAClH,QAAwB,IAAb8S,EAA4B,OACvC/J,EAAOoJ,QAAQW,KAGnBklB,QAAS,WACP,IAAIjvB,EAASlT,KACb,GAAKkT,EAAOguB,eAAevkB,aAAgBzJ,EAAOlF,OAAOkzB,eAAehtB,QACxE,GAAIhB,EAAOlF,OAAOkzB,eAAeI,cAAgBhgC,EAAII,SAAWJ,EAAII,QAAQ4/B,aAC1EhgC,EAAII,QAAQ4/B,aAAa,KAAM,KAAQ,IAAOpuB,EAAOkB,OAAO9J,GAAG4I,EAAOwF,aAAanU,KAAK,cAAkB,QACrG,CACL,IAAI4R,EAAQjD,EAAOkB,OAAO9J,GAAG4I,EAAOwF,aAChCrX,EAAO8U,EAAM5R,KAAK,cAAgB4R,EAAM5R,KAAK,gBACjDtE,EAAImB,SAASC,KAAOA,GAAQ,KAGhCkoB,KAAM,WACJ,IAAIrW,EAASlT,KACb,MAAKkT,EAAOlF,OAAOkzB,eAAehtB,SAAYhB,EAAOlF,OAAOtM,SAAWwR,EAAOlF,OAAOtM,QAAQwS,SAA7F,CACAhB,EAAOguB,eAAevkB,aAAc,EACpC,IAAItb,EAAOpB,EAAImB,SAASC,KAAKgM,QAAQ,IAAK,IAC1C,GAAIhM,EAEF,IADA,IACSgB,EAAI,EAAGC,EAAS4Q,EAAOkB,OAAO9R,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CACjE,IAAI8T,EAAQjD,EAAOkB,OAAO9J,GAAGjI,GAE7B,IADgB8T,EAAM5R,KAAK,cAAgB4R,EAAM5R,KAAK,mBACpClD,IAAS8U,EAAMhS,SAAS+O,EAAOlF,OAAOqM,qBAAsB,CAC5E,IAAIlQ,EAAQgM,EAAMhM,QAClB+I,EAAOoJ,QAAQnS,EANP,EAMqB+I,EAAOlF,OAAOgc,oBAAoB,IAIjE9W,EAAOlF,OAAOkzB,eAAekB,YAC/B7/B,EAAEjB,GAAKsE,GAAG,aAAcsN,EAAOguB,eAAee,eAGlDzV,QAAS,WACMxsB,KACFgO,OAAOkzB,eAAekB,YAC/B7/B,EAAEjB,GAAKgG,IAAI,aAFAtH,KAEqBkhC,eAAee,eAiDjDI,EAAW,CACbC,IAAK,WACH,IAAIpvB,EAASlT,KACTuiC,EAAiBrvB,EAAOkB,OAAO9J,GAAG4I,EAAOwF,aACzChM,EAAQwG,EAAOlF,OAAO4lB,SAASlnB,MAC/B61B,EAAeh+B,KAAK,0BACtBmI,EAAQ61B,EAAeh+B,KAAK,yBAA2B2O,EAAOlF,OAAO4lB,SAASlnB,OAEhFwG,EAAO0gB,SAASD,QAAUtnB,EAAMI,SAAS,WACnCyG,EAAOlF,OAAO4lB,SAAS4O,iBACrBtvB,EAAOlF,OAAOoM,MAChBlH,EAAOkK,UACPlK,EAAOoK,UAAUpK,EAAOlF,OAAOsK,OAAO,GAAM,GAC5CpF,EAAO/B,KAAK,aACF+B,EAAO0G,YAGP1G,EAAOlF,OAAO4lB,SAAS6O,gBAIjCvvB,EAAO0gB,SAASE,QAHhB5gB,EAAOoJ,QAAQpJ,EAAOkB,OAAO9R,OAAS,EAAG4Q,EAAOlF,OAAOsK,OAAO,GAAM,GACpEpF,EAAO/B,KAAK,cAJZ+B,EAAOoK,UAAUpK,EAAOlF,OAAOsK,OAAO,GAAM,GAC5CpF,EAAO/B,KAAK,aAOL+B,EAAOlF,OAAOoM,MACvBlH,EAAOkK,UACPlK,EAAOiK,UAAUjK,EAAOlF,OAAOsK,OAAO,GAAM,GAC5CpF,EAAO/B,KAAK,aACF+B,EAAO2G,MAGP3G,EAAOlF,OAAO4lB,SAAS6O,gBAIjCvvB,EAAO0gB,SAASE,QAHhB5gB,EAAOoJ,QAAQ,EAAGpJ,EAAOlF,OAAOsK,OAAO,GAAM,GAC7CpF,EAAO/B,KAAK,cAJZ+B,EAAOiK,UAAUjK,EAAOlF,OAAOsK,OAAO,GAAM,GAC5CpF,EAAO/B,KAAK,cAObzE,IAELob,MAAO,WACL,IAAI5U,EAASlT,KACb,YAAuC,IAA5BkT,EAAO0gB,SAASD,WACvBzgB,EAAO0gB,SAAS8O,UACpBxvB,EAAO0gB,SAAS8O,SAAU,EAC1BxvB,EAAO/B,KAAK,iBACZ+B,EAAO0gB,SAAS0O,OACT,KAETxO,KAAM,WACJ,IAAI5gB,EAASlT,KACb,QAAKkT,EAAO0gB,SAAS8O,eACkB,IAA5BxvB,EAAO0gB,SAASD,UAEvBzgB,EAAO0gB,SAASD,UAClBzxB,aAAagR,EAAO0gB,SAASD,SAC7BzgB,EAAO0gB,SAASD,aAAU5sB,GAE5BmM,EAAO0gB,SAAS8O,SAAU,EAC1BxvB,EAAO/B,KAAK,iBACL,KAETwxB,MAAO,SAAerqB,GACpB,IAAIpF,EAASlT,KACRkT,EAAO0gB,SAAS8O,UACjBxvB,EAAO0gB,SAASgP,SAChB1vB,EAAO0gB,SAASD,SAAWzxB,aAAagR,EAAO0gB,SAASD,SAC5DzgB,EAAO0gB,SAASgP,QAAS,EACX,IAAVtqB,GAAgBpF,EAAOlF,OAAO4lB,SAASiP,mBAIzC3vB,EAAOS,WAAW,GAAGvT,iBAAiB,gBAAiB8S,EAAO0gB,SAASsI,iBACvEhpB,EAAOS,WAAW,GAAGvT,iBAAiB,sBAAuB8S,EAAO0gB,SAASsI,mBAJ7EhpB,EAAO0gB,SAASgP,QAAS,EACzB1vB,EAAO0gB,SAAS0O,WAiFlBQ,EAAO,CACTpnB,aAAc,WAGZ,IAFA,IAAIxI,EAASlT,KACToU,EAASlB,EAAOkB,OACX/R,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI2tB,EAAW9c,EAAOkB,OAAO9J,GAAGjI,GAE5B0gC,GADS/S,EAAS,GAAGrX,kBAEpBzF,EAAOlF,OAAOwN,mBAAoBunB,GAAM7vB,EAAO6F,WACpD,IAAIiqB,EAAK,EACJ9vB,EAAOI,iBACV0vB,EAAKD,EACLA,EAAK,GAEP,IAAIE,EAAe/vB,EAAOlF,OAAOk1B,WAAWC,UACxCztB,KAAKK,IAAI,EAAIL,KAAKwB,IAAI8Y,EAAS,GAAGxW,UAAW,GAC7C,EAAI9D,KAAKgM,IAAIhM,KAAKK,IAAIia,EAAS,GAAGxW,UAAW,GAAI,GACrDwW,EACGvmB,IAAI,CACHmuB,QAASqL,IAEV59B,UAAW,eAAiB09B,EAAK,OAASC,EAAK,cAGtDvqB,cAAe,SAAuBhT,GACpC,IAAIyN,EAASlT,KACToU,EAASlB,EAAOkB,OAChBT,EAAaT,EAAOS,WAExB,GADAS,EAAO5O,WAAWC,GACdyN,EAAOlF,OAAOwN,kBAAiC,IAAb/V,EAAgB,CACpD,IAAI29B,GAAiB,EACrBhvB,EAAOlM,cAAc,WACnB,IAAIk7B,GACClwB,IAAUA,EAAO6J,UAAtB,CACAqmB,GAAiB,EACjBlwB,EAAOmJ,WAAY,EAEnB,IADA,IAAIgnB,EAAgB,CAAC,sBAAuB,iBACnChhC,EAAI,EAAGA,EAAIghC,EAAc/gC,OAAQD,GAAK,EAC7CsR,EAAWjM,QAAQ27B,EAAchhC,UAoDvCihC,EAAO,CACT5nB,aAAc,WACZ,IAYI6nB,EAZArwB,EAASlT,KACTmT,EAAMD,EAAOC,IACbQ,EAAaT,EAAOS,WACpBS,EAASlB,EAAOkB,OAChBovB,EAActwB,EAAOF,MACrBywB,EAAevwB,EAAOD,OACtBY,EAAMX,EAAOY,aACbF,EAAaV,EAAOO,KACpBzF,EAASkF,EAAOlF,OAAO01B,WACvBpwB,EAAeJ,EAAOI,eACtBU,EAAYd,EAAOe,SAAWf,EAAOlF,OAAOiG,QAAQC,QACpDyvB,EAAgB,EAEhB31B,EAAO41B,SACLtwB,GAE2B,KAD7BiwB,EAAgB5vB,EAAWhI,KAAK,wBACdrJ,SAChBihC,EAAgBhhC,EAAE,0CAClBoR,EAAWnJ,OAAO+4B,IAEpBA,EAAc95B,IAAI,CAAEwJ,OAASuwB,EAAc,QAGd,KAD7BD,EAAgBpwB,EAAIxH,KAAK,wBACPrJ,SAChBihC,EAAgBhhC,EAAE,0CAClB4Q,EAAI3I,OAAO+4B,KAIjB,IAAK,IAAIlhC,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI2tB,EAAW5b,EAAO9J,GAAGjI,GACrBma,EAAana,EACb2R,IACFwI,EAAahJ,SAASwc,EAASzrB,KAAK,2BAA4B,KAElE,IAAIs/B,EAA0B,GAAbrnB,EACbsnB,EAAQpuB,KAAKC,MAAMkuB,EAAa,KAChChwB,IACFgwB,GAAcA,EACdC,EAAQpuB,KAAKC,OAAOkuB,EAAa,MAEnC,IAAIrqB,EAAW9D,KAAKK,IAAIL,KAAKgM,IAAIsO,EAAS,GAAGxW,SAAU,IAAK,GACxDupB,EAAK,EACLC,EAAK,EACLe,EAAK,EACLvnB,EAAa,GAAM,GACrBumB,EAAc,GAARe,EAAYlwB,EAClBmwB,EAAK,IACKvnB,EAAa,GAAK,GAAM,GAClCumB,EAAK,EACLgB,EAAc,GAARD,EAAYlwB,IACR4I,EAAa,GAAK,GAAM,GAClCumB,EAAKnvB,EAAsB,EAARkwB,EAAYlwB,EAC/BmwB,EAAKnwB,IACK4I,EAAa,GAAK,GAAM,IAClCumB,GAAMnvB,EACNmwB,EAAM,EAAInwB,EAA4B,EAAbA,EAAiBkwB,GAExCjwB,IACFkvB,GAAMA,GAGHzvB,IACH0vB,EAAKD,EACLA,EAAK,GAGP,IAAI19B,EAAY,YAAciO,EAAe,GAAKuwB,GAAc,iBAAmBvwB,EAAeuwB,EAAa,GAAK,oBAAsBd,EAAK,OAASC,EAAK,OAASe,EAAK,MAM3K,GALIvqB,GAAY,IAAiB,EAAZA,IACnBmqB,EAA8B,GAAbnnB,EAA+B,GAAXhD,EACjC3F,IAAO8vB,EAA+B,IAAbnnB,EAA+B,GAAXhD,IAEnDwW,EAAS3qB,UAAUA,GACf2I,EAAOg2B,aAAc,CAEvB,IAAIC,EAAe3wB,EAAe0c,EAASrkB,KAAK,6BAA+BqkB,EAASrkB,KAAK,4BACzFu4B,EAAc5wB,EAAe0c,EAASrkB,KAAK,8BAAgCqkB,EAASrkB,KAAK,+BACjE,IAAxBs4B,EAAa3hC,SACf2hC,EAAe1hC,EAAG,oCAAuC+Q,EAAe,OAAS,OAAS,YAC1F0c,EAASxlB,OAAOy5B,IAES,IAAvBC,EAAY5hC,SACd4hC,EAAc3hC,EAAG,oCAAuC+Q,EAAe,QAAU,UAAY,YAC7F0c,EAASxlB,OAAO05B,IAEdD,EAAa3hC,SAAU2hC,EAAa,GAAGhjC,MAAM22B,QAAUliB,KAAKK,KAAKyD,EAAU,IAC3E0qB,EAAY5hC,SAAU4hC,EAAY,GAAGjjC,MAAM22B,QAAUliB,KAAKK,IAAIyD,EAAU,KAUhF,GAPA7F,EAAWlK,IAAI,CACb06B,2BAA6B,YAAevwB,EAAa,EAAK,KAC9DwwB,wBAA0B,YAAexwB,EAAa,EAAK,KAC3DywB,uBAAyB,YAAezwB,EAAa,EAAK,KAC1D0wB,mBAAqB,YAAe1wB,EAAa,EAAK,OAGpD5F,EAAO41B,OACT,GAAItwB,EACFiwB,EAAcl+B,UAAW,qBAAwBm+B,EAAc,EAAKx1B,EAAOu2B,cAAgB,QAAWf,EAAc,EAAK,0CAA6Cx1B,EAAkB,YAAI,SACvL,CACL,IAAIw2B,EAAc9uB,KAAKwB,IAAIysB,GAA6D,GAA3CjuB,KAAKC,MAAMD,KAAKwB,IAAIysB,GAAiB,IAC9E3E,EAAa,KACdtpB,KAAK+uB,IAAmB,EAAdD,EAAkB9uB,KAAKiP,GAAM,KAAO,EAC5CjP,KAAKgvB,IAAmB,EAAdF,EAAkB9uB,KAAKiP,GAAM,KAAO,GAE/CggB,EAAS32B,EAAO42B,YAChBC,EAAS72B,EAAO42B,YAAc5F,EAC9Bl2B,EAASkF,EAAOu2B,aACpBhB,EAAcl+B,UAAW,WAAas/B,EAAS,QAAUE,EAAS,uBAA0BpB,EAAe,EAAK36B,GAAU,QAAW26B,EAAe,EAAIoB,EAAU,uBAGtK,IAAIC,EAAW9b,EAAQG,UAAYH,EAAQI,aAAiBxV,EAAa,EAAK,EAC9ED,EACGtO,UAAW,qBAAuBy/B,EAAU,gBAAkB5xB,EAAOI,eAAiB,EAAIqwB,GAAiB,iBAAmBzwB,EAAOI,gBAAkBqwB,EAAgB,GAAK,SAEjLlrB,cAAe,SAAuBhT,GACpC,IACI0N,EADSnT,KACImT,IADJnT,KAEOoU,OAEjB5O,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,GANDzF,KAOFgO,OAAO01B,WAAWE,SAPhB5jC,KAOkCsT,gBAC7CH,EAAIxH,KAAK,uBAAuBnG,WAAWC,KAwD7Cs/B,GAAO,CACTrpB,aAAc,WAIZ,IAHA,IAAIxI,EAASlT,KACToU,EAASlB,EAAOkB,OAChBP,EAAMX,EAAOY,aACRzR,EAAI,EAAGA,EAAI+R,EAAO9R,OAAQD,GAAK,EAAG,CACzC,IAAI2tB,EAAW5b,EAAO9J,GAAGjI,GACrBmX,EAAWwW,EAAS,GAAGxW,SACvBtG,EAAOlF,OAAOg3B,WAAWC,gBAC3BzrB,EAAW9D,KAAKK,IAAIL,KAAKgM,IAAIsO,EAAS,GAAGxW,SAAU,IAAK,IAE1D,IAEI0rB,GADU,IAAM1rB,EAEhB2rB,EAAU,EACVpC,GAJS/S,EAAS,GAAGrX,kBAKrBqqB,EAAK,EAYT,GAXK9vB,EAAOI,eAKDO,IACTqxB,GAAWA,IALXlC,EAAKD,EAELoC,GAAWD,EACXA,EAFAnC,EAAK,GAOP/S,EAAS,GAAG/uB,MAAMmkC,QAAU1vB,KAAKwB,IAAIxB,KAAKouB,MAAMtqB,IAAapF,EAAO9R,OAEhE4Q,EAAOlF,OAAOg3B,WAAWhB,aAAc,CAEzC,IAAIC,EAAe/wB,EAAOI,eAAiB0c,EAASrkB,KAAK,6BAA+BqkB,EAASrkB,KAAK,4BAClGu4B,EAAchxB,EAAOI,eAAiB0c,EAASrkB,KAAK,8BAAgCqkB,EAASrkB,KAAK,+BAC1E,IAAxBs4B,EAAa3hC,SACf2hC,EAAe1hC,EAAG,oCAAuC2Q,EAAOI,eAAiB,OAAS,OAAS,YACnG0c,EAASxlB,OAAOy5B,IAES,IAAvBC,EAAY5hC,SACd4hC,EAAc3hC,EAAG,oCAAuC2Q,EAAOI,eAAiB,QAAU,UAAY,YACtG0c,EAASxlB,OAAO05B,IAEdD,EAAa3hC,SAAU2hC,EAAa,GAAGhjC,MAAM22B,QAAUliB,KAAKK,KAAKyD,EAAU,IAC3E0qB,EAAY5hC,SAAU4hC,EAAY,GAAGjjC,MAAM22B,QAAUliB,KAAKK,IAAIyD,EAAU,IAE9EwW,EACG3qB,UAAW,eAAiB09B,EAAK,OAASC,EAAK,oBAAsBmC,EAAU,gBAAkBD,EAAU,UAGlHzsB,cAAe,SAAuBhT,GACpC,IAAIyN,EAASlT,KACToU,EAASlB,EAAOkB,OAChBsE,EAAcxF,EAAOwF,YACrB/E,EAAaT,EAAOS,WAKxB,GAJAS,EACG5O,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,GACVyN,EAAOlF,OAAOwN,kBAAiC,IAAb/V,EAAgB,CACpD,IAAI29B,GAAiB,EAErBhvB,EAAO9J,GAAGoO,GAAaxQ,cAAc,WACnC,IAAIk7B,GACClwB,IAAUA,EAAO6J,UAAtB,CAEAqmB,GAAiB,EACjBlwB,EAAOmJ,WAAY,EAEnB,IADA,IAAIgnB,EAAgB,CAAC,sBAAuB,iBACnChhC,EAAI,EAAGA,EAAIghC,EAAc/gC,OAAQD,GAAK,EAC7CsR,EAAWjM,QAAQ27B,EAAchhC,UAsDvCgjC,GAAY,CACd3pB,aAAc,WAcZ,IAbA,IAAIxI,EAASlT,KACTwjC,EAActwB,EAAOF,MACrBywB,EAAevwB,EAAOD,OACtBmB,EAASlB,EAAOkB,OAChBT,EAAaT,EAAOS,WACpBa,EAAkBtB,EAAOsB,gBACzBxG,EAASkF,EAAOlF,OAAOs3B,gBACvBhyB,EAAeJ,EAAOI,eACtBjO,EAAY6N,EAAO6F,UACnBwsB,EAASjyB,EAA6BkwB,EAAc,EAA3Bn+B,EAA8Co+B,EAAe,EAA5Bp+B,EAC1DmgC,EAASlyB,EAAetF,EAAOw3B,QAAUx3B,EAAOw3B,OAChDzsB,EAAY/K,EAAOy3B,MAEdpjC,EAAI,EAAGC,EAAS8R,EAAO9R,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CAC1D,IAAI2tB,EAAW5b,EAAO9J,GAAGjI,GACrB8S,EAAYX,EAAgBnS,GAE5BqjC,GAAqBH,EADPvV,EAAS,GAAGrX,kBACmBxD,EAAY,GAAMA,EAAanH,EAAO23B,SAEnFT,EAAU5xB,EAAekyB,EAASE,EAAmB,EACrDP,EAAU7xB,EAAe,EAAIkyB,EAASE,EAEtCE,GAAc7sB,EAAYrD,KAAKwB,IAAIwuB,GAEnClJ,EAAalpB,EAAe,EAAItF,EAAO63B,QAAU,EACjDtJ,EAAajpB,EAAetF,EAAO63B,QAAU,EAAqB,EAGlEnwB,KAAKwB,IAAIqlB,GAAc,OAASA,EAAa,GAC7C7mB,KAAKwB,IAAIslB,GAAc,OAASA,EAAa,GAC7C9mB,KAAKwB,IAAI0uB,GAAc,OAASA,EAAa,GAC7ClwB,KAAKwB,IAAIguB,GAAW,OAASA,EAAU,GACvCxvB,KAAKwB,IAAIiuB,GAAW,OAASA,EAAU,GAE3C,IAAIW,EAAiB,eAAiBvJ,EAAa,MAAQC,EAAa,MAAQoJ,EAAa,gBAAkBT,EAAU,gBAAkBD,EAAU,OAIrJ,GAFAlV,EAAS3qB,UAAUygC,GACnB9V,EAAS,GAAG/uB,MAAMmkC,OAAmD,EAAzC1vB,KAAKwB,IAAIxB,KAAKouB,MAAM4B,IAC5C13B,EAAOg2B,aAAc,CAEvB,IAAI+B,EAAkBzyB,EAAe0c,EAASrkB,KAAK,6BAA+BqkB,EAASrkB,KAAK,4BAC5Fq6B,EAAiB1yB,EAAe0c,EAASrkB,KAAK,8BAAgCqkB,EAASrkB,KAAK,+BACjE,IAA3Bo6B,EAAgBzjC,SAClByjC,EAAkBxjC,EAAG,oCAAuC+Q,EAAe,OAAS,OAAS,YAC7F0c,EAASxlB,OAAOu7B,IAEY,IAA1BC,EAAe1jC,SACjB0jC,EAAiBzjC,EAAG,oCAAuC+Q,EAAe,QAAU,UAAY,YAChG0c,EAASxlB,OAAOw7B,IAEdD,EAAgBzjC,SAAUyjC,EAAgB,GAAG9kC,MAAM22B,QAA6B,EAAnB8N,EAAuBA,EAAmB,GACvGM,EAAe1jC,SAAU0jC,EAAe,GAAG/kC,MAAM22B,QAAgC,GAApB8N,GAAyBA,EAAmB,KAK7Gt2B,EAAQI,eAAiBJ,EAAQO,yBAC1BgE,EAAW,GAAG1S,MACpBglC,kBAAoBV,EAAS,WAGpC9sB,cAAe,SAAuBhT,GACvBzF,KACNoU,OACJ5O,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,KAgDdygC,GAAS,CACX3c,KAAM,WACJ,IAAIrW,EAASlT,KAETmmC,EADMjzB,EAAOlF,OACMo4B,OACnB71B,EAAc2C,EAAOzE,YACrB03B,EAAajzB,kBAAkB3C,GACjC2C,EAAOkzB,OAAOlzB,OAASizB,EAAajzB,OACpC7G,EAAMqC,OAAOwE,EAAOkzB,OAAOlzB,OAAOsV,eAAgB,CAChDtQ,qBAAqB,EACrBqD,qBAAqB,IAEvBlP,EAAMqC,OAAOwE,EAAOkzB,OAAOlzB,OAAOlF,OAAQ,CACxCkK,qBAAqB,EACrBqD,qBAAqB,KAEdlP,EAAMkC,SAAS43B,EAAajzB,UACrCA,EAAOkzB,OAAOlzB,OAAS,IAAI3C,EAAYlE,EAAMqC,OAAO,GAAIy3B,EAAajzB,OAAQ,CAC3EiF,uBAAuB,EACvBD,qBAAqB,EACrBqD,qBAAqB,KAEvBrI,EAAOkzB,OAAOC,eAAgB,GAEhCnzB,EAAOkzB,OAAOlzB,OAAOC,IAAIxP,SAASuP,EAAOlF,OAAOo4B,OAAOE,sBACvDpzB,EAAOkzB,OAAOlzB,OAAOtN,GAAG,MAAOsN,EAAOkzB,OAAOG,eAE/CA,aAAc,WACZ,IAAIrzB,EAASlT,KACTwmC,EAAetzB,EAAOkzB,OAAOlzB,OACjC,GAAKszB,EAAL,CACA,IAAIlrB,EAAekrB,EAAalrB,aAChC,GAAI,MAAOA,EAAX,CACA,IAAI2C,EAMJ,GAJEA,EADEuoB,EAAax4B,OAAOoM,KACP5G,SAASjR,EAAEikC,EAAanrB,cAAc9W,KAAK,2BAA4B,IAEvE+W,EAEbpI,EAAOlF,OAAOoM,KAAM,CACtB,IAAIqsB,EAAevzB,EAAOwF,YACtBxF,EAAOkB,OAAO9J,GAAGm8B,GAActiC,SAAS+O,EAAOlF,OAAOqM,uBACxDnH,EAAOkK,UAEPlK,EAAOmK,YAAcnK,EAAOS,WAAW,GAAGzK,WAC1Cu9B,EAAevzB,EAAOwF,aAExB,IAAI+E,EAAYvK,EAAOkB,OAAO9J,GAAGm8B,GAAcn7B,QAAS,6BAAgC2S,EAAe,MAAQ3T,GAAG,GAAGH,QACjH4E,EAAYmE,EAAOkB,OAAO9J,GAAGm8B,GAAcv7B,QAAS,6BAAgC+S,EAAe,MAAQ3T,GAAG,GAAGH,QAC7E8T,OAAf,IAAdR,EAA4C1O,OACzB,IAAdA,EAA4C0O,EACnD1O,EAAY03B,EAAeA,EAAehpB,EAA4B1O,EACzD0O,EAExBvK,EAAOoJ,QAAQ2B,MAEjBnL,OAAQ,SAAgB4zB,GACtB,IAAIxzB,EAASlT,KACTwmC,EAAetzB,EAAOkzB,OAAOlzB,OACjC,GAAKszB,EAAL,CAEA,IAAI3wB,EAAsD,SAAtC2wB,EAAax4B,OAAO6H,cACpC2wB,EAAaxoB,uBACbwoB,EAAax4B,OAAO6H,cAExB,GAAI3C,EAAOgH,YAAcssB,EAAatsB,UAAW,CAC/C,IACIysB,EADAC,EAAqBJ,EAAa9tB,YAEtC,GAAI8tB,EAAax4B,OAAOoM,KAAM,CACxBosB,EAAapyB,OAAO9J,GAAGs8B,GAAoBziC,SAASqiC,EAAax4B,OAAOqM,uBAC1EmsB,EAAappB,UAEbopB,EAAanpB,YAAcmpB,EAAa7yB,WAAW,GAAGzK,WACtD09B,EAAqBJ,EAAa9tB,aAGpC,IAAImuB,EAAkBL,EAAapyB,OAAO9J,GAAGs8B,GAAoBt7B,QAAS,6BAAiC4H,EAAgB,UAAI,MAAQ5I,GAAG,GAAGH,QACzI28B,EAAkBN,EAAapyB,OAAO9J,GAAGs8B,GAAoB17B,QAAS,6BAAiCgI,EAAgB,UAAI,MAAQ5I,GAAG,GAAGH,QAC/Fw8B,OAAf,IAApBE,EAAoDC,OAC3B,IAApBA,EAAoDD,EAC3DC,EAAkBF,EAAqBA,EAAqBC,EAAoCC,EACjFD,OAExBF,EAAiBzzB,EAAOgH,UAGtBssB,EAAattB,qBAAqBpW,QAAQ6jC,GAAkB,IAC1DH,EAAax4B,OAAOiJ,eAEpB0vB,EADmBC,EAAjBD,EACeA,EAAiBjxB,KAAKC,MAAME,EAAgB,GAAK,EAEjD8wB,EAAiBjxB,KAAKC,MAAME,EAAgB,GAAK,EAE1C+wB,EAAjBD,IACTA,EAAiBA,EAAiB9wB,EAAgB,GAEpD2wB,EAAalqB,QAAQqqB,EAAgBD,EAAU,OAAI3/B,IAKvD,IAAIggC,EAAmB,EACnBC,EAAmB9zB,EAAOlF,OAAOo4B,OAAOa,sBAO5C,GALkC,EAA9B/zB,EAAOlF,OAAO6H,gBAAsB3C,EAAOlF,OAAOiJ,iBACpD8vB,EAAmB7zB,EAAOlF,OAAO6H,eAGnC2wB,EAAapyB,OAAOnQ,YAAY+iC,GAC5BR,EAAax4B,OAAOoM,KACtB,IAAK,IAAI/X,EAAI,EAAGA,EAAI0kC,EAAkB1kC,GAAK,EACzCmkC,EAAa7yB,WAAW5S,SAAU,8BAAiCmS,EAAOgH,UAAY7X,GAAK,MAAQsB,SAASqjC,QAG9G,IAAK,IAAI1vB,EAAM,EAAGA,EAAMyvB,EAAkBzvB,GAAO,EAC/CkvB,EAAapyB,OAAO9J,GAAG4I,EAAOgH,UAAY5C,GAAK3T,SAASqjC,MAyE5Dp2B,GAAa,CACfic,EACAC,EACAE,EACAE,EACAqB,EACA4B,EACAuB,EAtmGiB,CACjBnf,KAAM,aACNvE,OAAQ,CACNolB,WAAY,CACVlf,SAAS,EACTmf,gBAAgB,EAChBI,QAAQ,EACRD,aAAa,EACbE,YAAa,EACbM,aAAc,cAGlB7hB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBkgB,WAAY,CACVlf,SAAS,EACTsd,OAAQG,EAAWH,OAAOvf,KAAKiB,GAC/Bue,QAASE,EAAWF,QAAQxf,KAAKiB,GACjCqd,OAAQoB,EAAWpB,OAAOte,KAAKiB,GAC/B+f,iBAAkBtB,EAAWsB,iBAAiBhhB,KAAKiB,GACnDigB,iBAAkBxB,EAAWwB,iBAAiBlhB,KAAKiB,GACnD0e,eAAgBvlB,EAAMM,UAI5B/G,GAAI,CACF2jB,KAAM,WACSvpB,KACFgO,OAAOolB,WAAWlf,SADhBlU,KACkCozB,WAAW5B,UAE5DhF,QAAS,WACMxsB,KACFozB,WAAWlf,SADTlU,KAC2BozB,WAAW3B,aAqGtC,CACjBlf,KAAM,aACNvE,OAAQ,CACNmc,WAAY,CACVmK,OAAQ,KACRC,OAAQ,KAER2S,aAAa,EACb9S,cAAe,yBACf+C,YAAa,uBACb9C,UAAW,uBAGfliB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBmqB,WAAY,CACVZ,KAAM0K,EAAW1K,KAAKtX,KAHbjS,MAIT8S,OAAQmhB,EAAWnhB,OAAOb,KAJjBjS,MAKTwsB,QAASyH,EAAWzH,QAAQva,KALnBjS,UASf4F,GAAI,CACF2jB,KAAM,WACSvpB,KACNmqB,WAAWZ,OADLvpB,KAENmqB,WAAWrX,UAEpBq0B,OAAQ,WACOnnC,KACNmqB,WAAWrX,UAEpBs0B,SAAU,WACKpnC,KACNmqB,WAAWrX,UAEpB0Z,QAAS,WACMxsB,KACNmqB,WAAWqC,WAEpB+T,MAAO,SAAel6B,GACpB,IACIqoB,EADS1uB,KACImqB,WACb+J,EAAUxF,EAAIwF,QACdC,EAAUzF,EAAIyF,SAHLn0B,KAKJgO,OAAOmc,WAAW+c,aACrB3kC,EAAE8D,EAAEC,QAAQI,GAAGytB,IACf5xB,EAAE8D,EAAEC,QAAQI,GAAGwtB,KAEfA,GAAWA,EAAQ7vB,YATZrE,KAS+BgO,OAAOmc,WAAWgN,aACxDhD,GAAWA,EAAQ9vB,YAVZrE,KAU+BgO,OAAOmc,WAAWgN,iBAkPjD,CACjB5kB,KAAM,aACNvE,OAAQ,CACNymB,WAAY,CACVvvB,GAAI,KACJmiC,cAAe,OACftQ,WAAW,EACXmQ,aAAa,EACb1Q,aAAc,KACdK,kBAAmB,KACnBH,eAAgB,KAChBN,aAAc,KACdJ,qBAAqB,EACrB7T,KAAM,UACN8S,gBAAgB,EAChBE,mBAAoB,EACpBU,sBAAuB,SAAUyR,GAAU,OAAOA,GAClDxR,oBAAqB,SAAUwR,GAAU,OAAOA,GAChD7Q,YAAa,2BACbjB,kBAAmB,kCACnByB,cAAe,qBACfN,aAAc,4BACdC,WAAY,0BACZO,YAAa,2BACbL,qBAAsB,qCACtBI,yBAA0B,yCAC1BF,eAAgB,8BAChB3C,UAAW,2BAGfliB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBuhB,WAAY,CACVlL,KAAMiL,EAAWjL,KAAKtX,KAAKiB,GAC3BmjB,OAAQ7B,EAAW6B,OAAOpkB,KAAKiB,GAC/BJ,OAAQ0hB,EAAW1hB,OAAOb,KAAKiB,GAC/BsZ,QAASgI,EAAWhI,QAAQva,KAAKiB,GACjCkiB,mBAAoB,MAI1BxvB,GAAI,CACF2jB,KAAM,WACSvpB,KACNy0B,WAAWlL,OADLvpB,KAENy0B,WAAW4B,SAFLr2B,KAGNy0B,WAAW3hB,UAEpBy0B,kBAAmB,WACJvnC,KACFgO,OAAOoM,KADLpa,KAEJy0B,WAAW3hB,cACmB,IAH1B9S,KAGY+X,WAHZ/X,KAIJy0B,WAAW3hB,UAGtB00B,gBAAiB,WACFxnC,KACDgO,OAAOoM,MADNpa,KAEJy0B,WAAW3hB,UAGtB20B,mBAAoB,WACLznC,KACFgO,OAAOoM,OADLpa,KAEJy0B,WAAW4B,SAFPr2B,KAGJy0B,WAAW3hB,WAGtB40B,qBAAsB,WACP1nC,KACDgO,OAAOoM,OADNpa,KAEJy0B,WAAW4B,SAFPr2B,KAGJy0B,WAAW3hB,WAGtB0Z,QAAS,WACMxsB,KACNy0B,WAAWjI,WAEpB+T,MAAO,SAAel6B,GACpB,IAAI6M,EAASlT,KAEXkT,EAAOlF,OAAOymB,WAAWvvB,IACtBgO,EAAOlF,OAAOymB,WAAWyS,aACM,EAA/Bh0B,EAAOuhB,WAAWthB,IAAI7Q,SACrBC,EAAE8D,EAAEC,QAAQnC,SAAS+O,EAAOlF,OAAOymB,WAAWgC,cAElDvjB,EAAOuhB,WAAWthB,IAAI9O,YAAY6O,EAAOlF,OAAOymB,WAAW0C,gBA8RjD,CAChB5kB,KAAM,YACNvE,OAAQ,CACNqpB,UAAW,CACTnyB,GAAI,KACJoyB,SAAU,OACVK,MAAM,EACNoB,WAAW,EACXN,eAAe,EACfpE,UAAW,wBACXsT,UAAW,0BAGfx1B,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBmkB,UAAW,CACT9N,KAAM6N,EAAU7N,KAAKtX,KAAKiB,GAC1BsZ,QAAS4K,EAAU5K,QAAQva,KAAKiB,GAChCH,WAAYqkB,EAAUrkB,WAAWd,KAAKiB,GACtCwI,aAAc0b,EAAU1b,aAAazJ,KAAKiB,GAC1CuF,cAAe2e,EAAU3e,cAAcxG,KAAKiB,GAC5CwlB,gBAAiBtB,EAAUsB,gBAAgBzmB,KAAKiB,GAChD0lB,iBAAkBxB,EAAUwB,iBAAiB3mB,KAAKiB,GAClD+kB,gBAAiBb,EAAUa,gBAAgBhmB,KAAKiB,GAChDmlB,YAAajB,EAAUiB,YAAYpmB,KAAKiB,GACxCqlB,WAAYnB,EAAUmB,WAAWtmB,KAAKiB,GACtCslB,UAAWpB,EAAUoB,UAAUvmB,KAAKiB,GACpCoP,WAAW,EACXqR,QAAS,KACT2E,YAAa,SAInB1yB,GAAI,CACF2jB,KAAM,WACSvpB,KACNq3B,UAAU9N,OADJvpB,KAENq3B,UAAUtkB,aAFJ/S,KAGNq3B,UAAU3b,gBAEnB5I,OAAQ,WACO9S,KACNq3B,UAAUtkB,cAEnBoa,OAAQ,WACOntB,KACNq3B,UAAUtkB,cAEnB8a,eAAgB,WACD7tB,KACNq3B,UAAUtkB,cAEnB2I,aAAc,WACC1b,KACNq3B,UAAU3b,gBAEnBjD,cAAe,SAAuBhT,GACvBzF,KACNq3B,UAAU5e,cAAchT,IAEjC+mB,QAAS,WACMxsB,KACNq3B,UAAU7K,aAyFN,CACfja,KAAM,WACNvE,OAAQ,CACNqrB,SAAU,CACRnlB,SAAS,IAGb/B,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBq5B,SAAU,CACRJ,aAAcD,EAASC,aAAahnB,KAH3BjS,MAIT0b,aAAcsd,EAAStd,aAAazJ,KAJ3BjS,MAKTyY,cAAeugB,EAASvgB,cAAcxG,KAL7BjS,UASf4F,GAAI,CACFwqB,WAAY,WACGpwB,KACDgO,OAAOqrB,SAASnlB,UADflU,KAENgO,OAAOkK,qBAAsB,EAFvBlY,KAGNwoB,eAAetQ,qBAAsB,IAE9CqR,KAAM,WACSvpB,KACDgO,OAAOqrB,UADNr5B,KAENq5B,SAAS3d,gBAElBA,aAAc,WACC1b,KACDgO,OAAOqrB,UADNr5B,KAENq5B,SAAS3d,gBAElBjD,cAAe,SAAuBhT,GACvBzF,KACDgO,OAAOqrB,UADNr5B,KAENq5B,SAAS5gB,cAAchT,MAuavB,CACX8M,KAAM,OACNvE,OAAQ,CACNisB,KAAM,CACJ/lB,SAAS,EACTsmB,SAAU,EACVI,SAAU,EACVt2B,QAAQ,EACRsjC,eAAgB,wBAChBC,iBAAkB,wBAGtB11B,OAAQ,WACN,IAAIe,EAASlT,KACTi6B,EAAO,CACT/lB,SAAS,EACT+hB,MAAO,EACPmD,aAAc,EACdqB,WAAW,EACXP,QAAS,CACPlK,cAAUjpB,EACVi0B,gBAAYj0B,EACZk0B,iBAAal0B,EACbuzB,cAAUvzB,EACVwzB,kBAAcxzB,EACdyzB,SAAU,GAEZxP,MAAO,CACL1I,eAAWvb,EACXwb,aAASxb,EACT6b,cAAU7b,EACVgc,cAAUhc,EACVq0B,UAAMr0B,EACNu0B,UAAMv0B,EACNs0B,UAAMt0B,EACNw0B,UAAMx0B,EACNiM,WAAOjM,EACPkM,YAAQlM,EACRkc,YAAQlc,EACRmc,YAAQnc,EACRg0B,aAAc,GACdS,eAAgB,IAElBpV,SAAU,CACRxK,OAAG7U,EACH8U,OAAG9U,EACH00B,mBAAe10B,EACf20B,mBAAe30B,EACf40B,cAAU50B,IAGd,+HAAiI5D,MAAM,KAAK+I,QAAQ,SAAUC,GAC5J8tB,EAAK9tB,GAAcutB,EAAKvtB,GAAY8F,KAAKiB,KAE3C7G,EAAMqC,OAAOwE,EAAQ,CACnB+mB,KAAMA,KAGVr0B,GAAI,CACF2jB,KAAM,WACSvpB,KACFgO,OAAOisB,KAAK/lB,SADVlU,KAEJi6B,KAAKzI,UAGhBhF,QAAS,WACMxsB,KACNi6B,KAAKxI,WAEdqW,WAAY,SAAoBzhC,GACjBrG,KACDi6B,KAAK/lB,SADJlU,KAENi6B,KAAKnY,aAAazb,IAE3B0hC,SAAU,SAAkB1hC,GACbrG,KACDi6B,KAAK/lB,SADJlU,KAENi6B,KAAKxU,WAAWpf,IAEzB2hC,UAAW,SAAmB3hC,GACfrG,KACFgO,OAAOisB,KAAK/lB,SADVlU,KAC4Bi6B,KAAK/lB,SADjClU,KACmDgO,OAAOisB,KAAK31B,QAD/DtE,KAEJi6B,KAAK31B,OAAO+B,IAGvB6B,cAAe,WACAlI,KACFi6B,KAAK/lB,SADHlU,KACqBgO,OAAOisB,KAAK/lB,SADjClU,KAEJi6B,KAAKiC,qBA4IP,CACX3pB,KAAM,OACNvE,OAAQ,CACNyhB,KAAM,CACJvb,SAAS,EACT4pB,cAAc,EACdC,mBAAoB,EACpBkK,uBAAuB,EAEvB9K,aAAc,cACdE,aAAc,sBACdD,YAAa,qBACb8K,eAAgB,0BAGpB/1B,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnByvB,KAAM,CACJmO,oBAAoB,EACpBlO,KAAMqN,EAAKrN,KAAKzd,KAJPjS,MAKTg9B,YAAaD,EAAKC,YAAY/qB,KALrBjS,UASf4F,GAAI,CACFwqB,WAAY,WACGpwB,KACFgO,OAAOyhB,KAAKvb,SADVlU,KAC4BgO,OAAOyb,gBADnCzpB,KAEJgO,OAAOyb,eAAgB,IAGlCF,KAAM,WACSvpB,KACFgO,OAAOyhB,KAAKvb,UADVlU,KAC6BgO,OAAOoM,MAAuC,IAD3Epa,KACmDgO,OAAO0O,cAD1D1c,KAEJyvB,KAAKC,QAGhByY,OAAQ,WACOnoC,KACFgO,OAAOwT,WADLxhB,KACyBgO,OAAOiZ,gBADhCjnB,KAEJyvB,KAAKC,QAGhBvC,OAAQ,WACOntB,KACFgO,OAAOyhB,KAAKvb,SADVlU,KAEJyvB,KAAKC,QAGhB0Y,kBAAmB,WACJpoC,KACFgO,OAAOyhB,KAAKvb,SADVlU,KAEJyvB,KAAKC,QAGhB1T,gBAAiB,WACf,IAAI9I,EAASlT,KACTkT,EAAOlF,OAAOyhB,KAAKvb,UACjBhB,EAAOlF,OAAOyhB,KAAKwY,wBAA2B/0B,EAAOlF,OAAOyhB,KAAKwY,wBAA0B/0B,EAAOuc,KAAKmO,qBACzG1qB,EAAOuc,KAAKC,QAIlBxnB,cAAe,WACAlI,KACFgO,OAAOyhB,KAAKvb,UADVlU,KAC6BgO,OAAOyhB,KAAKwY,uBADzCjoC,KAEJyvB,KAAKC,UAqID,CACjBnd,KAAM,aACNvE,OAAQ,CACN6wB,WAAY,CACVM,aAASp4B,EACTu4B,SAAS,EACTD,GAAI,UAGRltB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnB2rB,WAAY,CACVM,QAASjsB,EAAOlF,OAAO6wB,WAAWM,QAClCR,uBAAwBR,EAAWQ,uBAAuB1sB,KAAKiB,GAC/DwI,aAAcyiB,EAAWziB,aAAazJ,KAAKiB,GAC3CuF,cAAe0lB,EAAW1lB,cAAcxG,KAAKiB,OAInDtN,GAAI,CACFkN,OAAQ,WACO9S,KACD6+B,WAAWM,SADVn/B,KAEF6+B,WAAWC,SAFT9+B,KAGJ6+B,WAAWC,YAAS/3B,SAHhB/G,KAIG6+B,WAAWC,SAG7B3R,OAAQ,WACOntB,KACD6+B,WAAWM,SADVn/B,KAEF6+B,WAAWC,SAFT9+B,KAGJ6+B,WAAWC,YAAS/3B,SAHhB/G,KAIG6+B,WAAWC,SAG7BjR,eAAgB,WACD7tB,KACD6+B,WAAWM,SADVn/B,KAEF6+B,WAAWC,SAFT9+B,KAGJ6+B,WAAWC,YAAS/3B,SAHhB/G,KAIG6+B,WAAWC,SAG7BpjB,aAAc,SAAsB3C,EAAW4C,GAChC3b,KACD6+B,WAAWM,SADVn/B,KAEN6+B,WAAWnjB,aAAa3C,EAAW4C,IAE5ClD,cAAe,SAAuBhT,EAAUkW,GACjC3b,KACD6+B,WAAWM,SADVn/B,KAEN6+B,WAAWpmB,cAAchT,EAAUkW,MA2JrC,CACTpJ,KAAM,OACNvE,OAAQ,CACNwxB,KAAM,CACJtrB,SAAS,EACTm0B,kBAAmB,sBACnB/H,iBAAkB,iBAClBF,iBAAkB,aAClBC,kBAAmB,0BACnBF,iBAAkB,yBAClBY,wBAAyB,0BAG7B5uB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBssB,KAAM,CACJkB,WAAYn+B,EAAG,gBAAoB2Q,EAAOlF,OAAOwxB,KAAsB,kBAAI,yDAG/ExzB,OAAOC,KAAKuzB,GAAMtzB,QAAQ,SAAUC,GAClC+G,EAAOssB,KAAKrzB,GAAcqzB,EAAKrzB,GAAY8F,KAAKiB,MAGpDtN,GAAI,CACF2jB,KAAM,WACSvpB,KACDgO,OAAOwxB,KAAKtrB,UADXlU,KAENw/B,KAAKjW,OAFCvpB,KAGNw/B,KAAKmB,qBAEdwG,OAAQ,WACOnnC,KACDgO,OAAOwxB,KAAKtrB,SADXlU,KAENw/B,KAAKmB,oBAEdyG,SAAU,WACKpnC,KACDgO,OAAOwxB,KAAKtrB,SADXlU,KAENw/B,KAAKmB,oBAEd2H,iBAAkB,WACHtoC,KACDgO,OAAOwxB,KAAKtrB,SADXlU,KAENw/B,KAAKoB,oBAEdpU,QAAS,WACMxsB,KACDgO,OAAOwxB,KAAKtrB,SADXlU,KAENw/B,KAAKhT,aAoFF,CACdja,KAAM,UACNvE,OAAQ,CACNtM,QAAS,CACPwS,SAAS,EACTotB,cAAc,EACdr8B,IAAK,WAGTkN,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBxR,QAAS,CACP6nB,KAAMyX,EAAQzX,KAAKtX,KAAKiB,GACxByuB,WAAYX,EAAQW,WAAW1vB,KAAKiB,GACpCquB,mBAAoBP,EAAQO,mBAAmBtvB,KAAKiB,GACpDmuB,cAAeL,EAAQK,cAAcpvB,KAAKiB,GAC1CsZ,QAASwU,EAAQxU,QAAQva,KAAKiB,OAIpCtN,GAAI,CACF2jB,KAAM,WACSvpB,KACFgO,OAAOtM,QAAQwS,SADblU,KAEJ0B,QAAQ6nB,QAGnBiD,QAAS,WACMxsB,KACFgO,OAAOtM,QAAQwS,SADblU,KAEJ0B,QAAQ8qB,WAGnBtkB,cAAe,WACAlI,KACF0B,QAAQib,aADN3c,KAEJ0B,QAAQigC,WAFJ3hC,KAEsBgO,OAAOtM,QAAQuD,IAFrCjF,KAEiD0Y,gBAuD7C,CACrBnG,KAAM,kBACNvE,OAAQ,CACNkzB,eAAgB,CACdhtB,SAAS,EACTotB,cAAc,EACdc,YAAY,IAGhBjwB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnBguB,eAAgB,CACdvkB,aAAa,EACb4M,KAAMyY,EAAezY,KAAKtX,KAAKiB,GAC/BsZ,QAASwV,EAAexV,QAAQva,KAAKiB,GACrCivB,QAASH,EAAeG,QAAQlwB,KAAKiB,GACrC+uB,YAAaD,EAAeC,YAAYhwB,KAAKiB,OAInDtN,GAAI,CACF2jB,KAAM,WACSvpB,KACFgO,OAAOkzB,eAAehtB,SADpBlU,KAEJkhC,eAAe3X,QAG1BiD,QAAS,WACMxsB,KACFgO,OAAOkzB,eAAehtB,SADpBlU,KAEJkhC,eAAe1U,WAG1BtkB,cAAe,WACAlI,KACFkhC,eAAevkB,aADb3c,KAEJkhC,eAAeiB,aAoFb,CACf5vB,KAAM,WACNvE,OAAQ,CACN4lB,SAAU,CACR1f,SAAS,EACTxH,MAAO,IACPm2B,mBAAmB,EACnB0F,sBAAsB,EACtB9F,iBAAiB,EACjBD,kBAAkB,IAGtBrwB,OAAQ,WACN,IAAIe,EAASlT,KACbqM,EAAMqC,OAAOwE,EAAQ,CACnB0gB,SAAU,CACR8O,SAAS,EACTE,QAAQ,EACRN,IAAKD,EAASC,IAAIrwB,KAAKiB,GACvB4U,MAAOua,EAASva,MAAM7V,KAAKiB,GAC3B4gB,KAAMuO,EAASvO,KAAK7hB,KAAKiB,GACzByvB,MAAON,EAASM,MAAM1wB,KAAKiB,GAC3BgpB,gBAAiB,SAAyB71B,GACnC6M,IAAUA,EAAO6J,WAAc7J,EAAOS,YACvCtN,EAAEC,SAAWtG,OACjBkT,EAAOS,WAAW,GAAGtT,oBAAoB,gBAAiB6S,EAAO0gB,SAASsI,iBAC1EhpB,EAAOS,WAAW,GAAGtT,oBAAoB,sBAAuB6S,EAAO0gB,SAASsI,iBAChFhpB,EAAO0gB,SAASgP,QAAS,EACpB1vB,EAAO0gB,SAAS8O,QAGnBxvB,EAAO0gB,SAAS0O,MAFhBpvB,EAAO0gB,SAASE,aAQ1BluB,GAAI,CACF2jB,KAAM,WACSvpB,KACFgO,OAAO4lB,SAAS1f,SADdlU,KAEJ4zB,SAAS9L,SAGpB0gB,sBAAuB,SAA+BlwB,EAAOiE,GAC9Cvc,KACF4zB,SAAS8O,UACdnmB,IAFOvc,KAEagO,OAAO4lB,SAAS2U,qBAF7BvoC,KAGF4zB,SAAS+O,MAAMrqB,GAHbtY,KAKF4zB,SAASE,SAItB2U,gBAAiB,WACFzoC,KACF4zB,SAAS8O,UADP1iC,KAEAgO,OAAO4lB,SAAS2U,qBAFhBvoC,KAGF4zB,SAASE,OAHP9zB,KAKF4zB,SAAS+O,UAItBnW,QAAS,WACMxsB,KACF4zB,SAAS8O,SADP1iC,KAEJ4zB,SAASE,UAmDP,CACfvhB,KAAM,cACNvE,OAAQ,CACNk1B,WAAY,CACVC,WAAW,IAGfhxB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBkjC,WAAY,CACVxnB,aAAconB,EAAKpnB,aAAazJ,KAHvBjS,MAITyY,cAAeqqB,EAAKrqB,cAAcxG,KAJzBjS,UAQf4F,GAAI,CACFwqB,WAAY,WACV,IAAIld,EAASlT,KACb,GAA6B,SAAzBkT,EAAOlF,OAAOoJ,OAAlB,CACAlE,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,QACjE,IAAIqiB,EAAkB,CACpBxa,cAAe,EACfJ,gBAAiB,EACjB0B,eAAgB,EAChBe,qBAAqB,EACrBnD,aAAc,EACdyG,kBAAkB,GAEpBnP,EAAMqC,OAAOwE,EAAOlF,OAAQqiB,GAC5BhkB,EAAMqC,OAAOwE,EAAOsV,eAAgB6H,KAEtC3U,aAAc,WAEiB,SADhB1b,KACFgO,OAAOoJ,QADLpX,KAENkjC,WAAWxnB,gBAEpBjD,cAAe,SAAuBhT,GAEP,SADhBzF,KACFgO,OAAOoJ,QADLpX,KAENkjC,WAAWzqB,cAAchT,MAwIrB,CACf8M,KAAM,cACNvE,OAAQ,CACN01B,WAAY,CACVM,cAAc,EACdJ,QAAQ,EACRW,aAAc,GACdK,YAAa,MAGjBzyB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnB0jC,WAAY,CACVhoB,aAAc4nB,EAAK5nB,aAAazJ,KAHvBjS,MAITyY,cAAe6qB,EAAK7qB,cAAcxG,KAJzBjS,UAQf4F,GAAI,CACFwqB,WAAY,WACV,IAAIld,EAASlT,KACb,GAA6B,SAAzBkT,EAAOlF,OAAOoJ,OAAlB,CACAlE,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,QACjEkF,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,MACjE,IAAIqiB,EAAkB,CACpBxa,cAAe,EACfJ,gBAAiB,EACjB0B,eAAgB,EAChBe,qBAAqB,EACrBiN,gBAAiB,EACjBpQ,aAAc,EACdkC,gBAAgB,EAChBuE,kBAAkB,GAEpBnP,EAAMqC,OAAOwE,EAAOlF,OAAQqiB,GAC5BhkB,EAAMqC,OAAOwE,EAAOsV,eAAgB6H,KAEtC3U,aAAc,WAEiB,SADhB1b,KACFgO,OAAOoJ,QADLpX,KAEN0jC,WAAWhoB,gBAEpBjD,cAAe,SAAuBhT,GAEP,SADhBzF,KACFgO,OAAOoJ,QADLpX,KAEN0jC,WAAWjrB,cAAchT,MA+ErB,CACf8M,KAAM,cACNvE,OAAQ,CACNg3B,WAAY,CACVhB,cAAc,EACdiB,eAAe,IAGnB9yB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBglC,WAAY,CACVtpB,aAAcqpB,GAAKrpB,aAAazJ,KAHvBjS,MAITyY,cAAessB,GAAKtsB,cAAcxG,KAJzBjS,UAQf4F,GAAI,CACFwqB,WAAY,WACV,IAAIld,EAASlT,KACb,GAA6B,SAAzBkT,EAAOlF,OAAOoJ,OAAlB,CACAlE,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,QACjEkF,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,MACjE,IAAIqiB,EAAkB,CACpBxa,cAAe,EACfJ,gBAAiB,EACjB0B,eAAgB,EAChBe,qBAAqB,EACrBnD,aAAc,EACdyG,kBAAkB,GAEpBnP,EAAMqC,OAAOwE,EAAOlF,OAAQqiB,GAC5BhkB,EAAMqC,OAAOwE,EAAOsV,eAAgB6H,KAEtC3U,aAAc,WAEiB,SADhB1b,KACFgO,OAAOoJ,QADLpX,KAENglC,WAAWtpB,gBAEpBjD,cAAe,SAAuBhT,GAEP,SADhBzF,KACFgO,OAAOoJ,QADLpX,KAENglC,WAAWvsB,cAAchT,MA6EhB,CACpB8M,KAAM,mBACNvE,OAAQ,CACNs3B,gBAAiB,CACfE,OAAQ,GACRK,QAAS,EACTJ,MAAO,IACPE,SAAU,EACV3B,cAAc,IAGlB7xB,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBslC,gBAAiB,CACf5pB,aAAc2pB,GAAU3pB,aAAazJ,KAH5BjS,MAITyY,cAAe4sB,GAAU5sB,cAAcxG,KAJ9BjS,UAQf4F,GAAI,CACFwqB,WAAY,WACV,IAAIld,EAASlT,KACgB,cAAzBkT,EAAOlF,OAAOoJ,SAElBlE,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,aACjEkF,EAAOmX,WAAWpnB,KAAOiQ,EAAOlF,OAA6B,uBAAI,MAEjEkF,EAAOlF,OAAOkK,qBAAsB,EACpChF,EAAOsV,eAAetQ,qBAAsB,IAE9CwD,aAAc,WAEiB,cADhB1b,KACFgO,OAAOoJ,QADLpX,KAENslC,gBAAgB5pB,gBAEzBjD,cAAe,SAAuBhT,GAEP,cADhBzF,KACFgO,OAAOoJ,QADLpX,KAENslC,gBAAgB7sB,cAAchT,MA6H5B,CACb8M,KAAM,SACNvE,OAAQ,CACNo4B,OAAQ,CACNlzB,OAAQ,KACR+zB,sBAAuB,4BACvBX,qBAAsB,4BAG1Bn0B,OAAQ,WAEN9F,EAAMqC,OADO1O,KACQ,CACnBomC,OAAQ,CACNlzB,OAAQ,KACRqW,KAAM2c,GAAO3c,KAAKtX,KAJTjS,MAKT8S,OAAQozB,GAAOpzB,OAAOb,KALbjS,MAMTumC,aAAcL,GAAOK,aAAat0B,KANzBjS,UAUf4F,GAAI,CACFwqB,WAAY,WACV,IAEIgW,EAFSpmC,KACIgO,OACAo4B,OACZA,GAAWA,EAAOlzB,SAHVlT,KAINomC,OAAO7c,OAJDvpB,KAKNomC,OAAOtzB,QAAO,KAEvB41B,YAAa,WACE1oC,KACDomC,OAAOlzB,QADNlT,KAENomC,OAAOtzB,UAEhBA,OAAQ,WACO9S,KACDomC,OAAOlzB,QADNlT,KAENomC,OAAOtzB,UAEhBqa,OAAQ,WACOntB,KACDomC,OAAOlzB,QADNlT,KAENomC,OAAOtzB,UAEhB+a,eAAgB,WACD7tB,KACDomC,OAAOlzB,QADNlT,KAENomC,OAAOtzB,UAEhB2F,cAAe,SAAuBhT,GACpC,IACI+gC,EADSxmC,KACaomC,OAAOlzB,OAC5BszB,GACLA,EAAa/tB,cAAchT,IAE7BkjC,cAAe,WACb,IACInC,EADSxmC,KACaomC,OAAOlzB,OAC5BszB,GAFQxmC,KAGFomC,OAAOC,eAAiBG,GACjCA,EAAaha,cA0CrB,YAP0B,IAAfzsB,EAAOsS,MAChBtS,EAAOsS,IAAMtS,EAAO0D,MAAM4O,IAC1BtS,EAAOuS,cAAgBvS,EAAO0D,MAAM6O,eAGtCvS,EAAOsS,IAAIzB,IAEJ7Q", "file": "swiper.min.js", "sourcesContent": ["/**\n * Swiper 4.4.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * http://www.idangero.us/swiper/\n *\n * Copyright 2014-2018 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: September 14, 2018\n */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global.Swiper = factory());\n}(this, (function () { 'use strict';\n\n  /**\n   * SSR Window 1.0.1\n   * Better handling for window object in SSR environment\n   * https://github.com/nolimits4web/ssr-window\n   *\n   * Copyright 2018, <PERSON>\n   *\n   * Licensed under MIT\n   *\n   * Released on: July 18, 2018\n   */\n  var doc = (typeof document === 'undefined') ? {\n    body: {},\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    activeElement: {\n      blur: function blur() {},\n      nodeName: '',\n    },\n    querySelector: function querySelector() {\n      return null;\n    },\n    querySelectorAll: function querySelectorAll() {\n      return [];\n    },\n    getElementById: function getElementById() {\n      return null;\n    },\n    createEvent: function createEvent() {\n      return {\n        initEvent: function initEvent() {},\n      };\n    },\n    createElement: function createElement() {\n      return {\n        children: [],\n        childNodes: [],\n        style: {},\n        setAttribute: function setAttribute() {},\n        getElementsByTagName: function getElementsByTagName() {\n          return [];\n        },\n      };\n    },\n    location: { hash: '' },\n  } : document; // eslint-disable-line\n\n  var win = (typeof window === 'undefined') ? {\n    document: doc,\n    navigator: {\n      userAgent: '',\n    },\n    location: {},\n    history: {},\n    CustomEvent: function CustomEvent() {\n      return this;\n    },\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    getComputedStyle: function getComputedStyle() {\n      return {\n        getPropertyValue: function getPropertyValue() {\n          return '';\n        },\n      };\n    },\n    Image: function Image() {},\n    Date: function Date() {},\n    screen: {},\n    setTimeout: function setTimeout() {},\n    clearTimeout: function clearTimeout() {},\n  } : window; // eslint-disable-line\n\n  /**\n   * Dom7 2.1.2\n   * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API\n   * http://framework7.io/docs/dom.html\n   *\n   * Copyright 2018, Vladimir Kharlampidi\n   * The iDangero.us\n   * http://www.idangero.us/\n   *\n   * Licensed under MIT\n   *\n   * Released on: September 13, 2018\n   */\n\n  var Dom7 = function Dom7(arr) {\n    var self = this;\n    // Create array-like object\n    for (var i = 0; i < arr.length; i += 1) {\n      self[i] = arr[i];\n    }\n    self.length = arr.length;\n    // Return collection with methods\n    return this;\n  };\n\n  function $(selector, context) {\n    var arr = [];\n    var i = 0;\n    if (selector && !context) {\n      if (selector instanceof Dom7) {\n        return selector;\n      }\n    }\n    if (selector) {\n        // String\n      if (typeof selector === 'string') {\n        var els;\n        var tempParent;\n        var html = selector.trim();\n        if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n          var toCreate = 'div';\n          if (html.indexOf('<li') === 0) { toCreate = 'ul'; }\n          if (html.indexOf('<tr') === 0) { toCreate = 'tbody'; }\n          if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) { toCreate = 'tr'; }\n          if (html.indexOf('<tbody') === 0) { toCreate = 'table'; }\n          if (html.indexOf('<option') === 0) { toCreate = 'select'; }\n          tempParent = doc.createElement(toCreate);\n          tempParent.innerHTML = html;\n          for (i = 0; i < tempParent.childNodes.length; i += 1) {\n            arr.push(tempParent.childNodes[i]);\n          }\n        } else {\n          if (!context && selector[0] === '#' && !selector.match(/[ .<>:~]/)) {\n            // Pure ID selector\n            els = [doc.getElementById(selector.trim().split('#')[1])];\n          } else {\n            // Other selectors\n            els = (context || doc).querySelectorAll(selector.trim());\n          }\n          for (i = 0; i < els.length; i += 1) {\n            if (els[i]) { arr.push(els[i]); }\n          }\n        }\n      } else if (selector.nodeType || selector === win || selector === doc) {\n        // Node/element\n        arr.push(selector);\n      } else if (selector.length > 0 && selector[0].nodeType) {\n        // Array of elements or instance of Dom\n        for (i = 0; i < selector.length; i += 1) {\n          arr.push(selector[i]);\n        }\n      }\n    }\n    return new Dom7(arr);\n  }\n\n  $.fn = Dom7.prototype;\n  $.Class = Dom7;\n  $.Dom7 = Dom7;\n\n  function unique(arr) {\n    var uniqueArray = [];\n    for (var i = 0; i < arr.length; i += 1) {\n      if (uniqueArray.indexOf(arr[i]) === -1) { uniqueArray.push(arr[i]); }\n    }\n    return uniqueArray;\n  }\n\n  // Classes and attributes\n  function addClass(className) {\n    var this$1 = this;\n\n    if (typeof className === 'undefined') {\n      return this;\n    }\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this$1[j] !== 'undefined' && typeof this$1[j].classList !== 'undefined') { this$1[j].classList.add(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function removeClass(className) {\n    var this$1 = this;\n\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this$1[j] !== 'undefined' && typeof this$1[j].classList !== 'undefined') { this$1[j].classList.remove(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function hasClass(className) {\n    if (!this[0]) { return false; }\n    return this[0].classList.contains(className);\n  }\n  function toggleClass(className) {\n    var this$1 = this;\n\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this$1[j] !== 'undefined' && typeof this$1[j].classList !== 'undefined') { this$1[j].classList.toggle(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function attr(attrs, value) {\n    var arguments$1 = arguments;\n    var this$1 = this;\n\n    if (arguments.length === 1 && typeof attrs === 'string') {\n      // Get attr\n      if (this[0]) { return this[0].getAttribute(attrs); }\n      return undefined;\n    }\n\n    // Set attrs\n    for (var i = 0; i < this.length; i += 1) {\n      if (arguments$1.length === 2) {\n        // String\n        this$1[i].setAttribute(attrs, value);\n      } else {\n        // Object\n        // eslint-disable-next-line\n        for (var attrName in attrs) {\n          this$1[i][attrName] = attrs[attrName];\n          this$1[i].setAttribute(attrName, attrs[attrName]);\n        }\n      }\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function removeAttr(attr) {\n    var this$1 = this;\n\n    for (var i = 0; i < this.length; i += 1) {\n      this$1[i].removeAttribute(attr);\n    }\n    return this;\n  }\n  function data(key, value) {\n    var this$1 = this;\n\n    var el;\n    if (typeof value === 'undefined') {\n      el = this[0];\n      // Get value\n      if (el) {\n        if (el.dom7ElementDataStorage && (key in el.dom7ElementDataStorage)) {\n          return el.dom7ElementDataStorage[key];\n        }\n\n        var dataKey = el.getAttribute((\"data-\" + key));\n        if (dataKey) {\n          return dataKey;\n        }\n        return undefined;\n      }\n      return undefined;\n    }\n\n    // Set value\n    for (var i = 0; i < this.length; i += 1) {\n      el = this$1[i];\n      if (!el.dom7ElementDataStorage) { el.dom7ElementDataStorage = {}; }\n      el.dom7ElementDataStorage[key] = value;\n    }\n    return this;\n  }\n  // Transforms\n  // eslint-disable-next-line\n  function transform(transform) {\n    var this$1 = this;\n\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this$1[i].style;\n      elStyle.webkitTransform = transform;\n      elStyle.transform = transform;\n    }\n    return this;\n  }\n  function transition(duration) {\n    var this$1 = this;\n\n    if (typeof duration !== 'string') {\n      duration = duration + \"ms\"; // eslint-disable-line\n    }\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this$1[i].style;\n      elStyle.webkitTransitionDuration = duration;\n      elStyle.transitionDuration = duration;\n    }\n    return this;\n  }\n  // Events\n  function on() {\n    var this$1 = this;\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    function handleLiveEvent(e) {\n      var target = e.target;\n      if (!target) { return; }\n      var eventData = e.target.dom7EventData || [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      if ($(target).is(targetSelector)) { listener.apply(target, eventData); }\n      else {\n        var parents = $(target).parents(); // eslint-disable-line\n        for (var k = 0; k < parents.length; k += 1) {\n          if ($(parents[k]).is(targetSelector)) { listener.apply(parents[k], eventData); }\n        }\n      }\n    }\n    function handleEvent(e) {\n      var eventData = e && e.target ? e.target.dom7EventData || [] : [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      listener.apply(this, eventData);\n    }\n    var events = eventType.split(' ');\n    var j;\n    for (var i = 0; i < this.length; i += 1) {\n      var el = this$1[i];\n      if (!targetSelector) {\n        for (j = 0; j < events.length; j += 1) {\n          var event = events[j];\n          if (!el.dom7Listeners) { el.dom7Listeners = {}; }\n          if (!el.dom7Listeners[event]) { el.dom7Listeners[event] = []; }\n          el.dom7Listeners[event].push({\n            listener: listener,\n            proxyListener: handleEvent,\n          });\n          el.addEventListener(event, handleEvent, capture);\n        }\n      } else {\n        // Live events\n        for (j = 0; j < events.length; j += 1) {\n          var event$1 = events[j];\n          if (!el.dom7LiveListeners) { el.dom7LiveListeners = {}; }\n          if (!el.dom7LiveListeners[event$1]) { el.dom7LiveListeners[event$1] = []; }\n          el.dom7LiveListeners[event$1].push({\n            listener: listener,\n            proxyListener: handleLiveEvent,\n          });\n          el.addEventListener(event$1, handleLiveEvent, capture);\n        }\n      }\n    }\n    return this;\n  }\n  function off() {\n    var this$1 = this;\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    var events = eventType.split(' ');\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this$1[j];\n        var handlers = (void 0);\n        if (!targetSelector && el.dom7Listeners) {\n          handlers = el.dom7Listeners[event];\n        } else if (targetSelector && el.dom7LiveListeners) {\n          handlers = el.dom7LiveListeners[event];\n        }\n        if (handlers && handlers.length) {\n          for (var k = handlers.length - 1; k >= 0; k -= 1) {\n            var handler = handlers[k];\n            if (listener && handler.listener === listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            } else if (!listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            }\n          }\n        }\n      }\n    }\n    return this;\n  }\n  function trigger() {\n    var this$1 = this;\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var events = args[0].split(' ');\n    var eventData = args[1];\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this$1[j];\n        var evt = (void 0);\n        try {\n          evt = new win.CustomEvent(event, {\n            detail: eventData,\n            bubbles: true,\n            cancelable: true,\n          });\n        } catch (e) {\n          evt = doc.createEvent('Event');\n          evt.initEvent(event, true, true);\n          evt.detail = eventData;\n        }\n        // eslint-disable-next-line\n        el.dom7EventData = args.filter(function (data, dataIndex) { return dataIndex > 0; });\n        el.dispatchEvent(evt);\n        el.dom7EventData = [];\n        delete el.dom7EventData;\n      }\n    }\n    return this;\n  }\n  function transitionEnd(callback) {\n    var events = ['webkitTransitionEnd', 'transitionend'];\n    var dom = this;\n    var i;\n    function fireCallBack(e) {\n      /* jshint validthis:true */\n      if (e.target !== this) { return; }\n      callback.call(this, e);\n      for (i = 0; i < events.length; i += 1) {\n        dom.off(events[i], fireCallBack);\n      }\n    }\n    if (callback) {\n      for (i = 0; i < events.length; i += 1) {\n        dom.on(events[i], fireCallBack);\n      }\n    }\n    return this;\n  }\n  function outerWidth(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetWidth + parseFloat(styles.getPropertyValue('margin-right')) + parseFloat(styles.getPropertyValue('margin-left'));\n      }\n      return this[0].offsetWidth;\n    }\n    return null;\n  }\n  function outerHeight(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetHeight + parseFloat(styles.getPropertyValue('margin-top')) + parseFloat(styles.getPropertyValue('margin-bottom'));\n      }\n      return this[0].offsetHeight;\n    }\n    return null;\n  }\n  function offset() {\n    if (this.length > 0) {\n      var el = this[0];\n      var box = el.getBoundingClientRect();\n      var body = doc.body;\n      var clientTop = el.clientTop || body.clientTop || 0;\n      var clientLeft = el.clientLeft || body.clientLeft || 0;\n      var scrollTop = el === win ? win.scrollY : el.scrollTop;\n      var scrollLeft = el === win ? win.scrollX : el.scrollLeft;\n      return {\n        top: (box.top + scrollTop) - clientTop,\n        left: (box.left + scrollLeft) - clientLeft,\n      };\n    }\n\n    return null;\n  }\n  function styles() {\n    if (this[0]) { return win.getComputedStyle(this[0], null); }\n    return {};\n  }\n  function css(props, value) {\n    var this$1 = this;\n\n    var i;\n    if (arguments.length === 1) {\n      if (typeof props === 'string') {\n        if (this[0]) { return win.getComputedStyle(this[0], null).getPropertyValue(props); }\n      } else {\n        for (i = 0; i < this.length; i += 1) {\n          // eslint-disable-next-line\n          for (var prop in props) {\n            this$1[i].style[prop] = props[prop];\n          }\n        }\n        return this;\n      }\n    }\n    if (arguments.length === 2 && typeof props === 'string') {\n      for (i = 0; i < this.length; i += 1) {\n        this$1[i].style[props] = value;\n      }\n      return this;\n    }\n    return this;\n  }\n  // Iterate over the collection passing elements to `callback`\n  function each(callback) {\n    var this$1 = this;\n\n    // Don't bother continuing without a callback\n    if (!callback) { return this; }\n    // Iterate over the current collection\n    for (var i = 0; i < this.length; i += 1) {\n      // If the callback returns false\n      if (callback.call(this$1[i], i, this$1[i]) === false) {\n        // End the loop early\n        return this$1;\n      }\n    }\n    // Return `this` to allow chained DOM operations\n    return this;\n  }\n  // eslint-disable-next-line\n  function html(html) {\n    var this$1 = this;\n\n    if (typeof html === 'undefined') {\n      return this[0] ? this[0].innerHTML : undefined;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this$1[i].innerHTML = html;\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function text(text) {\n    var this$1 = this;\n\n    if (typeof text === 'undefined') {\n      if (this[0]) {\n        return this[0].textContent.trim();\n      }\n      return null;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this$1[i].textContent = text;\n    }\n    return this;\n  }\n  function is(selector) {\n    var el = this[0];\n    var compareWith;\n    var i;\n    if (!el || typeof selector === 'undefined') { return false; }\n    if (typeof selector === 'string') {\n      if (el.matches) { return el.matches(selector); }\n      else if (el.webkitMatchesSelector) { return el.webkitMatchesSelector(selector); }\n      else if (el.msMatchesSelector) { return el.msMatchesSelector(selector); }\n\n      compareWith = $(selector);\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    } else if (selector === doc) { return el === doc; }\n    else if (selector === win) { return el === win; }\n\n    if (selector.nodeType || selector instanceof Dom7) {\n      compareWith = selector.nodeType ? [selector] : selector;\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    }\n    return false;\n  }\n  function index() {\n    var child = this[0];\n    var i;\n    if (child) {\n      i = 0;\n      // eslint-disable-next-line\n      while ((child = child.previousSibling) !== null) {\n        if (child.nodeType === 1) { i += 1; }\n      }\n      return i;\n    }\n    return undefined;\n  }\n  // eslint-disable-next-line\n  function eq(index) {\n    if (typeof index === 'undefined') { return this; }\n    var length = this.length;\n    var returnIndex;\n    if (index > length - 1) {\n      return new Dom7([]);\n    }\n    if (index < 0) {\n      returnIndex = length + index;\n      if (returnIndex < 0) { return new Dom7([]); }\n      return new Dom7([this[returnIndex]]);\n    }\n    return new Dom7([this[index]]);\n  }\n  function append() {\n    var this$1 = this;\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var newChild;\n\n    for (var k = 0; k < args.length; k += 1) {\n      newChild = args[k];\n      for (var i = 0; i < this.length; i += 1) {\n        if (typeof newChild === 'string') {\n          var tempDiv = doc.createElement('div');\n          tempDiv.innerHTML = newChild;\n          while (tempDiv.firstChild) {\n            this$1[i].appendChild(tempDiv.firstChild);\n          }\n        } else if (newChild instanceof Dom7) {\n          for (var j = 0; j < newChild.length; j += 1) {\n            this$1[i].appendChild(newChild[j]);\n          }\n        } else {\n          this$1[i].appendChild(newChild);\n        }\n      }\n    }\n\n    return this;\n  }\n  function prepend(newChild) {\n    var this$1 = this;\n\n    var i;\n    var j;\n    for (i = 0; i < this.length; i += 1) {\n      if (typeof newChild === 'string') {\n        var tempDiv = doc.createElement('div');\n        tempDiv.innerHTML = newChild;\n        for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {\n          this$1[i].insertBefore(tempDiv.childNodes[j], this$1[i].childNodes[0]);\n        }\n      } else if (newChild instanceof Dom7) {\n        for (j = 0; j < newChild.length; j += 1) {\n          this$1[i].insertBefore(newChild[j], this$1[i].childNodes[0]);\n        }\n      } else {\n        this$1[i].insertBefore(newChild, this$1[i].childNodes[0]);\n      }\n    }\n    return this;\n  }\n  function next(selector) {\n    if (this.length > 0) {\n      if (selector) {\n        if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {\n          return new Dom7([this[0].nextElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (this[0].nextElementSibling) { return new Dom7([this[0].nextElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function nextAll(selector) {\n    var nextEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.nextElementSibling) {\n      var next = el.nextElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(next).is(selector)) { nextEls.push(next); }\n      } else { nextEls.push(next); }\n      el = next;\n    }\n    return new Dom7(nextEls);\n  }\n  function prev(selector) {\n    if (this.length > 0) {\n      var el = this[0];\n      if (selector) {\n        if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {\n          return new Dom7([el.previousElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (el.previousElementSibling) { return new Dom7([el.previousElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function prevAll(selector) {\n    var prevEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.previousElementSibling) {\n      var prev = el.previousElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(prev).is(selector)) { prevEls.push(prev); }\n      } else { prevEls.push(prev); }\n      el = prev;\n    }\n    return new Dom7(prevEls);\n  }\n  function parent(selector) {\n    var this$1 = this;\n\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      if (this$1[i].parentNode !== null) {\n        if (selector) {\n          if ($(this$1[i].parentNode).is(selector)) { parents.push(this$1[i].parentNode); }\n        } else {\n          parents.push(this$1[i].parentNode);\n        }\n      }\n    }\n    return $(unique(parents));\n  }\n  function parents(selector) {\n    var this$1 = this;\n\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var parent = this$1[i].parentNode; // eslint-disable-line\n      while (parent) {\n        if (selector) {\n          if ($(parent).is(selector)) { parents.push(parent); }\n        } else {\n          parents.push(parent);\n        }\n        parent = parent.parentNode;\n      }\n    }\n    return $(unique(parents));\n  }\n  function closest(selector) {\n    var closest = this; // eslint-disable-line\n    if (typeof selector === 'undefined') {\n      return new Dom7([]);\n    }\n    if (!closest.is(selector)) {\n      closest = closest.parents(selector).eq(0);\n    }\n    return closest;\n  }\n  function find(selector) {\n    var this$1 = this;\n\n    var foundElements = [];\n    for (var i = 0; i < this.length; i += 1) {\n      var found = this$1[i].querySelectorAll(selector);\n      for (var j = 0; j < found.length; j += 1) {\n        foundElements.push(found[j]);\n      }\n    }\n    return new Dom7(foundElements);\n  }\n  function children(selector) {\n    var this$1 = this;\n\n    var children = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var childNodes = this$1[i].childNodes;\n\n      for (var j = 0; j < childNodes.length; j += 1) {\n        if (!selector) {\n          if (childNodes[j].nodeType === 1) { children.push(childNodes[j]); }\n        } else if (childNodes[j].nodeType === 1 && $(childNodes[j]).is(selector)) {\n          children.push(childNodes[j]);\n        }\n      }\n    }\n    return new Dom7(unique(children));\n  }\n  function remove() {\n    var this$1 = this;\n\n    for (var i = 0; i < this.length; i += 1) {\n      if (this$1[i].parentNode) { this$1[i].parentNode.removeChild(this$1[i]); }\n    }\n    return this;\n  }\n  function add() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var dom = this;\n    var i;\n    var j;\n    for (i = 0; i < args.length; i += 1) {\n      var toAdd = $(args[i]);\n      for (j = 0; j < toAdd.length; j += 1) {\n        dom[dom.length] = toAdd[j];\n        dom.length += 1;\n      }\n    }\n    return dom;\n  }\n\n  var Methods = {\n    addClass: addClass,\n    removeClass: removeClass,\n    hasClass: hasClass,\n    toggleClass: toggleClass,\n    attr: attr,\n    removeAttr: removeAttr,\n    data: data,\n    transform: transform,\n    transition: transition,\n    on: on,\n    off: off,\n    trigger: trigger,\n    transitionEnd: transitionEnd,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    offset: offset,\n    css: css,\n    each: each,\n    html: html,\n    text: text,\n    is: is,\n    index: index,\n    eq: eq,\n    append: append,\n    prepend: prepend,\n    next: next,\n    nextAll: nextAll,\n    prev: prev,\n    prevAll: prevAll,\n    parent: parent,\n    parents: parents,\n    closest: closest,\n    find: find,\n    children: children,\n    remove: remove,\n    add: add,\n    styles: styles,\n  };\n\n  Object.keys(Methods).forEach(function (methodName) {\n    $.fn[methodName] = Methods[methodName];\n  });\n\n  var Utils = {\n    deleteProps: function deleteProps(obj) {\n      var object = obj;\n      Object.keys(object).forEach(function (key) {\n        try {\n          object[key] = null;\n        } catch (e) {\n          // no getter for object\n        }\n        try {\n          delete object[key];\n        } catch (e) {\n          // something got wrong\n        }\n      });\n    },\n    nextTick: function nextTick(callback, delay) {\n      if ( delay === void 0 ) delay = 0;\n\n      return setTimeout(callback, delay);\n    },\n    now: function now() {\n      return Date.now();\n    },\n    getTranslate: function getTranslate(el, axis) {\n      if ( axis === void 0 ) axis = 'x';\n\n      var matrix;\n      var curTransform;\n      var transformMatrix;\n\n      var curStyle = win.getComputedStyle(el, null);\n\n      if (win.WebKitCSSMatrix) {\n        curTransform = curStyle.transform || curStyle.webkitTransform;\n        if (curTransform.split(',').length > 6) {\n          curTransform = curTransform.split(', ').map(function (a) { return a.replace(',', '.'); }).join(', ');\n        }\n        // Some old versions of Webkit choke when 'none' is passed; pass\n        // empty string instead in this case\n        transformMatrix = new win.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n      } else {\n        transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n        matrix = transformMatrix.toString().split(',');\n      }\n\n      if (axis === 'x') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m41; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[12]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[4]); }\n      }\n      if (axis === 'y') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m42; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[13]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[5]); }\n      }\n      return curTransform || 0;\n    },\n    parseUrlQuery: function parseUrlQuery(url) {\n      var query = {};\n      var urlToParse = url || win.location.href;\n      var i;\n      var params;\n      var param;\n      var length;\n      if (typeof urlToParse === 'string' && urlToParse.length) {\n        urlToParse = urlToParse.indexOf('?') > -1 ? urlToParse.replace(/\\S*\\?/, '') : '';\n        params = urlToParse.split('&').filter(function (paramsPart) { return paramsPart !== ''; });\n        length = params.length;\n\n        for (i = 0; i < length; i += 1) {\n          param = params[i].replace(/#\\S+/g, '').split('=');\n          query[decodeURIComponent(param[0])] = typeof param[1] === 'undefined' ? undefined : decodeURIComponent(param[1]) || '';\n        }\n      }\n      return query;\n    },\n    isObject: function isObject(o) {\n      return typeof o === 'object' && o !== null && o.constructor && o.constructor === Object;\n    },\n    extend: function extend() {\n      var args = [], len$1 = arguments.length;\n      while ( len$1-- ) args[ len$1 ] = arguments[ len$1 ];\n\n      var to = Object(args[0]);\n      for (var i = 1; i < args.length; i += 1) {\n        var nextSource = args[i];\n        if (nextSource !== undefined && nextSource !== null) {\n          var keysArray = Object.keys(Object(nextSource));\n          for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n            var nextKey = keysArray[nextIndex];\n            var desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n            if (desc !== undefined && desc.enumerable) {\n              if (Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else if (!Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                to[nextKey] = {};\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else {\n                to[nextKey] = nextSource[nextKey];\n              }\n            }\n          }\n        }\n      }\n      return to;\n    },\n  };\n\n  var Support = (function Support() {\n    var testDiv = doc.createElement('div');\n    return {\n      touch: (win.Modernizr && win.Modernizr.touch === true) || (function checkTouch() {\n        return !!(('ontouchstart' in win) || (win.DocumentTouch && doc instanceof win.DocumentTouch));\n      }()),\n\n      pointerEvents: !!(win.navigator.pointerEnabled || win.PointerEvent),\n      prefixedPointerEvents: !!win.navigator.msPointerEnabled,\n\n      transition: (function checkTransition() {\n        var style = testDiv.style;\n        return ('transition' in style || 'webkitTransition' in style || 'MozTransition' in style);\n      }()),\n      transforms3d: (win.Modernizr && win.Modernizr.csstransforms3d === true) || (function checkTransforms3d() {\n        var style = testDiv.style;\n        return ('webkitPerspective' in style || 'MozPerspective' in style || 'OPerspective' in style || 'MsPerspective' in style || 'perspective' in style);\n      }()),\n\n      flexbox: (function checkFlexbox() {\n        var style = testDiv.style;\n        var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n        for (var i = 0; i < styles.length; i += 1) {\n          if (styles[i] in style) { return true; }\n        }\n        return false;\n      }()),\n\n      observer: (function checkObserver() {\n        return ('MutationObserver' in win || 'WebkitMutationObserver' in win);\n      }()),\n\n      passiveListener: (function checkPassiveListener() {\n        var supportsPassive = false;\n        try {\n          var opts = Object.defineProperty({}, 'passive', {\n            // eslint-disable-next-line\n            get: function get() {\n              supportsPassive = true;\n            },\n          });\n          win.addEventListener('testPassiveListener', null, opts);\n        } catch (e) {\n          // No support\n        }\n        return supportsPassive;\n      }()),\n\n      gestures: (function checkGestures() {\n        return 'ongesturestart' in win;\n      }()),\n    };\n  }());\n\n  var SwiperClass = function SwiperClass(params) {\n    if ( params === void 0 ) params = {};\n\n    var self = this;\n    self.params = params;\n\n    // Events\n    self.eventsListeners = {};\n\n    if (self.params && self.params.on) {\n      Object.keys(self.params.on).forEach(function (eventName) {\n        self.on(eventName, self.params.on[eventName]);\n      });\n    }\n  };\n\n  var staticAccessors = { components: { configurable: true } };\n\n  SwiperClass.prototype.on = function on (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    var method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(function (event) {\n      if (!self.eventsListeners[event]) { self.eventsListeners[event] = []; }\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.once = function once (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    function onceHandler() {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n      handler.apply(self, args);\n      self.off(events, onceHandler);\n    }\n    return self.on(events, onceHandler, priority);\n  };\n\n  SwiperClass.prototype.off = function off (events, handler) {\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    events.split(' ').forEach(function (event) {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event] && self.eventsListeners[event].length) {\n        self.eventsListeners[event].forEach(function (eventHandler, index) {\n          if (eventHandler === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.emit = function emit () {\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    var events;\n    var data;\n    var context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    var eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(function (event) {\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        var handlers = [];\n        self.eventsListeners[event].forEach(function (eventHandler) {\n          handlers.push(eventHandler);\n        });\n        handlers.forEach(function (eventHandler) {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.useModulesParams = function useModulesParams (instanceParams) {\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      // Extend params\n      if (module.params) {\n        Utils.extend(instanceParams, module.params);\n      }\n    });\n  };\n\n  SwiperClass.prototype.useModules = function useModules (modulesParams) {\n      if ( modulesParams === void 0 ) modulesParams = {};\n\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      var moduleParams = modulesParams[moduleName] || {};\n      // Extend instance methods and props\n      if (module.instance) {\n        Object.keys(module.instance).forEach(function (modulePropName) {\n          var moduleProp = module.instance[modulePropName];\n          if (typeof moduleProp === 'function') {\n            instance[modulePropName] = moduleProp.bind(instance);\n          } else {\n            instance[modulePropName] = moduleProp;\n          }\n        });\n      }\n      // Add event listeners\n      if (module.on && instance.on) {\n        Object.keys(module.on).forEach(function (moduleEventName) {\n          instance.on(moduleEventName, module.on[moduleEventName]);\n        });\n      }\n\n      // Module create callback\n      if (module.create) {\n        module.create.bind(instance)(moduleParams);\n      }\n    });\n  };\n\n  staticAccessors.components.set = function (components) {\n    var Class = this;\n    if (!Class.use) { return; }\n    Class.use(components);\n  };\n\n  SwiperClass.installModule = function installModule (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (!Class.prototype.modules) { Class.prototype.modules = {}; }\n    var name = module.name || (((Object.keys(Class.prototype.modules).length) + \"_\" + (Utils.now())));\n    Class.prototype.modules[name] = module;\n    // Prototype\n    if (module.proto) {\n      Object.keys(module.proto).forEach(function (key) {\n        Class.prototype[key] = module.proto[key];\n      });\n    }\n    // Class\n    if (module.static) {\n      Object.keys(module.static).forEach(function (key) {\n        Class[key] = module.static[key];\n      });\n    }\n    // Callback\n    if (module.install) {\n      module.install.apply(Class, params);\n    }\n    return Class;\n  };\n\n  SwiperClass.use = function use (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (Array.isArray(module)) {\n      module.forEach(function (m) { return Class.installModule(m); });\n      return Class;\n    }\n    return Class.installModule.apply(Class, [ module ].concat( params ));\n  };\n\n  Object.defineProperties( SwiperClass, staticAccessors );\n\n  function updateSize () {\n    var swiper = this;\n    var width;\n    var height;\n    var $el = swiper.$el;\n    if (typeof swiper.params.width !== 'undefined') {\n      width = swiper.params.width;\n    } else {\n      width = $el[0].clientWidth;\n    }\n    if (typeof swiper.params.height !== 'undefined') {\n      height = swiper.params.height;\n    } else {\n      height = $el[0].clientHeight;\n    }\n    if ((width === 0 && swiper.isHorizontal()) || (height === 0 && swiper.isVertical())) {\n      return;\n    }\n\n    // Subtract paddings\n    width = width - parseInt($el.css('padding-left'), 10) - parseInt($el.css('padding-right'), 10);\n    height = height - parseInt($el.css('padding-top'), 10) - parseInt($el.css('padding-bottom'), 10);\n\n    Utils.extend(swiper, {\n      width: width,\n      height: height,\n      size: swiper.isHorizontal() ? width : height,\n    });\n  }\n\n  function updateSlides () {\n    var swiper = this;\n    var params = swiper.params;\n\n    var $wrapperEl = swiper.$wrapperEl;\n    var swiperSize = swiper.size;\n    var rtl = swiper.rtlTranslate;\n    var wrongRTL = swiper.wrongRTL;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n    var previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n    var slides = $wrapperEl.children((\".\" + (swiper.params.slideClass)));\n    var slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n    var snapGrid = [];\n    var slidesGrid = [];\n    var slidesSizesGrid = [];\n\n    var offsetBefore = params.slidesOffsetBefore;\n    if (typeof offsetBefore === 'function') {\n      offsetBefore = params.slidesOffsetBefore.call(swiper);\n    }\n\n    var offsetAfter = params.slidesOffsetAfter;\n    if (typeof offsetAfter === 'function') {\n      offsetAfter = params.slidesOffsetAfter.call(swiper);\n    }\n\n    var previousSnapGridLength = swiper.snapGrid.length;\n    var previousSlidesGridLength = swiper.snapGrid.length;\n\n    var spaceBetween = params.spaceBetween;\n    var slidePosition = -offsetBefore;\n    var prevSlideSize = 0;\n    var index = 0;\n    if (typeof swiperSize === 'undefined') {\n      return;\n    }\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = (parseFloat(spaceBetween.replace('%', '')) / 100) * swiperSize;\n    }\n\n    swiper.virtualSize = -spaceBetween;\n\n    // reset margins\n    if (rtl) { slides.css({ marginLeft: '', marginTop: '' }); }\n    else { slides.css({ marginRight: '', marginBottom: '' }); }\n\n    var slidesNumberEvenToRows;\n    if (params.slidesPerColumn > 1) {\n      if (Math.floor(slidesLength / params.slidesPerColumn) === slidesLength / swiper.params.slidesPerColumn) {\n        slidesNumberEvenToRows = slidesLength;\n      } else {\n        slidesNumberEvenToRows = Math.ceil(slidesLength / params.slidesPerColumn) * params.slidesPerColumn;\n      }\n      if (params.slidesPerView !== 'auto' && params.slidesPerColumnFill === 'row') {\n        slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, params.slidesPerView * params.slidesPerColumn);\n      }\n    }\n\n    // Calc slides\n    var slideSize;\n    var slidesPerColumn = params.slidesPerColumn;\n    var slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n    var numFullColumns = slidesPerRow - ((params.slidesPerColumn * slidesPerRow) - slidesLength);\n    for (var i = 0; i < slidesLength; i += 1) {\n      slideSize = 0;\n      var slide = slides.eq(i);\n      if (params.slidesPerColumn > 1) {\n        // Set slides order\n        var newSlideOrderIndex = (void 0);\n        var column = (void 0);\n        var row = (void 0);\n        if (params.slidesPerColumnFill === 'column') {\n          column = Math.floor(i / slidesPerColumn);\n          row = i - (column * slidesPerColumn);\n          if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn - 1)) {\n            row += 1;\n            if (row >= slidesPerColumn) {\n              row = 0;\n              column += 1;\n            }\n          }\n          newSlideOrderIndex = column + ((row * slidesNumberEvenToRows) / slidesPerColumn);\n          slide\n            .css({\n              '-webkit-box-ordinal-group': newSlideOrderIndex,\n              '-moz-box-ordinal-group': newSlideOrderIndex,\n              '-ms-flex-order': newSlideOrderIndex,\n              '-webkit-order': newSlideOrderIndex,\n              order: newSlideOrderIndex,\n            });\n        } else {\n          row = Math.floor(i / slidesPerRow);\n          column = i - (row * slidesPerRow);\n        }\n        slide\n          .css(\n            (\"margin-\" + (swiper.isHorizontal() ? 'top' : 'left')),\n            (row !== 0 && params.spaceBetween) && (((params.spaceBetween) + \"px\"))\n          )\n          .attr('data-swiper-column', column)\n          .attr('data-swiper-row', row);\n      }\n      if (slide.css('display') === 'none') { continue; } // eslint-disable-line\n\n      if (params.slidesPerView === 'auto') {\n        var slideStyles = win.getComputedStyle(slide[0], null);\n        var currentTransform = slide[0].style.transform;\n        var currentWebKitTransform = slide[0].style.webkitTransform;\n        if (currentTransform) {\n          slide[0].style.transform = 'none';\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = 'none';\n        }\n        if (params.roundLengths) {\n          slideSize = swiper.isHorizontal()\n            ? slide.outerWidth(true)\n            : slide.outerHeight(true);\n        } else {\n          // eslint-disable-next-line\n          if (swiper.isHorizontal()) {\n            slideSize = slide[0].getBoundingClientRect().width\n              + parseFloat(slideStyles.getPropertyValue('margin-left'))\n              + parseFloat(slideStyles.getPropertyValue('margin-right'));\n          } else {\n            slideSize = slide[0].getBoundingClientRect().height\n              + parseFloat(slideStyles.getPropertyValue('margin-top'))\n              + parseFloat(slideStyles.getPropertyValue('margin-bottom'));\n          }\n        }\n        if (currentTransform) {\n          slide[0].style.transform = currentTransform;\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = currentWebKitTransform;\n        }\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n      } else {\n        slideSize = (swiperSize - ((params.slidesPerView - 1) * spaceBetween)) / params.slidesPerView;\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n\n        if (slides[i]) {\n          if (swiper.isHorizontal()) {\n            slides[i].style.width = slideSize + \"px\";\n          } else {\n            slides[i].style.height = slideSize + \"px\";\n          }\n        }\n      }\n      if (slides[i]) {\n        slides[i].swiperSlideSize = slideSize;\n      }\n      slidesSizesGrid.push(slideSize);\n\n\n      if (params.centeredSlides) {\n        slidePosition = slidePosition + (slideSize / 2) + (prevSlideSize / 2) + spaceBetween;\n        if (prevSlideSize === 0 && i !== 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (i === 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (Math.abs(slidePosition) < 1 / 1000) { slidePosition = 0; }\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n      } else {\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n        slidePosition = slidePosition + slideSize + spaceBetween;\n      }\n\n      swiper.virtualSize += slideSize + spaceBetween;\n\n      prevSlideSize = slideSize;\n\n      index += 1;\n    }\n    swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n    var newSlidesGrid;\n\n    if (\n      rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n      $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") });\n    }\n    if (!Support.flexbox || params.setWrapperSize) {\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n    }\n\n    if (params.slidesPerColumn > 1) {\n      swiper.virtualSize = (slideSize + params.spaceBetween) * slidesNumberEvenToRows;\n      swiper.virtualSize = Math.ceil(swiper.virtualSize / params.slidesPerColumn) - params.spaceBetween;\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      if (params.centeredSlides) {\n        newSlidesGrid = [];\n        for (var i$1 = 0; i$1 < snapGrid.length; i$1 += 1) {\n          var slidesGridItem = snapGrid[i$1];\n          if (params.roundLengths) { slidesGridItem = Math.floor(slidesGridItem); }\n          if (snapGrid[i$1] < swiper.virtualSize + snapGrid[0]) { newSlidesGrid.push(slidesGridItem); }\n        }\n        snapGrid = newSlidesGrid;\n      }\n    }\n\n    // Remove last grid elements depending on width\n    if (!params.centeredSlides) {\n      newSlidesGrid = [];\n      for (var i$2 = 0; i$2 < snapGrid.length; i$2 += 1) {\n        var slidesGridItem$1 = snapGrid[i$2];\n        if (params.roundLengths) { slidesGridItem$1 = Math.floor(slidesGridItem$1); }\n        if (snapGrid[i$2] <= swiper.virtualSize - swiperSize) {\n          newSlidesGrid.push(slidesGridItem$1);\n        }\n      }\n      snapGrid = newSlidesGrid;\n      if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n        snapGrid.push(swiper.virtualSize - swiperSize);\n      }\n    }\n    if (snapGrid.length === 0) { snapGrid = [0]; }\n\n    if (params.spaceBetween !== 0) {\n      if (swiper.isHorizontal()) {\n        if (rtl) { slides.css({ marginLeft: (spaceBetween + \"px\") }); }\n        else { slides.css({ marginRight: (spaceBetween + \"px\") }); }\n      } else { slides.css({ marginBottom: (spaceBetween + \"px\") }); }\n    }\n\n    if (params.centerInsufficientSlides) {\n      var allSlidesSize = 0;\n      slidesSizesGrid.forEach(function (slideSizeValue) {\n        allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n      });\n      allSlidesSize -= params.spaceBetween;\n      if (allSlidesSize < swiperSize) {\n        var allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n        snapGrid.forEach(function (snap, snapIndex) {\n          snapGrid[snapIndex] = snap - allSlidesOffset;\n        });\n        slidesGrid.forEach(function (snap, snapIndex) {\n          slidesGrid[snapIndex] = snap + allSlidesOffset;\n        });\n      }\n    }\n\n    Utils.extend(swiper, {\n      slides: slides,\n      snapGrid: snapGrid,\n      slidesGrid: slidesGrid,\n      slidesSizesGrid: slidesSizesGrid,\n    });\n\n    if (slidesLength !== previousSlidesLength) {\n      swiper.emit('slidesLengthChange');\n    }\n    if (snapGrid.length !== previousSnapGridLength) {\n      if (swiper.params.watchOverflow) { swiper.checkOverflow(); }\n      swiper.emit('snapGridLengthChange');\n    }\n    if (slidesGrid.length !== previousSlidesGridLength) {\n      swiper.emit('slidesGridLengthChange');\n    }\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateSlidesOffset();\n    }\n  }\n\n  function updateAutoHeight (speed) {\n    var swiper = this;\n    var activeSlides = [];\n    var newHeight = 0;\n    var i;\n    if (typeof speed === 'number') {\n      swiper.setTransition(speed);\n    } else if (speed === true) {\n      swiper.setTransition(swiper.params.speed);\n    }\n    // Find slides currently in view\n    if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        var index = swiper.activeIndex + i;\n        if (index > swiper.slides.length) { break; }\n        activeSlides.push(swiper.slides.eq(index)[0]);\n      }\n    } else {\n      activeSlides.push(swiper.slides.eq(swiper.activeIndex)[0]);\n    }\n\n    // Find new height from highest slide in view\n    for (i = 0; i < activeSlides.length; i += 1) {\n      if (typeof activeSlides[i] !== 'undefined') {\n        var height = activeSlides[i].offsetHeight;\n        newHeight = height > newHeight ? height : newHeight;\n      }\n    }\n\n    // Update Height\n    if (newHeight) { swiper.$wrapperEl.css('height', (newHeight + \"px\")); }\n  }\n\n  function updateSlidesOffset () {\n    var swiper = this;\n    var slides = swiper.slides;\n    for (var i = 0; i < slides.length; i += 1) {\n      slides[i].swiperSlideOffset = swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop;\n    }\n  }\n\n  function updateSlidesProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var slides = swiper.slides;\n    var rtl = swiper.rtlTranslate;\n\n    if (slides.length === 0) { return; }\n    if (typeof slides[0].swiperSlideOffset === 'undefined') { swiper.updateSlidesOffset(); }\n\n    var offsetCenter = -translate;\n    if (rtl) { offsetCenter = translate; }\n\n    // Visible Slides\n    slides.removeClass(params.slideVisibleClass);\n\n    swiper.visibleSlidesIndexes = [];\n    swiper.visibleSlides = [];\n\n    for (var i = 0; i < slides.length; i += 1) {\n      var slide = slides[i];\n      var slideProgress = (\n        (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0)) - slide.swiperSlideOffset\n      ) / (slide.swiperSlideSize + params.spaceBetween);\n      if (params.watchSlidesVisibility) {\n        var slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n        var slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n        var isVisible = (slideBefore >= 0 && slideBefore < swiper.size)\n                  || (slideAfter > 0 && slideAfter <= swiper.size)\n                  || (slideBefore <= 0 && slideAfter >= swiper.size);\n        if (isVisible) {\n          swiper.visibleSlides.push(slide);\n          swiper.visibleSlidesIndexes.push(i);\n          slides.eq(i).addClass(params.slideVisibleClass);\n        }\n      }\n      slide.progress = rtl ? -slideProgress : slideProgress;\n    }\n    swiper.visibleSlides = $(swiper.visibleSlides);\n  }\n\n  function updateProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    var progress = swiper.progress;\n    var isBeginning = swiper.isBeginning;\n    var isEnd = swiper.isEnd;\n    var wasBeginning = isBeginning;\n    var wasEnd = isEnd;\n    if (translatesDiff === 0) {\n      progress = 0;\n      isBeginning = true;\n      isEnd = true;\n    } else {\n      progress = (translate - swiper.minTranslate()) / (translatesDiff);\n      isBeginning = progress <= 0;\n      isEnd = progress >= 1;\n    }\n    Utils.extend(swiper, {\n      progress: progress,\n      isBeginning: isBeginning,\n      isEnd: isEnd,\n    });\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) { swiper.updateSlidesProgress(translate); }\n\n    if (isBeginning && !wasBeginning) {\n      swiper.emit('reachBeginning toEdge');\n    }\n    if (isEnd && !wasEnd) {\n      swiper.emit('reachEnd toEdge');\n    }\n    if ((wasBeginning && !isBeginning) || (wasEnd && !isEnd)) {\n      swiper.emit('fromEdge');\n    }\n\n    swiper.emit('progress', progress);\n  }\n\n  function updateSlidesClasses () {\n    var swiper = this;\n\n    var slides = swiper.slides;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n    var realIndex = swiper.realIndex;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n\n    slides.removeClass(((params.slideActiveClass) + \" \" + (params.slideNextClass) + \" \" + (params.slidePrevClass) + \" \" + (params.slideDuplicateActiveClass) + \" \" + (params.slideDuplicateNextClass) + \" \" + (params.slideDuplicatePrevClass)));\n\n    var activeSlide;\n    if (isVirtual) {\n      activeSlide = swiper.$wrapperEl.find((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + activeIndex + \"\\\"]\"));\n    } else {\n      activeSlide = slides.eq(activeIndex);\n    }\n\n    // Active classes\n    activeSlide.addClass(params.slideActiveClass);\n\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (activeSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      }\n    }\n    // Next Slide\n    var nextSlide = activeSlide.nextAll((\".\" + (params.slideClass))).eq(0).addClass(params.slideNextClass);\n    if (params.loop && nextSlide.length === 0) {\n      nextSlide = slides.eq(0);\n      nextSlide.addClass(params.slideNextClass);\n    }\n    // Prev Slide\n    var prevSlide = activeSlide.prevAll((\".\" + (params.slideClass))).eq(0).addClass(params.slidePrevClass);\n    if (params.loop && prevSlide.length === 0) {\n      prevSlide = slides.eq(-1);\n      prevSlide.addClass(params.slidePrevClass);\n    }\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (nextSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      }\n      if (prevSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      }\n    }\n  }\n\n  function updateActiveIndex (newActiveIndex) {\n    var swiper = this;\n    var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var params = swiper.params;\n    var previousIndex = swiper.activeIndex;\n    var previousRealIndex = swiper.realIndex;\n    var previousSnapIndex = swiper.snapIndex;\n    var activeIndex = newActiveIndex;\n    var snapIndex;\n    if (typeof activeIndex === 'undefined') {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (typeof slidesGrid[i + 1] !== 'undefined') {\n          if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - ((slidesGrid[i + 1] - slidesGrid[i]) / 2)) {\n            activeIndex = i;\n          } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n            activeIndex = i + 1;\n          }\n        } else if (translate >= slidesGrid[i]) {\n          activeIndex = i;\n        }\n      }\n      // Normalize slideIndex\n      if (params.normalizeSlideIndex) {\n        if (activeIndex < 0 || typeof activeIndex === 'undefined') { activeIndex = 0; }\n      }\n    }\n    if (snapGrid.indexOf(translate) >= 0) {\n      snapIndex = snapGrid.indexOf(translate);\n    } else {\n      snapIndex = Math.floor(activeIndex / params.slidesPerGroup);\n    }\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n    if (activeIndex === previousIndex) {\n      if (snapIndex !== previousSnapIndex) {\n        swiper.snapIndex = snapIndex;\n        swiper.emit('snapIndexChange');\n      }\n      return;\n    }\n\n    // Get real index\n    var realIndex = parseInt(swiper.slides.eq(activeIndex).attr('data-swiper-slide-index') || activeIndex, 10);\n\n    Utils.extend(swiper, {\n      snapIndex: snapIndex,\n      realIndex: realIndex,\n      previousIndex: previousIndex,\n      activeIndex: activeIndex,\n    });\n    swiper.emit('activeIndexChange');\n    swiper.emit('snapIndexChange');\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n\n  function updateClickedSlide (e) {\n    var swiper = this;\n    var params = swiper.params;\n    var slide = $(e.target).closest((\".\" + (params.slideClass)))[0];\n    var slideFound = false;\n    if (slide) {\n      for (var i = 0; i < swiper.slides.length; i += 1) {\n        if (swiper.slides[i] === slide) { slideFound = true; }\n      }\n    }\n\n    if (slide && slideFound) {\n      swiper.clickedSlide = slide;\n      if (swiper.virtual && swiper.params.virtual.enabled) {\n        swiper.clickedIndex = parseInt($(slide).attr('data-swiper-slide-index'), 10);\n      } else {\n        swiper.clickedIndex = $(slide).index();\n      }\n    } else {\n      swiper.clickedSlide = undefined;\n      swiper.clickedIndex = undefined;\n      return;\n    }\n    if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n      swiper.slideToClickedSlide();\n    }\n  }\n\n  var update = {\n    updateSize: updateSize,\n    updateSlides: updateSlides,\n    updateAutoHeight: updateAutoHeight,\n    updateSlidesOffset: updateSlidesOffset,\n    updateSlidesProgress: updateSlidesProgress,\n    updateProgress: updateProgress,\n    updateSlidesClasses: updateSlidesClasses,\n    updateActiveIndex: updateActiveIndex,\n    updateClickedSlide: updateClickedSlide,\n  };\n\n  function getTranslate (axis) {\n    if ( axis === void 0 ) axis = this.isHorizontal() ? 'x' : 'y';\n\n    var swiper = this;\n\n    var params = swiper.params;\n    var rtl = swiper.rtlTranslate;\n    var translate = swiper.translate;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    if (params.virtualTranslate) {\n      return rtl ? -translate : translate;\n    }\n\n    var currentTranslate = Utils.getTranslate($wrapperEl[0], axis);\n    if (rtl) { currentTranslate = -currentTranslate; }\n\n    return currentTranslate || 0;\n  }\n\n  function setTranslate (translate, byController) {\n    var swiper = this;\n    var rtl = swiper.rtlTranslate;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var progress = swiper.progress;\n    var x = 0;\n    var y = 0;\n    var z = 0;\n\n    if (swiper.isHorizontal()) {\n      x = rtl ? -translate : translate;\n    } else {\n      y = translate;\n    }\n\n    if (params.roundLengths) {\n      x = Math.floor(x);\n      y = Math.floor(y);\n    }\n\n    if (!params.virtualTranslate) {\n      if (Support.transforms3d) { $wrapperEl.transform((\"translate3d(\" + x + \"px, \" + y + \"px, \" + z + \"px)\")); }\n      else { $wrapperEl.transform((\"translate(\" + x + \"px, \" + y + \"px)\")); }\n    }\n    swiper.previousTranslate = swiper.translate;\n    swiper.translate = swiper.isHorizontal() ? x : y;\n\n    // Check if we need to update progress\n    var newProgress;\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    if (translatesDiff === 0) {\n      newProgress = 0;\n    } else {\n      newProgress = (translate - swiper.minTranslate()) / (translatesDiff);\n    }\n    if (newProgress !== progress) {\n      swiper.updateProgress(translate);\n    }\n\n    swiper.emit('setTranslate', swiper.translate, byController);\n  }\n\n  function minTranslate () {\n    return (-this.snapGrid[0]);\n  }\n\n  function maxTranslate () {\n    return (-this.snapGrid[this.snapGrid.length - 1]);\n  }\n\n  var translate = {\n    getTranslate: getTranslate,\n    setTranslate: setTranslate,\n    minTranslate: minTranslate,\n    maxTranslate: maxTranslate,\n  };\n\n  function setTransition (duration, byController) {\n    var swiper = this;\n\n    swiper.$wrapperEl.transition(duration);\n\n    swiper.emit('setTransition', duration, byController);\n  }\n\n  function transitionStart (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var params = swiper.params;\n    var previousIndex = swiper.previousIndex;\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionStart');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionStart');\n        return;\n      }\n      swiper.emit('slideChangeTransitionStart');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionStart');\n      } else {\n        swiper.emit('slidePrevTransitionStart');\n      }\n    }\n  }\n\n  function transitionEnd$1 (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var previousIndex = swiper.previousIndex;\n    swiper.animating = false;\n    swiper.setTransition(0);\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionEnd');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionEnd');\n        return;\n      }\n      swiper.emit('slideChangeTransitionEnd');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionEnd');\n      } else {\n        swiper.emit('slidePrevTransitionEnd');\n      }\n    }\n  }\n\n  var transition$1 = {\n    setTransition: setTransition,\n    transitionStart: transitionStart,\n    transitionEnd: transitionEnd$1,\n  };\n\n  function slideTo (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var slideIndex = index;\n    if (slideIndex < 0) { slideIndex = 0; }\n\n    var params = swiper.params;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var previousIndex = swiper.previousIndex;\n    var activeIndex = swiper.activeIndex;\n    var rtl = swiper.rtlTranslate;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return false;\n    }\n\n    var snapIndex = Math.floor(slideIndex / params.slidesPerGroup);\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n\n    if ((activeIndex || params.initialSlide || 0) === (previousIndex || 0) && runCallbacks) {\n      swiper.emit('beforeSlideChangeStart');\n    }\n\n    var translate = -snapGrid[snapIndex];\n\n    // Update progress\n    swiper.updateProgress(translate);\n\n    // Normalize slideIndex\n    if (params.normalizeSlideIndex) {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (-Math.floor(translate * 100) >= Math.floor(slidesGrid[i] * 100)) {\n          slideIndex = i;\n        }\n      }\n    }\n    // Directions locks\n    if (swiper.initialized && slideIndex !== activeIndex) {\n      if (!swiper.allowSlideNext && translate < swiper.translate && translate < swiper.minTranslate()) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n        if ((activeIndex || 0) !== slideIndex) { return false; }\n      }\n    }\n\n    var direction;\n    if (slideIndex > activeIndex) { direction = 'next'; }\n    else if (slideIndex < activeIndex) { direction = 'prev'; }\n    else { direction = 'reset'; }\n\n\n    // Update Index\n    if ((rtl && -translate === swiper.translate) || (!rtl && translate === swiper.translate)) {\n      swiper.updateActiveIndex(slideIndex);\n      // Update Height\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n      swiper.updateSlidesClasses();\n      if (params.effect !== 'slide') {\n        swiper.setTranslate(translate);\n      }\n      if (direction !== 'reset') {\n        swiper.transitionStart(runCallbacks, direction);\n        swiper.transitionEnd(runCallbacks, direction);\n      }\n      return false;\n    }\n\n    if (speed === 0 || !Support.transition) {\n      swiper.setTransition(0);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    } else {\n      swiper.setTransition(speed);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      if (!swiper.animating) {\n        swiper.animating = true;\n        if (!swiper.onSlideToWrapperTransitionEnd) {\n          swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n            if (!swiper || swiper.destroyed) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n            swiper.onSlideToWrapperTransitionEnd = null;\n            delete swiper.onSlideToWrapperTransitionEnd;\n            swiper.transitionEnd(runCallbacks, direction);\n          };\n        }\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n      }\n    }\n\n    return true;\n  }\n\n  function slideToLoop (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var newIndex = index;\n    if (swiper.params.loop) {\n      newIndex += swiper.loopedSlides;\n    }\n\n    return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideNext (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n      return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n    }\n    return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slidePrev (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var rtlTranslate = swiper.rtlTranslate;\n\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n    }\n    var translate = rtlTranslate ? swiper.translate : -swiper.translate;\n    function normalize(val) {\n      if (val < 0) { return -Math.floor(Math.abs(val)); }\n      return Math.floor(val);\n    }\n    var normalizedTranslate = normalize(translate);\n    var normalizedSnapGrid = snapGrid.map(function (val) { return normalize(val); });\n    var normalizedSlidesGrid = slidesGrid.map(function (val) { return normalize(val); });\n\n    var currentSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate)];\n    var prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n    var prevIndex;\n    if (typeof prevSnap !== 'undefined') {\n      prevIndex = slidesGrid.indexOf(prevSnap);\n      if (prevIndex < 0) { prevIndex = swiper.activeIndex - 1; }\n    }\n    return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideReset (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideToClosest (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var index = swiper.activeIndex;\n    var snapIndex = Math.floor(index / swiper.params.slidesPerGroup);\n\n    if (snapIndex < swiper.snapGrid.length - 1) {\n      var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n      var currentSnap = swiper.snapGrid[snapIndex];\n      var nextSnap = swiper.snapGrid[snapIndex + 1];\n\n      if ((translate - currentSnap) > (nextSnap - currentSnap) / 2) {\n        index = swiper.params.slidesPerGroup;\n      }\n    }\n\n    return swiper.slideTo(index, speed, runCallbacks, internal);\n  }\n\n  function slideToClickedSlide () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    var slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n    var slideToIndex = swiper.clickedIndex;\n    var realIndex;\n    if (params.loop) {\n      if (swiper.animating) { return; }\n      realIndex = parseInt($(swiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n      if (params.centeredSlides) {\n        if (\n          (slideToIndex < swiper.loopedSlides - (slidesPerView / 2))\n          || (slideToIndex > (swiper.slides.length - swiper.loopedSlides) + (slidesPerView / 2))\n        ) {\n          swiper.loopFix();\n          slideToIndex = $wrapperEl\n            .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n            .eq(0)\n            .index();\n\n          Utils.nextTick(function () {\n            swiper.slideTo(slideToIndex);\n          });\n        } else {\n          swiper.slideTo(slideToIndex);\n        }\n      } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n        swiper.loopFix();\n        slideToIndex = $wrapperEl\n          .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n          .eq(0)\n          .index();\n\n        Utils.nextTick(function () {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n\n  var slide = {\n    slideTo: slideTo,\n    slideToLoop: slideToLoop,\n    slideNext: slideNext,\n    slidePrev: slidePrev,\n    slideReset: slideReset,\n    slideToClosest: slideToClosest,\n    slideToClickedSlide: slideToClickedSlide,\n  };\n\n  function loopCreate () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    // Remove duplicated slides\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass))).remove();\n\n    var slides = $wrapperEl.children((\".\" + (params.slideClass)));\n\n    if (params.loopFillGroupWithBlank) {\n      var blankSlidesNum = params.slidesPerGroup - (slides.length % params.slidesPerGroup);\n      if (blankSlidesNum !== params.slidesPerGroup) {\n        for (var i = 0; i < blankSlidesNum; i += 1) {\n          var blankNode = $(doc.createElement('div')).addClass(((params.slideClass) + \" \" + (params.slideBlankClass)));\n          $wrapperEl.append(blankNode);\n        }\n        slides = $wrapperEl.children((\".\" + (params.slideClass)));\n      }\n    }\n\n    if (params.slidesPerView === 'auto' && !params.loopedSlides) { params.loopedSlides = slides.length; }\n\n    swiper.loopedSlides = parseInt(params.loopedSlides || params.slidesPerView, 10);\n    swiper.loopedSlides += params.loopAdditionalSlides;\n    if (swiper.loopedSlides > slides.length) {\n      swiper.loopedSlides = slides.length;\n    }\n\n    var prependSlides = [];\n    var appendSlides = [];\n    slides.each(function (index, el) {\n      var slide = $(el);\n      if (index < swiper.loopedSlides) { appendSlides.push(el); }\n      if (index < slides.length && index >= slides.length - swiper.loopedSlides) { prependSlides.push(el); }\n      slide.attr('data-swiper-slide-index', index);\n    });\n    for (var i$1 = 0; i$1 < appendSlides.length; i$1 += 1) {\n      $wrapperEl.append($(appendSlides[i$1].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n    for (var i$2 = prependSlides.length - 1; i$2 >= 0; i$2 -= 1) {\n      $wrapperEl.prepend($(prependSlides[i$2].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n  }\n\n  function loopFix () {\n    var swiper = this;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var slides = swiper.slides;\n    var loopedSlides = swiper.loopedSlides;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var allowSlideNext = swiper.allowSlideNext;\n    var snapGrid = swiper.snapGrid;\n    var rtl = swiper.rtlTranslate;\n    var newIndex;\n    swiper.allowSlidePrev = true;\n    swiper.allowSlideNext = true;\n\n    var snapTranslate = -snapGrid[activeIndex];\n    var diff = snapTranslate - swiper.getTranslate();\n\n\n    // Fix For Negative Oversliding\n    if (activeIndex < loopedSlides) {\n      newIndex = (slides.length - (loopedSlides * 3)) + activeIndex;\n      newIndex += loopedSlides;\n      var slideChanged = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    } else if ((params.slidesPerView === 'auto' && activeIndex >= loopedSlides * 2) || (activeIndex >= slides.length - loopedSlides)) {\n      // Fix For Positive Oversliding\n      newIndex = -slides.length + activeIndex + loopedSlides;\n      newIndex += loopedSlides;\n      var slideChanged$1 = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged$1 && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n  }\n\n  function loopDestroy () {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var slides = swiper.slides;\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass))).remove();\n    slides.removeAttr('data-swiper-slide-index');\n  }\n\n  var loop = {\n    loopCreate: loopCreate,\n    loopFix: loopFix,\n    loopDestroy: loopDestroy,\n  };\n\n  function setGrabCursor (moving) {\n    var swiper = this;\n    if (Support.touch || !swiper.params.simulateTouch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    var el = swiper.el;\n    el.style.cursor = 'move';\n    el.style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n    el.style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n    el.style.cursor = moving ? 'grabbing' : 'grab';\n  }\n\n  function unsetGrabCursor () {\n    var swiper = this;\n    if (Support.touch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    swiper.el.style.cursor = '';\n  }\n\n  var grabCursor = {\n    setGrabCursor: setGrabCursor,\n    unsetGrabCursor: unsetGrabCursor,\n  };\n\n  function appendSlide (slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.append(slides[i]); }\n      }\n    } else {\n      $wrapperEl.append(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n  }\n\n  function prependSlide (slides) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    var newActiveIndex = activeIndex + 1;\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.prepend(slides[i]); }\n      }\n      newActiveIndex = activeIndex + slides.length;\n    } else {\n      $wrapperEl.prepend(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n\n  function addSlide (index, slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var baseLength = swiper.slides.length;\n    if (index <= 0) {\n      swiper.prependSlide(slides);\n      return;\n    }\n    if (index >= baseLength) {\n      swiper.appendSlide(slides);\n      return;\n    }\n    var newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n\n    var slidesBuffer = [];\n    for (var i = baseLength - 1; i >= index; i -= 1) {\n      var currentSlide = swiper.slides.eq(i);\n      currentSlide.remove();\n      slidesBuffer.unshift(currentSlide);\n    }\n\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (slides[i$1]) { $wrapperEl.append(slides[i$1]); }\n      }\n      newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n    } else {\n      $wrapperEl.append(slides);\n    }\n\n    for (var i$2 = 0; i$2 < slidesBuffer.length; i$2 += 1) {\n      $wrapperEl.append(slidesBuffer[i$2]);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeSlide (slidesIndexes) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var newActiveIndex = activeIndexBuffer;\n    var indexToRemove;\n\n    if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n      for (var i = 0; i < slidesIndexes.length; i += 1) {\n        indexToRemove = slidesIndexes[i];\n        if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n        if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    } else {\n      indexToRemove = slidesIndexes;\n      if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n      if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeAllSlides () {\n    var swiper = this;\n\n    var slidesIndexes = [];\n    for (var i = 0; i < swiper.slides.length; i += 1) {\n      slidesIndexes.push(i);\n    }\n    swiper.removeSlide(slidesIndexes);\n  }\n\n  var manipulation = {\n    appendSlide: appendSlide,\n    prependSlide: prependSlide,\n    addSlide: addSlide,\n    removeSlide: removeSlide,\n    removeAllSlides: removeAllSlides,\n  };\n\n  var Device = (function Device() {\n    var ua = win.navigator.userAgent;\n\n    var device = {\n      ios: false,\n      android: false,\n      androidChrome: false,\n      desktop: false,\n      windows: false,\n      iphone: false,\n      ipod: false,\n      ipad: false,\n      cordova: win.cordova || win.phonegap,\n      phonegap: win.cordova || win.phonegap,\n    };\n\n    var windows = ua.match(/(Windows Phone);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n    var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n    var iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n\n\n    // Windows\n    if (windows) {\n      device.os = 'windows';\n      device.osVersion = windows[2];\n      device.windows = true;\n    }\n    // Android\n    if (android && !windows) {\n      device.os = 'android';\n      device.osVersion = android[2];\n      device.android = true;\n      device.androidChrome = ua.toLowerCase().indexOf('chrome') >= 0;\n    }\n    if (ipad || iphone || ipod) {\n      device.os = 'ios';\n      device.ios = true;\n    }\n    // iOS\n    if (iphone && !ipod) {\n      device.osVersion = iphone[2].replace(/_/g, '.');\n      device.iphone = true;\n    }\n    if (ipad) {\n      device.osVersion = ipad[2].replace(/_/g, '.');\n      device.ipad = true;\n    }\n    if (ipod) {\n      device.osVersion = ipod[3] ? ipod[3].replace(/_/g, '.') : null;\n      device.iphone = true;\n    }\n    // iOS 8+ changed UA\n    if (device.ios && device.osVersion && ua.indexOf('Version/') >= 0) {\n      if (device.osVersion.split('.')[0] === '10') {\n        device.osVersion = ua.toLowerCase().split('version/')[1].split(' ')[0];\n      }\n    }\n\n    // Desktop\n    device.desktop = !(device.os || device.android || device.webView);\n\n    // Webview\n    device.webView = (iphone || ipad || ipod) && ua.match(/.*AppleWebKit(?!.*Safari)/i);\n\n    // Minimal UI\n    if (device.os && device.os === 'ios') {\n      var osVersionArr = device.osVersion.split('.');\n      var metaViewport = doc.querySelector('meta[name=\"viewport\"]');\n      device.minimalUi = !device.webView\n        && (ipod || iphone)\n        && (osVersionArr[0] * 1 === 7 ? osVersionArr[1] * 1 >= 1 : osVersionArr[0] * 1 > 7)\n        && metaViewport && metaViewport.getAttribute('content').indexOf('minimal-ui') >= 0;\n    }\n\n    // Pixel Ratio\n    device.pixelRatio = win.devicePixelRatio || 1;\n\n    // Export object\n    return device;\n  }());\n\n  function onTouchStart (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return;\n    }\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    data.isTouchEvent = e.type === 'touchstart';\n    if (!data.isTouchEvent && 'which' in e && e.which === 3) { return; }\n    if (!data.isTouchEvent && 'button' in e && e.button > 0) { return; }\n    if (data.isTouched && data.isMoved) { return; }\n    if (params.noSwiping && $(e.target).closest(params.noSwipingSelector ? params.noSwipingSelector : (\".\" + (params.noSwipingClass)))[0]) {\n      swiper.allowClick = true;\n      return;\n    }\n    if (params.swipeHandler) {\n      if (!$(e).closest(params.swipeHandler)[0]) { return; }\n    }\n\n    touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n    touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    var startX = touches.currentX;\n    var startY = touches.currentY;\n\n    // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n\n    var edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n    var edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n    if (\n      edgeSwipeDetection\n      && ((startX <= edgeSwipeThreshold)\n      || (startX >= win.screen.width - edgeSwipeThreshold))\n    ) {\n      return;\n    }\n\n    Utils.extend(data, {\n      isTouched: true,\n      isMoved: false,\n      allowTouchCallbacks: true,\n      isScrolling: undefined,\n      startMoving: undefined,\n    });\n\n    touches.startX = startX;\n    touches.startY = startY;\n    data.touchStartTime = Utils.now();\n    swiper.allowClick = true;\n    swiper.updateSize();\n    swiper.swipeDirection = undefined;\n    if (params.threshold > 0) { data.allowThresholdMove = false; }\n    if (e.type !== 'touchstart') {\n      var preventDefault = true;\n      if ($(e.target).is(data.formElements)) { preventDefault = false; }\n      if (\n        doc.activeElement\n        && $(doc.activeElement).is(data.formElements)\n        && doc.activeElement !== e.target\n      ) {\n        doc.activeElement.blur();\n      }\n      if (preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault) {\n        e.preventDefault();\n      }\n    }\n    swiper.emit('touchStart', e);\n  }\n\n  function onTouchMove (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (!data.isTouched) {\n      if (data.startMoving && data.isScrolling) {\n        swiper.emit('touchMoveOpposite', e);\n      }\n      return;\n    }\n    if (data.isTouchEvent && e.type === 'mousemove') { return; }\n    var pageX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n    var pageY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n    if (e.preventedByNestedSwiper) {\n      touches.startX = pageX;\n      touches.startY = pageY;\n      return;\n    }\n    if (!swiper.allowTouchMove) {\n      // isMoved = true;\n      swiper.allowClick = false;\n      if (data.isTouched) {\n        Utils.extend(touches, {\n          startX: pageX,\n          startY: pageY,\n          currentX: pageX,\n          currentY: pageY,\n        });\n        data.touchStartTime = Utils.now();\n      }\n      return;\n    }\n    if (data.isTouchEvent && params.touchReleaseOnEdges && !params.loop) {\n      if (swiper.isVertical()) {\n        // Vertical\n        if (\n          (pageY < touches.startY && swiper.translate <= swiper.maxTranslate())\n          || (pageY > touches.startY && swiper.translate >= swiper.minTranslate())\n        ) {\n          data.isTouched = false;\n          data.isMoved = false;\n          return;\n        }\n      } else if (\n        (pageX < touches.startX && swiper.translate <= swiper.maxTranslate())\n        || (pageX > touches.startX && swiper.translate >= swiper.minTranslate())\n      ) {\n        return;\n      }\n    }\n    if (data.isTouchEvent && doc.activeElement) {\n      if (e.target === doc.activeElement && $(e.target).is(data.formElements)) {\n        data.isMoved = true;\n        swiper.allowClick = false;\n        return;\n      }\n    }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchMove', e);\n    }\n    if (e.targetTouches && e.targetTouches.length > 1) { return; }\n\n    touches.currentX = pageX;\n    touches.currentY = pageY;\n\n    var diffX = touches.currentX - touches.startX;\n    var diffY = touches.currentY - touches.startY;\n    if (swiper.params.threshold && Math.sqrt((Math.pow( diffX, 2 )) + (Math.pow( diffY, 2 ))) < swiper.params.threshold) { return; }\n\n    if (typeof data.isScrolling === 'undefined') {\n      var touchAngle;\n      if ((swiper.isHorizontal() && touches.currentY === touches.startY) || (swiper.isVertical() && touches.currentX === touches.startX)) {\n        data.isScrolling = false;\n      } else {\n        // eslint-disable-next-line\n        if ((diffX * diffX) + (diffY * diffY) >= 25) {\n          touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n          data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : (90 - touchAngle > params.touchAngle);\n        }\n      }\n    }\n    if (data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    if (typeof data.startMoving === 'undefined') {\n      if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n        data.startMoving = true;\n      }\n    }\n    if (data.isScrolling) {\n      data.isTouched = false;\n      return;\n    }\n    if (!data.startMoving) {\n      return;\n    }\n    swiper.allowClick = false;\n    e.preventDefault();\n    if (params.touchMoveStopPropagation && !params.nested) {\n      e.stopPropagation();\n    }\n\n    if (!data.isMoved) {\n      if (params.loop) {\n        swiper.loopFix();\n      }\n      data.startTranslate = swiper.getTranslate();\n      swiper.setTransition(0);\n      if (swiper.animating) {\n        swiper.$wrapperEl.trigger('webkitTransitionEnd transitionend');\n      }\n      data.allowMomentumBounce = false;\n      // Grab Cursor\n      if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n        swiper.setGrabCursor(true);\n      }\n      swiper.emit('sliderFirstMove', e);\n    }\n    swiper.emit('sliderMove', e);\n    data.isMoved = true;\n\n    var diff = swiper.isHorizontal() ? diffX : diffY;\n    touches.diff = diff;\n\n    diff *= params.touchRatio;\n    if (rtl) { diff = -diff; }\n\n    swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n    data.currentTranslate = diff + data.startTranslate;\n\n    var disableParentSwiper = true;\n    var resistanceRatio = params.resistanceRatio;\n    if (params.touchReleaseOnEdges) {\n      resistanceRatio = 0;\n    }\n    if ((diff > 0 && data.currentTranslate > swiper.minTranslate())) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.minTranslate() - 1) + (Math.pow( (-swiper.minTranslate() + data.startTranslate + diff), resistanceRatio )); }\n    } else if (diff < 0 && data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.maxTranslate() + 1) - (Math.pow( (swiper.maxTranslate() - data.startTranslate - diff), resistanceRatio )); }\n    }\n\n    if (disableParentSwiper) {\n      e.preventedByNestedSwiper = true;\n    }\n\n    // Directions locks\n    if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n    if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n\n\n    // Threshold\n    if (params.threshold > 0) {\n      if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n        if (!data.allowThresholdMove) {\n          data.allowThresholdMove = true;\n          touches.startX = touches.currentX;\n          touches.startY = touches.currentY;\n          data.currentTranslate = data.startTranslate;\n          touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n          return;\n        }\n      } else {\n        data.currentTranslate = data.startTranslate;\n        return;\n      }\n    }\n\n    if (!params.followFinger) { return; }\n\n    // Update active index in free mode\n    if (params.freeMode || params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    if (params.freeMode) {\n      // Velocity\n      if (data.velocities.length === 0) {\n        data.velocities.push({\n          position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n          time: data.touchStartTime,\n        });\n      }\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n        time: Utils.now(),\n      });\n    }\n    // Update progress\n    swiper.updateProgress(data.currentTranslate);\n    // Update translate\n    swiper.setTranslate(data.currentTranslate);\n  }\n\n  function onTouchEnd (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var $wrapperEl = swiper.$wrapperEl;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchEnd', e);\n    }\n    data.allowTouchCallbacks = false;\n    if (!data.isTouched) {\n      if (data.isMoved && params.grabCursor) {\n        swiper.setGrabCursor(false);\n      }\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    // Return Grab Cursor\n    if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(false);\n    }\n\n    // Time diff\n    var touchEndTime = Utils.now();\n    var timeDiff = touchEndTime - data.touchStartTime;\n\n    // Tap, doubleTap, Click\n    if (swiper.allowClick) {\n      swiper.updateClickedSlide(e);\n      swiper.emit('tap', e);\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) > 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        data.clickTimeout = Utils.nextTick(function () {\n          if (!swiper || swiper.destroyed) { return; }\n          swiper.emit('click', e);\n        }, 300);\n      }\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) < 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        swiper.emit('doubleTap', e);\n      }\n    }\n\n    data.lastClickTime = Utils.now();\n    Utils.nextTick(function () {\n      if (!swiper.destroyed) { swiper.allowClick = true; }\n    });\n\n    if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n      data.isTouched = false;\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n\n    var currentPos;\n    if (params.followFinger) {\n      currentPos = rtl ? swiper.translate : -swiper.translate;\n    } else {\n      currentPos = -data.currentTranslate;\n    }\n\n    if (params.freeMode) {\n      if (currentPos < -swiper.minTranslate()) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (currentPos > -swiper.maxTranslate()) {\n        if (swiper.slides.length < snapGrid.length) {\n          swiper.slideTo(snapGrid.length - 1);\n        } else {\n          swiper.slideTo(swiper.slides.length - 1);\n        }\n        return;\n      }\n\n      if (params.freeModeMomentum) {\n        if (data.velocities.length > 1) {\n          var lastMoveEvent = data.velocities.pop();\n          var velocityEvent = data.velocities.pop();\n\n          var distance = lastMoveEvent.position - velocityEvent.position;\n          var time = lastMoveEvent.time - velocityEvent.time;\n          swiper.velocity = distance / time;\n          swiper.velocity /= 2;\n          if (Math.abs(swiper.velocity) < params.freeModeMinimumVelocity) {\n            swiper.velocity = 0;\n          }\n          // this implies that the user stopped moving a finger then released.\n          // There would be no events with distance zero, so the last event is stale.\n          if (time > 150 || (Utils.now() - lastMoveEvent.time) > 300) {\n            swiper.velocity = 0;\n          }\n        } else {\n          swiper.velocity = 0;\n        }\n        swiper.velocity *= params.freeModeMomentumVelocityRatio;\n\n        data.velocities.length = 0;\n        var momentumDuration = 1000 * params.freeModeMomentumRatio;\n        var momentumDistance = swiper.velocity * momentumDuration;\n\n        var newPosition = swiper.translate + momentumDistance;\n        if (rtl) { newPosition = -newPosition; }\n\n        var doBounce = false;\n        var afterBouncePosition;\n        var bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeModeMomentumBounceRatio;\n        var needsLoopFix;\n        if (newPosition < swiper.maxTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n              newPosition = swiper.maxTranslate() - bounceAmount;\n            }\n            afterBouncePosition = swiper.maxTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.maxTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (newPosition > swiper.minTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition - swiper.minTranslate() > bounceAmount) {\n              newPosition = swiper.minTranslate() + bounceAmount;\n            }\n            afterBouncePosition = swiper.minTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.minTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (params.freeModeSticky) {\n          var nextSlide;\n          for (var j = 0; j < snapGrid.length; j += 1) {\n            if (snapGrid[j] > -newPosition) {\n              nextSlide = j;\n              break;\n            }\n          }\n\n          if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n            newPosition = snapGrid[nextSlide];\n          } else {\n            newPosition = snapGrid[nextSlide - 1];\n          }\n          newPosition = -newPosition;\n        }\n        if (needsLoopFix) {\n          swiper.once('transitionEnd', function () {\n            swiper.loopFix();\n          });\n        }\n        // Fix duration\n        if (swiper.velocity !== 0) {\n          if (rtl) {\n            momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n          } else {\n            momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n          }\n        } else if (params.freeModeSticky) {\n          swiper.slideToClosest();\n          return;\n        }\n\n        if (params.freeModeMomentumBounce && doBounce) {\n          swiper.updateProgress(afterBouncePosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          swiper.animating = true;\n          $wrapperEl.transitionEnd(function () {\n            if (!swiper || swiper.destroyed || !data.allowMomentumBounce) { return; }\n            swiper.emit('momentumBounce');\n\n            swiper.setTransition(params.speed);\n            swiper.setTranslate(afterBouncePosition);\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          });\n        } else if (swiper.velocity) {\n          swiper.updateProgress(newPosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          if (!swiper.animating) {\n            swiper.animating = true;\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          }\n        } else {\n          swiper.updateProgress(newPosition);\n        }\n\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      } else if (params.freeModeSticky) {\n        swiper.slideToClosest();\n        return;\n      }\n\n      if (!params.freeModeMomentum || timeDiff >= params.longSwipesMs) {\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      return;\n    }\n\n    // Find current slide\n    var stopIndex = 0;\n    var groupSize = swiper.slidesSizesGrid[0];\n    for (var i = 0; i < slidesGrid.length; i += params.slidesPerGroup) {\n      if (typeof slidesGrid[i + params.slidesPerGroup] !== 'undefined') {\n        if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + params.slidesPerGroup]) {\n          stopIndex = i;\n          groupSize = slidesGrid[i + params.slidesPerGroup] - slidesGrid[i];\n        }\n      } else if (currentPos >= slidesGrid[i]) {\n        stopIndex = i;\n        groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n      }\n    }\n\n    // Find current slide size\n    var ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n\n    if (timeDiff > params.longSwipesMs) {\n      // Long touches\n      if (!params.longSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        if (ratio >= params.longSwipesRatio) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n      if (swiper.swipeDirection === 'prev') {\n        if (ratio > (1 - params.longSwipesRatio)) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n    } else {\n      // Short swipes\n      if (!params.shortSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(stopIndex + params.slidesPerGroup);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  }\n\n  function onResize () {\n    var swiper = this;\n\n    var params = swiper.params;\n    var el = swiper.el;\n\n    if (el && el.offsetWidth === 0) { return; }\n\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Save locks\n    var allowSlideNext = swiper.allowSlideNext;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var snapGrid = swiper.snapGrid;\n\n    // Disable locks on resize\n    swiper.allowSlideNext = true;\n    swiper.allowSlidePrev = true;\n\n    swiper.updateSize();\n    swiper.updateSlides();\n\n    if (params.freeMode) {\n      var newTranslate = Math.min(Math.max(swiper.translate, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      swiper.updateSlidesClasses();\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n        swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n    }\n    // Return locks after resize\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n\n    if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n  }\n\n  function onClick (e) {\n    var swiper = this;\n    if (!swiper.allowClick) {\n      if (swiper.params.preventClicks) { e.preventDefault(); }\n      if (swiper.params.preventClicksPropagation && swiper.animating) {\n        e.stopPropagation();\n        e.stopImmediatePropagation();\n      }\n    }\n  }\n\n  function attachEvents() {\n    var swiper = this;\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    {\n      swiper.onTouchStart = onTouchStart.bind(swiper);\n      swiper.onTouchMove = onTouchMove.bind(swiper);\n      swiper.onTouchEnd = onTouchEnd.bind(swiper);\n    }\n\n    swiper.onClick = onClick.bind(swiper);\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.addEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.addEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.addEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'touchstart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.addEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.addEventListener(touchEvents.move, swiper.onTouchMove, Support.passiveListener ? { passive: false, capture: capture } : capture);\n          target.addEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.addEventListener('mousedown', swiper.onTouchStart, false);\n          doc.addEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.addEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.addEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.on((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize, true);\n  }\n\n  function detachEvents() {\n    var swiper = this;\n\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.removeEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.removeEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'onTouchStart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.removeEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n          target.removeEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.removeEventListener('mousedown', swiper.onTouchStart, false);\n          doc.removeEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.removeEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.removeEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.off((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize);\n  }\n\n  var events = {\n    attachEvents: attachEvents,\n    detachEvents: detachEvents,\n  };\n\n  function setBreakpoint () {\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var initialized = swiper.initialized;\n    var loopedSlides = swiper.loopedSlides; if ( loopedSlides === void 0 ) loopedSlides = 0;\n    var params = swiper.params;\n    var breakpoints = params.breakpoints;\n    if (!breakpoints || (breakpoints && Object.keys(breakpoints).length === 0)) { return; }\n    // Set breakpoint for window width and update parameters\n    var breakpoint = swiper.getBreakpoint(breakpoints);\n    if (breakpoint && swiper.currentBreakpoint !== breakpoint) {\n      var breakPointsParams = breakpoint in breakpoints ? breakpoints[breakpoint] : swiper.originalParams;\n      var needsReLoop = params.loop && (breakPointsParams.slidesPerView !== params.slidesPerView);\n\n      Utils.extend(swiper.params, breakPointsParams);\n\n      Utils.extend(swiper, {\n        allowTouchMove: swiper.params.allowTouchMove,\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n      });\n\n      swiper.currentBreakpoint = breakpoint;\n\n      if (needsReLoop && initialized) {\n        swiper.loopDestroy();\n        swiper.loopCreate();\n        swiper.updateSlides();\n        swiper.slideTo((activeIndex - loopedSlides) + swiper.loopedSlides, 0, false);\n      }\n      swiper.emit('breakpoint', breakPointsParams);\n    }\n  }\n\n  function getBreakpoint (breakpoints) {\n    var swiper = this;\n    // Get breakpoint for window width\n    if (!breakpoints) { return undefined; }\n    var breakpoint = false;\n    var points = [];\n    Object.keys(breakpoints).forEach(function (point) {\n      points.push(point);\n    });\n    points.sort(function (a, b) { return parseInt(a, 10) - parseInt(b, 10); });\n    for (var i = 0; i < points.length; i += 1) {\n      var point = points[i];\n      if (swiper.params.breakpointsInverse) {\n        if (point <= win.innerWidth) {\n          breakpoint = point;\n        }\n      } else if (point >= win.innerWidth && !breakpoint) {\n        breakpoint = point;\n      }\n    }\n    return breakpoint || 'max';\n  }\n\n  var breakpoints = { setBreakpoint: setBreakpoint, getBreakpoint: getBreakpoint };\n\n  var Browser = (function Browser() {\n    function isSafari() {\n      var ua = win.navigator.userAgent.toLowerCase();\n      return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n    }\n    return {\n      isIE: !!win.navigator.userAgent.match(/Trident/g) || !!win.navigator.userAgent.match(/MSIE/g),\n      isEdge: !!win.navigator.userAgent.match(/Edge/g),\n      isSafari: isSafari(),\n      isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(win.navigator.userAgent),\n    };\n  }());\n\n  function addClasses () {\n    var swiper = this;\n    var classNames = swiper.classNames;\n    var params = swiper.params;\n    var rtl = swiper.rtl;\n    var $el = swiper.$el;\n    var suffixes = [];\n\n    suffixes.push(params.direction);\n\n    if (params.freeMode) {\n      suffixes.push('free-mode');\n    }\n    if (!Support.flexbox) {\n      suffixes.push('no-flexbox');\n    }\n    if (params.autoHeight) {\n      suffixes.push('autoheight');\n    }\n    if (rtl) {\n      suffixes.push('rtl');\n    }\n    if (params.slidesPerColumn > 1) {\n      suffixes.push('multirow');\n    }\n    if (Device.android) {\n      suffixes.push('android');\n    }\n    if (Device.ios) {\n      suffixes.push('ios');\n    }\n    // WP8 Touch Events Fix\n    if ((Browser.isIE || Browser.isEdge) && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n      suffixes.push((\"wp8-\" + (params.direction)));\n    }\n\n    suffixes.forEach(function (suffix) {\n      classNames.push(params.containerModifierClass + suffix);\n    });\n\n    $el.addClass(classNames.join(' '));\n  }\n\n  function removeClasses () {\n    var swiper = this;\n    var $el = swiper.$el;\n    var classNames = swiper.classNames;\n\n    $el.removeClass(classNames.join(' '));\n  }\n\n  var classes = { addClasses: addClasses, removeClasses: removeClasses };\n\n  function loadImage (imageEl, src, srcset, sizes, checkForComplete, callback) {\n    var image;\n    function onReady() {\n      if (callback) { callback(); }\n    }\n    if (!imageEl.complete || !checkForComplete) {\n      if (src) {\n        image = new win.Image();\n        image.onload = onReady;\n        image.onerror = onReady;\n        if (sizes) {\n          image.sizes = sizes;\n        }\n        if (srcset) {\n          image.srcset = srcset;\n        }\n        if (src) {\n          image.src = src;\n        }\n      } else {\n        onReady();\n      }\n    } else {\n      // image already loaded...\n      onReady();\n    }\n  }\n\n  function preloadImages () {\n    var swiper = this;\n    swiper.imagesToLoad = swiper.$el.find('img');\n    function onReady() {\n      if (typeof swiper === 'undefined' || swiper === null || !swiper || swiper.destroyed) { return; }\n      if (swiper.imagesLoaded !== undefined) { swiper.imagesLoaded += 1; }\n      if (swiper.imagesLoaded === swiper.imagesToLoad.length) {\n        if (swiper.params.updateOnImagesReady) { swiper.update(); }\n        swiper.emit('imagesReady');\n      }\n    }\n    for (var i = 0; i < swiper.imagesToLoad.length; i += 1) {\n      var imageEl = swiper.imagesToLoad[i];\n      swiper.loadImage(\n        imageEl,\n        imageEl.currentSrc || imageEl.getAttribute('src'),\n        imageEl.srcset || imageEl.getAttribute('srcset'),\n        imageEl.sizes || imageEl.getAttribute('sizes'),\n        true,\n        onReady\n      );\n    }\n  }\n\n  var images = {\n    loadImage: loadImage,\n    preloadImages: preloadImages,\n  };\n\n  function checkOverflow() {\n    var swiper = this;\n    var wasLocked = swiper.isLocked;\n\n    swiper.isLocked = swiper.snapGrid.length === 1;\n    swiper.allowSlideNext = !swiper.isLocked;\n    swiper.allowSlidePrev = !swiper.isLocked;\n\n    // events\n    if (wasLocked !== swiper.isLocked) { swiper.emit(swiper.isLocked ? 'lock' : 'unlock'); }\n\n    if (wasLocked && wasLocked !== swiper.isLocked) {\n      swiper.isEnd = false;\n      swiper.navigation.update();\n    }\n  }\n\n  var checkOverflow$1 = { checkOverflow: checkOverflow };\n\n  var defaults = {\n    init: true,\n    direction: 'horizontal',\n    touchEventsTarget: 'container',\n    initialSlide: 0,\n    speed: 300,\n    //\n    preventInteractionOnTransition: false,\n\n    // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n    edgeSwipeDetection: false,\n    edgeSwipeThreshold: 20,\n\n    // Free mode\n    freeMode: false,\n    freeModeMomentum: true,\n    freeModeMomentumRatio: 1,\n    freeModeMomentumBounce: true,\n    freeModeMomentumBounceRatio: 1,\n    freeModeMomentumVelocityRatio: 1,\n    freeModeSticky: false,\n    freeModeMinimumVelocity: 0.02,\n\n    // Autoheight\n    autoHeight: false,\n\n    // Set wrapper width\n    setWrapperSize: false,\n\n    // Virtual Translate\n    virtualTranslate: false,\n\n    // Effects\n    effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n    // Breakpoints\n    breakpoints: undefined,\n    breakpointsInverse: false,\n\n    // Slides grid\n    spaceBetween: 0,\n    slidesPerView: 1,\n    slidesPerColumn: 1,\n    slidesPerColumnFill: 'column',\n    slidesPerGroup: 1,\n    centeredSlides: false,\n    slidesOffsetBefore: 0, // in px\n    slidesOffsetAfter: 0, // in px\n    normalizeSlideIndex: true,\n    centerInsufficientSlides: false,\n\n    // Disable swiper and hide navigation when container not overflow\n    watchOverflow: false,\n\n    // Round length\n    roundLengths: false,\n\n    // Touches\n    touchRatio: 1,\n    touchAngle: 45,\n    simulateTouch: true,\n    shortSwipes: true,\n    longSwipes: true,\n    longSwipesRatio: 0.5,\n    longSwipesMs: 300,\n    followFinger: true,\n    allowTouchMove: true,\n    threshold: 0,\n    touchMoveStopPropagation: true,\n    touchStartPreventDefault: true,\n    touchReleaseOnEdges: false,\n\n    // Unique Navigation Elements\n    uniqueNavElements: true,\n\n    // Resistance\n    resistance: true,\n    resistanceRatio: 0.85,\n\n    // Progress\n    watchSlidesProgress: false,\n    watchSlidesVisibility: false,\n\n    // Cursor\n    grabCursor: false,\n\n    // Clicks\n    preventClicks: true,\n    preventClicksPropagation: true,\n    slideToClickedSlide: false,\n\n    // Images\n    preloadImages: true,\n    updateOnImagesReady: true,\n\n    // loop\n    loop: false,\n    loopAdditionalSlides: 0,\n    loopedSlides: null,\n    loopFillGroupWithBlank: false,\n\n    // Swiping/no swiping\n    allowSlidePrev: true,\n    allowSlideNext: true,\n    swipeHandler: null, // '.swipe-handler',\n    noSwiping: true,\n    noSwipingClass: 'swiper-no-swiping',\n    noSwipingSelector: null,\n\n    // Passive Listeners\n    passiveListeners: true,\n\n    // NS\n    containerModifierClass: 'swiper-container-', // NEW\n    slideClass: 'swiper-slide',\n    slideBlankClass: 'swiper-slide-invisible-blank',\n    slideActiveClass: 'swiper-slide-active',\n    slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n    slideVisibleClass: 'swiper-slide-visible',\n    slideDuplicateClass: 'swiper-slide-duplicate',\n    slideNextClass: 'swiper-slide-next',\n    slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n    slidePrevClass: 'swiper-slide-prev',\n    slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n    wrapperClass: 'swiper-wrapper',\n\n    // Callbacks\n    runCallbacksOnInit: true,\n  };\n\n  var prototypes = {\n    update: update,\n    translate: translate,\n    transition: transition$1,\n    slide: slide,\n    loop: loop,\n    grabCursor: grabCursor,\n    manipulation: manipulation,\n    events: events,\n    breakpoints: breakpoints,\n    checkOverflow: checkOverflow$1,\n    classes: classes,\n    images: images,\n  };\n\n  var extendedDefaults = {};\n\n  var Swiper = (function (SwiperClass$$1) {\n    function Swiper() {\n      var assign;\n\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n      var el;\n      var params;\n      if (args.length === 1 && args[0].constructor && args[0].constructor === Object) {\n        params = args[0];\n      } else {\n        (assign = args, el = assign[0], params = assign[1]);\n      }\n      if (!params) { params = {}; }\n\n      params = Utils.extend({}, params);\n      if (el && !params.el) { params.el = el; }\n\n      SwiperClass$$1.call(this, params);\n\n      Object.keys(prototypes).forEach(function (prototypeGroup) {\n        Object.keys(prototypes[prototypeGroup]).forEach(function (protoMethod) {\n          if (!Swiper.prototype[protoMethod]) {\n            Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n          }\n        });\n      });\n\n      // Swiper Instance\n      var swiper = this;\n      if (typeof swiper.modules === 'undefined') {\n        swiper.modules = {};\n      }\n      Object.keys(swiper.modules).forEach(function (moduleName) {\n        var module = swiper.modules[moduleName];\n        if (module.params) {\n          var moduleParamName = Object.keys(module.params)[0];\n          var moduleParams = module.params[moduleParamName];\n          if (typeof moduleParams !== 'object' || moduleParams === null) { return; }\n          if (!(moduleParamName in params && 'enabled' in moduleParams)) { return; }\n          if (params[moduleParamName] === true) {\n            params[moduleParamName] = { enabled: true };\n          }\n          if (\n            typeof params[moduleParamName] === 'object'\n            && !('enabled' in params[moduleParamName])\n          ) {\n            params[moduleParamName].enabled = true;\n          }\n          if (!params[moduleParamName]) { params[moduleParamName] = { enabled: false }; }\n        }\n      });\n\n      // Extend defaults with modules params\n      var swiperParams = Utils.extend({}, defaults);\n      swiper.useModulesParams(swiperParams);\n\n      // Extend defaults with passed params\n      swiper.params = Utils.extend({}, swiperParams, extendedDefaults, params);\n      swiper.originalParams = Utils.extend({}, swiper.params);\n      swiper.passedParams = Utils.extend({}, params);\n\n      // Save Dom lib\n      swiper.$ = $;\n\n      // Find el\n      var $el = $(swiper.params.el);\n      el = $el[0];\n\n      if (!el) {\n        return undefined;\n      }\n\n      if ($el.length > 1) {\n        var swipers = [];\n        $el.each(function (index, containerEl) {\n          var newParams = Utils.extend({}, params, { el: containerEl });\n          swipers.push(new Swiper(newParams));\n        });\n        return swipers;\n      }\n\n      el.swiper = swiper;\n      $el.data('swiper', swiper);\n\n      // Find Wrapper\n      var $wrapperEl = $el.children((\".\" + (swiper.params.wrapperClass)));\n\n      // Extend Swiper\n      Utils.extend(swiper, {\n        $el: $el,\n        el: el,\n        $wrapperEl: $wrapperEl,\n        wrapperEl: $wrapperEl[0],\n\n        // Classes\n        classNames: [],\n\n        // Slides\n        slides: $(),\n        slidesGrid: [],\n        snapGrid: [],\n        slidesSizesGrid: [],\n\n        // isDirection\n        isHorizontal: function isHorizontal() {\n          return swiper.params.direction === 'horizontal';\n        },\n        isVertical: function isVertical() {\n          return swiper.params.direction === 'vertical';\n        },\n        // RTL\n        rtl: (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        wrongRTL: $wrapperEl.css('display') === '-webkit-box',\n\n        // Indexes\n        activeIndex: 0,\n        realIndex: 0,\n\n        //\n        isBeginning: true,\n        isEnd: false,\n\n        // Props\n        translate: 0,\n        previousTranslate: 0,\n        progress: 0,\n        velocity: 0,\n        animating: false,\n\n        // Locks\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n\n        // Touch Events\n        touchEvents: (function touchEvents() {\n          var touch = ['touchstart', 'touchmove', 'touchend'];\n          var desktop = ['mousedown', 'mousemove', 'mouseup'];\n          if (Support.pointerEvents) {\n            desktop = ['pointerdown', 'pointermove', 'pointerup'];\n          } else if (Support.prefixedPointerEvents) {\n            desktop = ['MSPointerDown', 'MSPointerMove', 'MSPointerUp'];\n          }\n          swiper.touchEventsTouch = {\n            start: touch[0],\n            move: touch[1],\n            end: touch[2],\n          };\n          swiper.touchEventsDesktop = {\n            start: desktop[0],\n            move: desktop[1],\n            end: desktop[2],\n          };\n          return Support.touch || !swiper.params.simulateTouch ? swiper.touchEventsTouch : swiper.touchEventsDesktop;\n        }()),\n        touchEventsData: {\n          isTouched: undefined,\n          isMoved: undefined,\n          allowTouchCallbacks: undefined,\n          touchStartTime: undefined,\n          isScrolling: undefined,\n          currentTranslate: undefined,\n          startTranslate: undefined,\n          allowThresholdMove: undefined,\n          // Form elements to match\n          formElements: 'input, select, option, textarea, button, video',\n          // Last click time\n          lastClickTime: Utils.now(),\n          clickTimeout: undefined,\n          // Velocities\n          velocities: [],\n          allowMomentumBounce: undefined,\n          isTouchEvent: undefined,\n          startMoving: undefined,\n        },\n\n        // Clicks\n        allowClick: true,\n\n        // Touches\n        allowTouchMove: swiper.params.allowTouchMove,\n\n        touches: {\n          startX: 0,\n          startY: 0,\n          currentX: 0,\n          currentY: 0,\n          diff: 0,\n        },\n\n        // Images\n        imagesToLoad: [],\n        imagesLoaded: 0,\n\n      });\n\n      // Install Modules\n      swiper.useModules();\n\n      // Init\n      if (swiper.params.init) {\n        swiper.init();\n      }\n\n      // Return app instance\n      return swiper;\n    }\n\n    if ( SwiperClass$$1 ) Swiper.__proto__ = SwiperClass$$1;\n    Swiper.prototype = Object.create( SwiperClass$$1 && SwiperClass$$1.prototype );\n    Swiper.prototype.constructor = Swiper;\n\n    var staticAccessors = { extendedDefaults: { configurable: true },defaults: { configurable: true },Class: { configurable: true },$: { configurable: true } };\n\n    Swiper.prototype.slidesPerViewDynamic = function slidesPerViewDynamic () {\n      var swiper = this;\n      var params = swiper.params;\n      var slides = swiper.slides;\n      var slidesGrid = swiper.slidesGrid;\n      var swiperSize = swiper.size;\n      var activeIndex = swiper.activeIndex;\n      var spv = 1;\n      if (params.centeredSlides) {\n        var slideSize = slides[activeIndex].swiperSlideSize;\n        var breakLoop;\n        for (var i = activeIndex + 1; i < slides.length; i += 1) {\n          if (slides[i] && !breakLoop) {\n            slideSize += slides[i].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n        for (var i$1 = activeIndex - 1; i$1 >= 0; i$1 -= 1) {\n          if (slides[i$1] && !breakLoop) {\n            slideSize += slides[i$1].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n      } else {\n        for (var i$2 = activeIndex + 1; i$2 < slides.length; i$2 += 1) {\n          if (slidesGrid[i$2] - slidesGrid[activeIndex] < swiperSize) {\n            spv += 1;\n          }\n        }\n      }\n      return spv;\n    };\n\n    Swiper.prototype.update = function update$$1 () {\n      var swiper = this;\n      if (!swiper || swiper.destroyed) { return; }\n      var snapGrid = swiper.snapGrid;\n      var params = swiper.params;\n      // Breakpoints\n      if (params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n      swiper.updateSize();\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n\n      function setTranslate() {\n        var translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n        var newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n        swiper.setTranslate(newTranslate);\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      var translated;\n      if (swiper.params.freeMode) {\n        setTranslate();\n        if (swiper.params.autoHeight) {\n          swiper.updateAutoHeight();\n        }\n      } else {\n        if ((swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n          translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n        } else {\n          translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n        }\n        if (!translated) {\n          setTranslate();\n        }\n      }\n      if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n        swiper.checkOverflow();\n      }\n      swiper.emit('update');\n    };\n\n    Swiper.prototype.init = function init () {\n      var swiper = this;\n      if (swiper.initialized) { return; }\n\n      swiper.emit('beforeInit');\n\n      // Set breakpoint\n      if (swiper.params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n\n      // Add Classes\n      swiper.addClasses();\n\n      // Create loop\n      if (swiper.params.loop) {\n        swiper.loopCreate();\n      }\n\n      // Update size\n      swiper.updateSize();\n\n      // Update slides\n      swiper.updateSlides();\n\n      if (swiper.params.watchOverflow) {\n        swiper.checkOverflow();\n      }\n\n      // Set Grab Cursor\n      if (swiper.params.grabCursor) {\n        swiper.setGrabCursor();\n      }\n\n      if (swiper.params.preloadImages) {\n        swiper.preloadImages();\n      }\n\n      // Slide To Initial Slide\n      if (swiper.params.loop) {\n        swiper.slideTo(swiper.params.initialSlide + swiper.loopedSlides, 0, swiper.params.runCallbacksOnInit);\n      } else {\n        swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit);\n      }\n\n      // Attach events\n      swiper.attachEvents();\n\n      // Init Flag\n      swiper.initialized = true;\n\n      // Emit\n      swiper.emit('init');\n    };\n\n    Swiper.prototype.destroy = function destroy (deleteInstance, cleanStyles) {\n      if ( deleteInstance === void 0 ) deleteInstance = true;\n      if ( cleanStyles === void 0 ) cleanStyles = true;\n\n      var swiper = this;\n      var params = swiper.params;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n\n      if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n        return null;\n      }\n\n      swiper.emit('beforeDestroy');\n\n      // Init Flag\n      swiper.initialized = false;\n\n      // Detach events\n      swiper.detachEvents();\n\n      // Destroy loop\n      if (params.loop) {\n        swiper.loopDestroy();\n      }\n\n      // Cleanup styles\n      if (cleanStyles) {\n        swiper.removeClasses();\n        $el.removeAttr('style');\n        $wrapperEl.removeAttr('style');\n        if (slides && slides.length) {\n          slides\n            .removeClass([\n              params.slideVisibleClass,\n              params.slideActiveClass,\n              params.slideNextClass,\n              params.slidePrevClass ].join(' '))\n            .removeAttr('style')\n            .removeAttr('data-swiper-slide-index')\n            .removeAttr('data-swiper-column')\n            .removeAttr('data-swiper-row');\n        }\n      }\n\n      swiper.emit('destroy');\n\n      // Detach emitter events\n      Object.keys(swiper.eventsListeners).forEach(function (eventName) {\n        swiper.off(eventName);\n      });\n\n      if (deleteInstance !== false) {\n        swiper.$el[0].swiper = null;\n        swiper.$el.data('swiper', null);\n        Utils.deleteProps(swiper);\n      }\n      swiper.destroyed = true;\n\n      return null;\n    };\n\n    Swiper.extendDefaults = function extendDefaults (newDefaults) {\n      Utils.extend(extendedDefaults, newDefaults);\n    };\n\n    staticAccessors.extendedDefaults.get = function () {\n      return extendedDefaults;\n    };\n\n    staticAccessors.defaults.get = function () {\n      return defaults;\n    };\n\n    staticAccessors.Class.get = function () {\n      return SwiperClass$$1;\n    };\n\n    staticAccessors.$.get = function () {\n      return $;\n    };\n\n    Object.defineProperties( Swiper, staticAccessors );\n\n    return Swiper;\n  }(SwiperClass));\n\n  var Device$1 = {\n    name: 'device',\n    proto: {\n      device: Device,\n    },\n    static: {\n      device: Device,\n    },\n  };\n\n  var Support$1 = {\n    name: 'support',\n    proto: {\n      support: Support,\n    },\n    static: {\n      support: Support,\n    },\n  };\n\n  var Browser$1 = {\n    name: 'browser',\n    proto: {\n      browser: Browser,\n    },\n    static: {\n      browser: Browser,\n    },\n  };\n\n  var Resize = {\n    name: 'resize',\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        resize: {\n          resizeHandler: function resizeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('beforeResize');\n            swiper.emit('resize');\n          },\n          orientationChangeHandler: function orientationChangeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('orientationchange');\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        // Emit resize\n        win.addEventListener('resize', swiper.resize.resizeHandler);\n\n        // Emit orientationchange\n        win.addEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        win.removeEventListener('resize', swiper.resize.resizeHandler);\n        win.removeEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n    },\n  };\n\n  var Observer = {\n    func: win.MutationObserver || win.WebkitMutationObserver,\n    attach: function attach(target, options) {\n      if ( options === void 0 ) options = {};\n\n      var swiper = this;\n\n      var ObserverFunc = Observer.func;\n      var observer = new ObserverFunc(function (mutations) {\n        // The observerUpdate event should only be triggered\n        // once despite the number of mutations.  Additional\n        // triggers are redundant and are very costly\n        if (mutations.length === 1) {\n          swiper.emit('observerUpdate', mutations[0]);\n          return;\n        }\n        var observerUpdate = function observerUpdate() {\n          swiper.emit('observerUpdate', mutations[0]);\n        };\n\n        if (win.requestAnimationFrame) {\n          win.requestAnimationFrame(observerUpdate);\n        } else {\n          win.setTimeout(observerUpdate, 0);\n        }\n      });\n\n      observer.observe(target, {\n        attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n        childList: typeof options.childList === 'undefined' ? true : options.childList,\n        characterData: typeof options.characterData === 'undefined' ? true : options.characterData,\n      });\n\n      swiper.observer.observers.push(observer);\n    },\n    init: function init() {\n      var swiper = this;\n      if (!Support.observer || !swiper.params.observer) { return; }\n      if (swiper.params.observeParents) {\n        var containerParents = swiper.$el.parents();\n        for (var i = 0; i < containerParents.length; i += 1) {\n          swiper.observer.attach(containerParents[i]);\n        }\n      }\n      // Observe container\n      swiper.observer.attach(swiper.$el[0], { childList: false });\n\n      // Observe wrapper\n      swiper.observer.attach(swiper.$wrapperEl[0], { attributes: false });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.observer.observers.forEach(function (observer) {\n        observer.disconnect();\n      });\n      swiper.observer.observers = [];\n    },\n  };\n\n  var Observer$1 = {\n    name: 'observer',\n    params: {\n      observer: false,\n      observeParents: false,\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        observer: {\n          init: Observer.init.bind(swiper),\n          attach: Observer.attach.bind(swiper),\n          destroy: Observer.destroy.bind(swiper),\n          observers: [],\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.observer.init();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.observer.destroy();\n      },\n    },\n  };\n\n  var Virtual = {\n    update: function update(force) {\n      var swiper = this;\n      var ref = swiper.params;\n      var slidesPerView = ref.slidesPerView;\n      var slidesPerGroup = ref.slidesPerGroup;\n      var centeredSlides = ref.centeredSlides;\n      var ref$1 = swiper.params.virtual;\n      var addSlidesBefore = ref$1.addSlidesBefore;\n      var addSlidesAfter = ref$1.addSlidesAfter;\n      var ref$2 = swiper.virtual;\n      var previousFrom = ref$2.from;\n      var previousTo = ref$2.to;\n      var slides = ref$2.slides;\n      var previousSlidesGrid = ref$2.slidesGrid;\n      var renderSlide = ref$2.renderSlide;\n      var previousOffset = ref$2.offset;\n      swiper.updateActiveIndex();\n      var activeIndex = swiper.activeIndex || 0;\n\n      var offsetProp;\n      if (swiper.rtlTranslate) { offsetProp = 'right'; }\n      else { offsetProp = swiper.isHorizontal() ? 'left' : 'top'; }\n\n      var slidesAfter;\n      var slidesBefore;\n      if (centeredSlides) {\n        slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n        slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      } else {\n        slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesBefore;\n        slidesBefore = slidesPerGroup + addSlidesAfter;\n      }\n      var from = Math.max((activeIndex || 0) - slidesBefore, 0);\n      var to = Math.min((activeIndex || 0) + slidesAfter, slides.length - 1);\n      var offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n\n      Utils.extend(swiper.virtual, {\n        from: from,\n        to: to,\n        offset: offset,\n        slidesGrid: swiper.slidesGrid,\n      });\n\n      function onRendered() {\n        swiper.updateSlides();\n        swiper.updateProgress();\n        swiper.updateSlidesClasses();\n        if (swiper.lazy && swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      }\n\n      if (previousFrom === from && previousTo === to && !force) {\n        if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n          swiper.slides.css(offsetProp, (offset + \"px\"));\n        }\n        swiper.updateProgress();\n        return;\n      }\n      if (swiper.params.virtual.renderExternal) {\n        swiper.params.virtual.renderExternal.call(swiper, {\n          offset: offset,\n          from: from,\n          to: to,\n          slides: (function getSlides() {\n            var slidesToRender = [];\n            for (var i = from; i <= to; i += 1) {\n              slidesToRender.push(slides[i]);\n            }\n            return slidesToRender;\n          }()),\n        });\n        onRendered();\n        return;\n      }\n      var prependIndexes = [];\n      var appendIndexes = [];\n      if (force) {\n        swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass))).remove();\n      } else {\n        for (var i = previousFrom; i <= previousTo; i += 1) {\n          if (i < from || i > to) {\n            swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + i + \"\\\"]\")).remove();\n          }\n        }\n      }\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (i$1 >= from && i$1 <= to) {\n          if (typeof previousTo === 'undefined' || force) {\n            appendIndexes.push(i$1);\n          } else {\n            if (i$1 > previousTo) { appendIndexes.push(i$1); }\n            if (i$1 < previousFrom) { prependIndexes.push(i$1); }\n          }\n        }\n      }\n      appendIndexes.forEach(function (index) {\n        swiper.$wrapperEl.append(renderSlide(slides[index], index));\n      });\n      prependIndexes.sort(function (a, b) { return a < b; }).forEach(function (index) {\n        swiper.$wrapperEl.prepend(renderSlide(slides[index], index));\n      });\n      swiper.$wrapperEl.children('.swiper-slide').css(offsetProp, (offset + \"px\"));\n      onRendered();\n    },\n    renderSlide: function renderSlide(slide, index) {\n      var swiper = this;\n      var params = swiper.params.virtual;\n      if (params.cache && swiper.virtual.cache[index]) {\n        return swiper.virtual.cache[index];\n      }\n      var $slideEl = params.renderSlide\n        ? $(params.renderSlide.call(swiper, slide, index))\n        : $((\"<div class=\\\"\" + (swiper.params.slideClass) + \"\\\" data-swiper-slide-index=\\\"\" + index + \"\\\">\" + slide + \"</div>\"));\n      if (!$slideEl.attr('data-swiper-slide-index')) { $slideEl.attr('data-swiper-slide-index', index); }\n      if (params.cache) { swiper.virtual.cache[index] = $slideEl; }\n      return $slideEl;\n    },\n    appendSlide: function appendSlide(slide) {\n      var swiper = this;\n      swiper.virtual.slides.push(slide);\n      swiper.virtual.update(true);\n    },\n    prependSlide: function prependSlide(slide) {\n      var swiper = this;\n      swiper.virtual.slides.unshift(slide);\n      if (swiper.params.virtual.cache) {\n        var cache = swiper.virtual.cache;\n        var newCache = {};\n        Object.keys(cache).forEach(function (cachedIndex) {\n          newCache[cachedIndex + 1] = cache[cachedIndex];\n        });\n        swiper.virtual.cache = newCache;\n      }\n      swiper.virtual.update(true);\n      swiper.slideNext(0);\n    },\n  };\n\n  var Virtual$1 = {\n    name: 'virtual',\n    params: {\n      virtual: {\n        enabled: false,\n        slides: [],\n        cache: true,\n        renderSlide: null,\n        renderExternal: null,\n        addSlidesBefore: 0,\n        addSlidesAfter: 0,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        virtual: {\n          update: Virtual.update.bind(swiper),\n          appendSlide: Virtual.appendSlide.bind(swiper),\n          prependSlide: Virtual.prependSlide.bind(swiper),\n          renderSlide: Virtual.renderSlide.bind(swiper),\n          slides: swiper.params.virtual.slides,\n          cache: {},\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"virtual\"));\n        var overwriteParams = {\n          watchSlidesProgress: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n\n        swiper.virtual.update();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.virtual.update();\n      },\n    },\n  };\n\n  var Keyboard = {\n    handle: function handle(event) {\n      var swiper = this;\n      var rtl = swiper.rtlTranslate;\n      var e = event;\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var kc = e.keyCode || e.charCode;\n      // Directions locks\n      if (!swiper.allowSlideNext && ((swiper.isHorizontal() && kc === 39) || (swiper.isVertical() && kc === 40))) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && ((swiper.isHorizontal() && kc === 37) || (swiper.isVertical() && kc === 38))) {\n        return false;\n      }\n      if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n        return undefined;\n      }\n      if (doc.activeElement && doc.activeElement.nodeName && (doc.activeElement.nodeName.toLowerCase() === 'input' || doc.activeElement.nodeName.toLowerCase() === 'textarea')) {\n        return undefined;\n      }\n      if (swiper.params.keyboard.onlyInViewport && (kc === 37 || kc === 39 || kc === 38 || kc === 40)) {\n        var inView = false;\n        // Check that swiper should be inside of visible area of window\n        if (swiper.$el.parents((\".\" + (swiper.params.slideClass))).length > 0 && swiper.$el.parents((\".\" + (swiper.params.slideActiveClass))).length === 0) {\n          return undefined;\n        }\n        var windowWidth = win.innerWidth;\n        var windowHeight = win.innerHeight;\n        var swiperOffset = swiper.$el.offset();\n        if (rtl) { swiperOffset.left -= swiper.$el[0].scrollLeft; }\n        var swiperCoord = [\n          [swiperOffset.left, swiperOffset.top],\n          [swiperOffset.left + swiper.width, swiperOffset.top],\n          [swiperOffset.left, swiperOffset.top + swiper.height],\n          [swiperOffset.left + swiper.width, swiperOffset.top + swiper.height] ];\n        for (var i = 0; i < swiperCoord.length; i += 1) {\n          var point = swiperCoord[i];\n          if (\n            point[0] >= 0 && point[0] <= windowWidth\n            && point[1] >= 0 && point[1] <= windowHeight\n          ) {\n            inView = true;\n          }\n        }\n        if (!inView) { return undefined; }\n      }\n      if (swiper.isHorizontal()) {\n        if (kc === 37 || kc === 39) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if ((kc === 39 && !rtl) || (kc === 37 && rtl)) { swiper.slideNext(); }\n        if ((kc === 37 && !rtl) || (kc === 39 && rtl)) { swiper.slidePrev(); }\n      } else {\n        if (kc === 38 || kc === 40) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if (kc === 40) { swiper.slideNext(); }\n        if (kc === 38) { swiper.slidePrev(); }\n      }\n      swiper.emit('keyPress', kc);\n      return undefined;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (swiper.keyboard.enabled) { return; }\n      $(doc).on('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!swiper.keyboard.enabled) { return; }\n      $(doc).off('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = false;\n    },\n  };\n\n  var Keyboard$1 = {\n    name: 'keyboard',\n    params: {\n      keyboard: {\n        enabled: false,\n        onlyInViewport: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        keyboard: {\n          enabled: false,\n          enable: Keyboard.enable.bind(swiper),\n          disable: Keyboard.disable.bind(swiper),\n          handle: Keyboard.handle.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.keyboard.enabled) {\n          swiper.keyboard.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.keyboard.enabled) {\n          swiper.keyboard.disable();\n        }\n      },\n    },\n  };\n\n  function isEventSupported() {\n    var eventName = 'onwheel';\n    var isSupported = eventName in doc;\n\n    if (!isSupported) {\n      var element = doc.createElement('div');\n      element.setAttribute(eventName, 'return;');\n      isSupported = typeof element[eventName] === 'function';\n    }\n\n    if (!isSupported\n      && doc.implementation\n      && doc.implementation.hasFeature\n      // always returns true in newer browsers as per the standard.\n      // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n      && doc.implementation.hasFeature('', '') !== true\n    ) {\n      // This is the only way to test support for the `wheel` event in IE9+.\n      isSupported = doc.implementation.hasFeature('Events.wheel', '3.0');\n    }\n\n    return isSupported;\n  }\n  var Mousewheel = {\n    lastScrollTime: Utils.now(),\n    event: (function getEvent() {\n      if (win.navigator.userAgent.indexOf('firefox') > -1) { return 'DOMMouseScroll'; }\n      return isEventSupported() ? 'wheel' : 'mousewheel';\n    }()),\n    normalize: function normalize(e) {\n      // Reasonable defaults\n      var PIXEL_STEP = 10;\n      var LINE_HEIGHT = 40;\n      var PAGE_HEIGHT = 800;\n\n      var sX = 0;\n      var sY = 0; // spinX, spinY\n      var pX = 0;\n      var pY = 0; // pixelX, pixelY\n\n      // Legacy\n      if ('detail' in e) {\n        sY = e.detail;\n      }\n      if ('wheelDelta' in e) {\n        sY = -e.wheelDelta / 120;\n      }\n      if ('wheelDeltaY' in e) {\n        sY = -e.wheelDeltaY / 120;\n      }\n      if ('wheelDeltaX' in e) {\n        sX = -e.wheelDeltaX / 120;\n      }\n\n      // side scrolling on FF with DOMMouseScroll\n      if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n        sX = sY;\n        sY = 0;\n      }\n\n      pX = sX * PIXEL_STEP;\n      pY = sY * PIXEL_STEP;\n\n      if ('deltaY' in e) {\n        pY = e.deltaY;\n      }\n      if ('deltaX' in e) {\n        pX = e.deltaX;\n      }\n\n      if ((pX || pY) && e.deltaMode) {\n        if (e.deltaMode === 1) { // delta in LINE units\n          pX *= LINE_HEIGHT;\n          pY *= LINE_HEIGHT;\n        } else { // delta in PAGE units\n          pX *= PAGE_HEIGHT;\n          pY *= PAGE_HEIGHT;\n        }\n      }\n\n      // Fall-back if spin cannot be determined\n      if (pX && !sX) {\n        sX = (pX < 1) ? -1 : 1;\n      }\n      if (pY && !sY) {\n        sY = (pY < 1) ? -1 : 1;\n      }\n\n      return {\n        spinX: sX,\n        spinY: sY,\n        pixelX: pX,\n        pixelY: pY,\n      };\n    },\n    handleMouseEnter: function handleMouseEnter() {\n      var swiper = this;\n      swiper.mouseEntered = true;\n    },\n    handleMouseLeave: function handleMouseLeave() {\n      var swiper = this;\n      swiper.mouseEntered = false;\n    },\n    handle: function handle(event) {\n      var e = event;\n      var swiper = this;\n      var params = swiper.params.mousewheel;\n\n      if (!swiper.mouseEntered && !params.releaseOnEdges) { return true; }\n\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var delta = 0;\n      var rtlFactor = swiper.rtlTranslate ? -1 : 1;\n\n      var data = Mousewheel.normalize(e);\n\n      if (params.forceToAxis) {\n        if (swiper.isHorizontal()) {\n          if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) { delta = data.pixelX * rtlFactor; }\n          else { return true; }\n        } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) { delta = data.pixelY; }\n        else { return true; }\n      } else {\n        delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n      }\n\n      if (delta === 0) { return true; }\n\n      if (params.invert) { delta = -delta; }\n\n      if (!swiper.params.freeMode) {\n        if (Utils.now() - swiper.mousewheel.lastScrollTime > 60) {\n          if (delta < 0) {\n            if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n              swiper.slideNext();\n              swiper.emit('scroll', e);\n            } else if (params.releaseOnEdges) { return true; }\n          } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n            swiper.slidePrev();\n            swiper.emit('scroll', e);\n          } else if (params.releaseOnEdges) { return true; }\n        }\n        swiper.mousewheel.lastScrollTime = (new win.Date()).getTime();\n      } else {\n        // Freemode or scrollContainer:\n        if (swiper.params.loop) {\n          swiper.loopFix();\n        }\n        var position = swiper.getTranslate() + (delta * params.sensitivity);\n        var wasBeginning = swiper.isBeginning;\n        var wasEnd = swiper.isEnd;\n\n        if (position >= swiper.minTranslate()) { position = swiper.minTranslate(); }\n        if (position <= swiper.maxTranslate()) { position = swiper.maxTranslate(); }\n\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n\n        if ((!wasBeginning && swiper.isBeginning) || (!wasEnd && swiper.isEnd)) {\n          swiper.updateSlidesClasses();\n        }\n\n        if (swiper.params.freeModeSticky) {\n          clearTimeout(swiper.mousewheel.timeout);\n          swiper.mousewheel.timeout = Utils.nextTick(function () {\n            swiper.slideToClosest();\n          }, 300);\n        }\n        // Emit event\n        swiper.emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction) { swiper.autoplay.stop(); }\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) { return true; }\n      }\n\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      return false;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.on('mouseenter', swiper.mousewheel.handleMouseEnter);\n      target.on('mouseleave', swiper.mousewheel.handleMouseLeave);\n      target.on(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = true;\n      return true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (!swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.off(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = false;\n      return true;\n    },\n  };\n\n  var Mousewheel$1 = {\n    name: 'mousewheel',\n    params: {\n      mousewheel: {\n        enabled: false,\n        releaseOnEdges: false,\n        invert: false,\n        forceToAxis: false,\n        sensitivity: 1,\n        eventsTarged: 'container',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        mousewheel: {\n          enabled: false,\n          enable: Mousewheel.enable.bind(swiper),\n          disable: Mousewheel.disable.bind(swiper),\n          handle: Mousewheel.handle.bind(swiper),\n          handleMouseEnter: Mousewheel.handleMouseEnter.bind(swiper),\n          handleMouseLeave: Mousewheel.handleMouseLeave.bind(swiper),\n          lastScrollTime: Utils.now(),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.mousewheel.enabled) { swiper.mousewheel.enable(); }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.mousewheel.enabled) { swiper.mousewheel.disable(); }\n      },\n    },\n  };\n\n  var Navigation = {\n    update: function update() {\n      // Update Navigation Buttons\n      var swiper = this;\n      var params = swiper.params.navigation;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          $prevEl.addClass(params.disabledClass);\n        } else {\n          $prevEl.removeClass(params.disabledClass);\n        }\n        $prevEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          $nextEl.addClass(params.disabledClass);\n        } else {\n          $nextEl.removeClass(params.disabledClass);\n        }\n        $nextEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.navigation;\n      if (!(params.nextEl || params.prevEl)) { return; }\n\n      var $nextEl;\n      var $prevEl;\n      if (params.nextEl) {\n        $nextEl = $(params.nextEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.nextEl === 'string'\n          && $nextEl.length > 1\n          && swiper.$el.find(params.nextEl).length === 1\n        ) {\n          $nextEl = swiper.$el.find(params.nextEl);\n        }\n      }\n      if (params.prevEl) {\n        $prevEl = $(params.prevEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.prevEl === 'string'\n          && $prevEl.length > 1\n          && swiper.$el.find(params.prevEl).length === 1\n        ) {\n          $prevEl = swiper.$el.find(params.prevEl);\n        }\n      }\n\n      if ($nextEl && $nextEl.length > 0) {\n        $nextEl.on('click', function (e) {\n          e.preventDefault();\n          if (swiper.isEnd && !swiper.params.loop) { return; }\n          swiper.slideNext();\n        });\n      }\n      if ($prevEl && $prevEl.length > 0) {\n        $prevEl.on('click', function (e) {\n          e.preventDefault();\n          if (swiper.isBeginning && !swiper.params.loop) { return; }\n          swiper.slidePrev();\n        });\n      }\n\n      Utils.extend(swiper.navigation, {\n        $nextEl: $nextEl,\n        nextEl: $nextEl && $nextEl[0],\n        $prevEl: $prevEl,\n        prevEl: $prevEl && $prevEl[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n      if ($nextEl && $nextEl.length) {\n        $nextEl.off('click');\n        $nextEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n      if ($prevEl && $prevEl.length) {\n        $prevEl.off('click');\n        $prevEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n    },\n  };\n\n  var Navigation$1 = {\n    name: 'navigation',\n    params: {\n      navigation: {\n        nextEl: null,\n        prevEl: null,\n\n        hideOnClick: false,\n        disabledClass: 'swiper-button-disabled',\n        hiddenClass: 'swiper-button-hidden',\n        lockClass: 'swiper-button-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        navigation: {\n          init: Navigation.init.bind(swiper),\n          update: Navigation.update.bind(swiper),\n          destroy: Navigation.destroy.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.navigation.init();\n        swiper.navigation.update();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.navigation.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        var ref = swiper.navigation;\n        var $nextEl = ref.$nextEl;\n        var $prevEl = ref.$prevEl;\n        if (\n          swiper.params.navigation.hideOnClick\n          && !$(e.target).is($prevEl)\n          && !$(e.target).is($nextEl)\n        ) {\n          if ($nextEl) { $nextEl.toggleClass(swiper.params.navigation.hiddenClass); }\n          if ($prevEl) { $prevEl.toggleClass(swiper.params.navigation.hiddenClass); }\n        }\n      },\n    },\n  };\n\n  var Pagination = {\n    update: function update() {\n      // Render || Update Pagination bullets/items\n      var swiper = this;\n      var rtl = swiper.rtl;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n      var $el = swiper.pagination.$el;\n      // Current/Total\n      var current;\n      var total = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.loop) {\n        current = Math.ceil((swiper.activeIndex - swiper.loopedSlides) / swiper.params.slidesPerGroup);\n        if (current > slidesLength - 1 - (swiper.loopedSlides * 2)) {\n          current -= (slidesLength - (swiper.loopedSlides * 2));\n        }\n        if (current > total - 1) { current -= total; }\n        if (current < 0 && swiper.params.paginationType !== 'bullets') { current = total + current; }\n      } else if (typeof swiper.snapIndex !== 'undefined') {\n        current = swiper.snapIndex;\n      } else {\n        current = swiper.activeIndex || 0;\n      }\n      // Types\n      if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n        var bullets = swiper.pagination.bullets;\n        var firstIndex;\n        var lastIndex;\n        var midIndex;\n        if (params.dynamicBullets) {\n          swiper.pagination.bulletSize = bullets.eq(0)[swiper.isHorizontal() ? 'outerWidth' : 'outerHeight'](true);\n          $el.css(swiper.isHorizontal() ? 'width' : 'height', ((swiper.pagination.bulletSize * (params.dynamicMainBullets + 4)) + \"px\"));\n          if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n            swiper.pagination.dynamicBulletIndex += (current - swiper.previousIndex);\n            if (swiper.pagination.dynamicBulletIndex > (params.dynamicMainBullets - 1)) {\n              swiper.pagination.dynamicBulletIndex = params.dynamicMainBullets - 1;\n            } else if (swiper.pagination.dynamicBulletIndex < 0) {\n              swiper.pagination.dynamicBulletIndex = 0;\n            }\n          }\n          firstIndex = current - swiper.pagination.dynamicBulletIndex;\n          lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n          midIndex = (lastIndex + firstIndex) / 2;\n        }\n        bullets.removeClass(((params.bulletActiveClass) + \" \" + (params.bulletActiveClass) + \"-next \" + (params.bulletActiveClass) + \"-next-next \" + (params.bulletActiveClass) + \"-prev \" + (params.bulletActiveClass) + \"-prev-prev \" + (params.bulletActiveClass) + \"-main\"));\n        if ($el.length > 1) {\n          bullets.each(function (index, bullet) {\n            var $bullet = $(bullet);\n            var bulletIndex = $bullet.index();\n            if (bulletIndex === current) {\n              $bullet.addClass(params.bulletActiveClass);\n            }\n            if (params.dynamicBullets) {\n              if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n                $bullet.addClass(((params.bulletActiveClass) + \"-main\"));\n              }\n              if (bulletIndex === firstIndex) {\n                $bullet\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev\"))\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n              }\n              if (bulletIndex === lastIndex) {\n                $bullet\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next\"))\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next-next\"));\n              }\n            }\n          });\n        } else {\n          var $bullet = bullets.eq(current);\n          $bullet.addClass(params.bulletActiveClass);\n          if (params.dynamicBullets) {\n            var $firstDisplayedBullet = bullets.eq(firstIndex);\n            var $lastDisplayedBullet = bullets.eq(lastIndex);\n            for (var i = firstIndex; i <= lastIndex; i += 1) {\n              bullets.eq(i).addClass(((params.bulletActiveClass) + \"-main\"));\n            }\n            $firstDisplayedBullet\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev\"))\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n            $lastDisplayedBullet\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next\"))\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next-next\"));\n          }\n        }\n        if (params.dynamicBullets) {\n          var dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n          var bulletsOffset = (((swiper.pagination.bulletSize * dynamicBulletsLength) - (swiper.pagination.bulletSize)) / 2) - (midIndex * swiper.pagination.bulletSize);\n          var offsetProp = rtl ? 'right' : 'left';\n          bullets.css(swiper.isHorizontal() ? offsetProp : 'top', (bulletsOffset + \"px\"));\n        }\n      }\n      if (params.type === 'fraction') {\n        $el.find((\".\" + (params.currentClass))).text(params.formatFractionCurrent(current + 1));\n        $el.find((\".\" + (params.totalClass))).text(params.formatFractionTotal(total));\n      }\n      if (params.type === 'progressbar') {\n        var progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        var scale = (current + 1) / total;\n        var scaleX = 1;\n        var scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        $el.find((\".\" + (params.progressbarFillClass))).transform((\"translate3d(0,0,0) scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\")).transition(swiper.params.speed);\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        $el.html(params.renderCustom(swiper, current + 1, total));\n        swiper.emit('paginationRender', swiper, $el[0]);\n      } else {\n        swiper.emit('paginationUpdate', swiper, $el[0]);\n      }\n      $el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n    },\n    render: function render() {\n      // Render Container\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n\n      var $el = swiper.pagination.$el;\n      var paginationHTML = '';\n      if (params.type === 'bullets') {\n        var numberOfBullets = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n        for (var i = 0; i < numberOfBullets; i += 1) {\n          if (params.renderBullet) {\n            paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n          } else {\n            paginationHTML += \"<\" + (params.bulletElement) + \" class=\\\"\" + (params.bulletClass) + \"\\\"></\" + (params.bulletElement) + \">\";\n          }\n        }\n        $el.html(paginationHTML);\n        swiper.pagination.bullets = $el.find((\".\" + (params.bulletClass)));\n      }\n      if (params.type === 'fraction') {\n        if (params.renderFraction) {\n          paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.currentClass) + \"\\\"></span>\"\n          + ' / '\n          + \"<span class=\\\"\" + (params.totalClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type === 'progressbar') {\n        if (params.renderProgressbar) {\n          paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.progressbarFillClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type !== 'custom') {\n        swiper.emit('paginationRender', swiper.pagination.$el[0]);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el) { return; }\n\n      var $el = $(params.el);\n      if ($el.length === 0) { return; }\n\n      if (\n        swiper.params.uniqueNavElements\n        && typeof params.el === 'string'\n        && $el.length > 1\n        && swiper.$el.find(params.el).length === 1\n      ) {\n        $el = swiper.$el.find(params.el);\n      }\n\n      if (params.type === 'bullets' && params.clickable) {\n        $el.addClass(params.clickableClass);\n      }\n\n      $el.addClass(params.modifierClass + params.type);\n\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        $el.addClass((\"\" + (params.modifierClass) + (params.type) + \"-dynamic\"));\n        swiper.pagination.dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        $el.addClass(params.progressbarOppositeClass);\n      }\n\n      if (params.clickable) {\n        $el.on('click', (\".\" + (params.bulletClass)), function onClick(e) {\n          e.preventDefault();\n          var index = $(this).index() * swiper.params.slidesPerGroup;\n          if (swiper.params.loop) { index += swiper.loopedSlides; }\n          swiper.slideTo(index);\n        });\n      }\n\n      Utils.extend(swiper.pagination, {\n        $el: $el,\n        el: $el[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var $el = swiper.pagination.$el;\n\n      $el.removeClass(params.hiddenClass);\n      $el.removeClass(params.modifierClass + params.type);\n      if (swiper.pagination.bullets) { swiper.pagination.bullets.removeClass(params.bulletActiveClass); }\n      if (params.clickable) {\n        $el.off('click', (\".\" + (params.bulletClass)));\n      }\n    },\n  };\n\n  var Pagination$1 = {\n    name: 'pagination',\n    params: {\n      pagination: {\n        el: null,\n        bulletElement: 'span',\n        clickable: false,\n        hideOnClick: false,\n        renderBullet: null,\n        renderProgressbar: null,\n        renderFraction: null,\n        renderCustom: null,\n        progressbarOpposite: false,\n        type: 'bullets', // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n        dynamicBullets: false,\n        dynamicMainBullets: 1,\n        formatFractionCurrent: function (number) { return number; },\n        formatFractionTotal: function (number) { return number; },\n        bulletClass: 'swiper-pagination-bullet',\n        bulletActiveClass: 'swiper-pagination-bullet-active',\n        modifierClass: 'swiper-pagination-', // NEW\n        currentClass: 'swiper-pagination-current',\n        totalClass: 'swiper-pagination-total',\n        hiddenClass: 'swiper-pagination-hidden',\n        progressbarFillClass: 'swiper-pagination-progressbar-fill',\n        progressbarOppositeClass: 'swiper-pagination-progressbar-opposite',\n        clickableClass: 'swiper-pagination-clickable', // NEW\n        lockClass: 'swiper-pagination-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        pagination: {\n          init: Pagination.init.bind(swiper),\n          render: Pagination.render.bind(swiper),\n          update: Pagination.update.bind(swiper),\n          destroy: Pagination.destroy.bind(swiper),\n          dynamicBulletIndex: 0,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.pagination.init();\n        swiper.pagination.render();\n        swiper.pagination.update();\n      },\n      activeIndexChange: function activeIndexChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.update();\n        } else if (typeof swiper.snapIndex === 'undefined') {\n          swiper.pagination.update();\n        }\n      },\n      snapIndexChange: function snapIndexChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.update();\n        }\n      },\n      slidesLengthChange: function slidesLengthChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      snapGridLengthChange: function snapGridLengthChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.pagination.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        if (\n          swiper.params.pagination.el\n          && swiper.params.pagination.hideOnClick\n          && swiper.pagination.$el.length > 0\n          && !$(e.target).hasClass(swiper.params.pagination.bulletClass)\n        ) {\n          swiper.pagination.$el.toggleClass(swiper.params.pagination.hiddenClass);\n        }\n      },\n    },\n  };\n\n  var Scrollbar = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var progress = swiper.progress;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n      var params = swiper.params.scrollbar;\n\n      var newSize = dragSize;\n      var newPos = (trackSize - dragSize) * progress;\n      if (rtl) {\n        newPos = -newPos;\n        if (newPos > 0) {\n          newSize = dragSize - newPos;\n          newPos = 0;\n        } else if (-newPos + dragSize > trackSize) {\n          newSize = trackSize + newPos;\n        }\n      } else if (newPos < 0) {\n        newSize = dragSize + newPos;\n        newPos = 0;\n      } else if (newPos + dragSize > trackSize) {\n        newSize = trackSize - newPos;\n      }\n      if (swiper.isHorizontal()) {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(\" + newPos + \"px, 0, 0)\"));\n        } else {\n          $dragEl.transform((\"translateX(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.width = newSize + \"px\";\n      } else {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(0px, \" + newPos + \"px, 0)\"));\n        } else {\n          $dragEl.transform((\"translateY(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.height = newSize + \"px\";\n      }\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.timeout);\n        $el[0].style.opacity = 1;\n        swiper.scrollbar.timeout = setTimeout(function () {\n          $el[0].style.opacity = 0;\n          $el.transition(400);\n        }, 1000);\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      swiper.scrollbar.$dragEl.transition(duration);\n    },\n    updateSize: function updateSize() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n\n      var scrollbar = swiper.scrollbar;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n\n      $dragEl[0].style.width = '';\n      $dragEl[0].style.height = '';\n      var trackSize = swiper.isHorizontal() ? $el[0].offsetWidth : $el[0].offsetHeight;\n\n      var divider = swiper.size / swiper.virtualSize;\n      var moveDivider = divider * (trackSize / swiper.size);\n      var dragSize;\n      if (swiper.params.scrollbar.dragSize === 'auto') {\n        dragSize = trackSize * divider;\n      } else {\n        dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n      }\n\n      if (swiper.isHorizontal()) {\n        $dragEl[0].style.width = dragSize + \"px\";\n      } else {\n        $dragEl[0].style.height = dragSize + \"px\";\n      }\n\n      if (divider >= 1) {\n        $el[0].style.display = 'none';\n      } else {\n        $el[0].style.display = '';\n      }\n      if (swiper.params.scrollbarHide) {\n        $el[0].style.opacity = 0;\n      }\n      Utils.extend(scrollbar, {\n        trackSize: trackSize,\n        divider: divider,\n        moveDivider: moveDivider,\n        dragSize: dragSize,\n      });\n      scrollbar.$el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](swiper.params.scrollbar.lockClass);\n    },\n    setDragPosition: function setDragPosition(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var $el = scrollbar.$el;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n\n      var pointerPosition;\n      if (swiper.isHorizontal()) {\n        pointerPosition = ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageX : e.pageX || e.clientX);\n      } else {\n        pointerPosition = ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageY : e.pageY || e.clientY);\n      }\n      var positionRatio;\n      positionRatio = ((pointerPosition) - $el.offset()[swiper.isHorizontal() ? 'left' : 'top'] - (dragSize / 2)) / (trackSize - dragSize);\n      positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n      if (rtl) {\n        positionRatio = 1 - positionRatio;\n      }\n\n      var position = swiper.minTranslate() + ((swiper.maxTranslate() - swiper.minTranslate()) * positionRatio);\n\n      swiper.updateProgress(position);\n      swiper.setTranslate(position);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    },\n    onDragStart: function onDragStart(e) {\n      var swiper = this;\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n      swiper.scrollbar.isTouched = true;\n      e.preventDefault();\n      e.stopPropagation();\n\n      $wrapperEl.transition(100);\n      $dragEl.transition(100);\n      scrollbar.setDragPosition(e);\n\n      clearTimeout(swiper.scrollbar.dragTimeout);\n\n      $el.transition(0);\n      if (params.hide) {\n        $el.css('opacity', 1);\n      }\n      swiper.emit('scrollbarDragStart', e);\n    },\n    onDragMove: function onDragMove(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      scrollbar.setDragPosition(e);\n      $wrapperEl.transition(0);\n      $el.transition(0);\n      $dragEl.transition(0);\n      swiper.emit('scrollbarDragMove', e);\n    },\n    onDragEnd: function onDragEnd(e) {\n      var swiper = this;\n\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $el = scrollbar.$el;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      swiper.scrollbar.isTouched = false;\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.dragTimeout);\n        swiper.scrollbar.dragTimeout = Utils.nextTick(function () {\n          $el.css('opacity', 0);\n          $el.transition(400);\n        }, 1000);\n      }\n      swiper.emit('scrollbarDragEnd', e);\n      if (params.snapOnRelease) {\n        swiper.slideToClosest();\n      }\n    },\n    enableDraggable: function enableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEvents = swiper.touchEvents;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.addEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.addEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.addEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        if (Support.touch) {\n          target.addEventListener(touchEvents.start, swiper.scrollbar.onDragStart, activeListener);\n          target.addEventListener(touchEvents.move, swiper.scrollbar.onDragMove, activeListener);\n          target.addEventListener(touchEvents.end, swiper.scrollbar.onDragEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.addEventListener('mousedown', swiper.scrollbar.onDragStart, activeListener);\n          doc.addEventListener('mousemove', swiper.scrollbar.onDragMove, activeListener);\n          doc.addEventListener('mouseup', swiper.scrollbar.onDragEnd, passiveListener);\n        }\n      }\n    },\n    disableDraggable: function disableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEvents = swiper.touchEvents;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.removeEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.removeEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.removeEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        if (Support.touch) {\n          target.removeEventListener(touchEvents.start, swiper.scrollbar.onDragStart, activeListener);\n          target.removeEventListener(touchEvents.move, swiper.scrollbar.onDragMove, activeListener);\n          target.removeEventListener(touchEvents.end, swiper.scrollbar.onDragEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.removeEventListener('mousedown', swiper.scrollbar.onDragStart, activeListener);\n          doc.removeEventListener('mousemove', swiper.scrollbar.onDragMove, activeListener);\n          doc.removeEventListener('mouseup', swiper.scrollbar.onDragEnd, passiveListener);\n        }\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var $swiperEl = swiper.$el;\n      var params = swiper.params.scrollbar;\n\n      var $el = $(params.el);\n      if (swiper.params.uniqueNavElements && typeof params.el === 'string' && $el.length > 1 && $swiperEl.find(params.el).length === 1) {\n        $el = $swiperEl.find(params.el);\n      }\n\n      var $dragEl = $el.find((\".\" + (swiper.params.scrollbar.dragClass)));\n      if ($dragEl.length === 0) {\n        $dragEl = $((\"<div class=\\\"\" + (swiper.params.scrollbar.dragClass) + \"\\\"></div>\"));\n        $el.append($dragEl);\n      }\n\n      Utils.extend(scrollbar, {\n        $el: $el,\n        el: $el[0],\n        $dragEl: $dragEl,\n        dragEl: $dragEl[0],\n      });\n\n      if (params.draggable) {\n        scrollbar.enableDraggable();\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.scrollbar.disableDraggable();\n    },\n  };\n\n  var Scrollbar$1 = {\n    name: 'scrollbar',\n    params: {\n      scrollbar: {\n        el: null,\n        dragSize: 'auto',\n        hide: false,\n        draggable: false,\n        snapOnRelease: true,\n        lockClass: 'swiper-scrollbar-lock',\n        dragClass: 'swiper-scrollbar-drag',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        scrollbar: {\n          init: Scrollbar.init.bind(swiper),\n          destroy: Scrollbar.destroy.bind(swiper),\n          updateSize: Scrollbar.updateSize.bind(swiper),\n          setTranslate: Scrollbar.setTranslate.bind(swiper),\n          setTransition: Scrollbar.setTransition.bind(swiper),\n          enableDraggable: Scrollbar.enableDraggable.bind(swiper),\n          disableDraggable: Scrollbar.disableDraggable.bind(swiper),\n          setDragPosition: Scrollbar.setDragPosition.bind(swiper),\n          onDragStart: Scrollbar.onDragStart.bind(swiper),\n          onDragMove: Scrollbar.onDragMove.bind(swiper),\n          onDragEnd: Scrollbar.onDragEnd.bind(swiper),\n          isTouched: false,\n          timeout: null,\n          dragTimeout: null,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.scrollbar.init();\n        swiper.scrollbar.updateSize();\n        swiper.scrollbar.setTranslate();\n      },\n      update: function update() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      resize: function resize() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        swiper.scrollbar.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        swiper.scrollbar.setTransition(duration);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.scrollbar.destroy();\n      },\n    },\n  };\n\n  var Parallax = {\n    setTransform: function setTransform(el, progress) {\n      var swiper = this;\n      var rtl = swiper.rtl;\n\n      var $el = $(el);\n      var rtlFactor = rtl ? -1 : 1;\n\n      var p = $el.attr('data-swiper-parallax') || '0';\n      var x = $el.attr('data-swiper-parallax-x');\n      var y = $el.attr('data-swiper-parallax-y');\n      var scale = $el.attr('data-swiper-parallax-scale');\n      var opacity = $el.attr('data-swiper-parallax-opacity');\n\n      if (x || y) {\n        x = x || '0';\n        y = y || '0';\n      } else if (swiper.isHorizontal()) {\n        x = p;\n        y = '0';\n      } else {\n        y = p;\n        x = '0';\n      }\n\n      if ((x).indexOf('%') >= 0) {\n        x = (parseInt(x, 10) * progress * rtlFactor) + \"%\";\n      } else {\n        x = (x * progress * rtlFactor) + \"px\";\n      }\n      if ((y).indexOf('%') >= 0) {\n        y = (parseInt(y, 10) * progress) + \"%\";\n      } else {\n        y = (y * progress) + \"px\";\n      }\n\n      if (typeof opacity !== 'undefined' && opacity !== null) {\n        var currentOpacity = opacity - ((opacity - 1) * (1 - Math.abs(progress)));\n        $el[0].style.opacity = currentOpacity;\n      }\n      if (typeof scale === 'undefined' || scale === null) {\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px)\"));\n      } else {\n        var currentScale = scale - ((scale - 1) * (1 - Math.abs(progress)));\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px) scale(\" + currentScale + \")\"));\n      }\n    },\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      var progress = swiper.progress;\n      var snapGrid = swiper.snapGrid;\n      $el.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n        .each(function (index, el) {\n          swiper.parallax.setTransform(el, progress);\n        });\n      slides.each(function (slideIndex, slideEl) {\n        var slideProgress = slideEl.progress;\n        if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n          slideProgress += Math.ceil(slideIndex / 2) - (progress * (snapGrid.length - 1));\n        }\n        slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n        $(slideEl).find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n          .each(function (index, el) {\n            swiper.parallax.setTransform(el, slideProgress);\n          });\n      });\n    },\n    setTransition: function setTransition(duration) {\n      if ( duration === void 0 ) duration = this.params.speed;\n\n      var swiper = this;\n      var $el = swiper.$el;\n      $el.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n        .each(function (index, parallaxEl) {\n          var $parallaxEl = $(parallaxEl);\n          var parallaxDuration = parseInt($parallaxEl.attr('data-swiper-parallax-duration'), 10) || duration;\n          if (duration === 0) { parallaxDuration = 0; }\n          $parallaxEl.transition(parallaxDuration);\n        });\n    },\n  };\n\n  var Parallax$1 = {\n    name: 'parallax',\n    params: {\n      parallax: {\n        enabled: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        parallax: {\n          setTransform: Parallax.setTransform.bind(swiper),\n          setTranslate: Parallax.setTranslate.bind(swiper),\n          setTransition: Parallax.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.params.watchSlidesProgress = true;\n        swiper.originalParams.watchSlidesProgress = true;\n      },\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.parallax) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.parallax) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (!swiper.params.parallax) { return; }\n        swiper.parallax.setTransition(duration);\n      },\n    },\n  };\n\n  var Zoom = {\n    // Calc Scale From Multi-touches\n    getDistanceBetweenTouches: function getDistanceBetweenTouches(e) {\n      if (e.targetTouches.length < 2) { return 1; }\n      var x1 = e.targetTouches[0].pageX;\n      var y1 = e.targetTouches[0].pageY;\n      var x2 = e.targetTouches[1].pageX;\n      var y2 = e.targetTouches[1].pageY;\n      var distance = Math.sqrt((Math.pow( (x2 - x1), 2 )) + (Math.pow( (y2 - y1), 2 )));\n      return distance;\n    },\n    // Events\n    onGestureStart: function onGestureStart(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      zoom.fakeGestureTouched = false;\n      zoom.fakeGestureMoved = false;\n      if (!Support.gestures) {\n        if (e.type !== 'touchstart' || (e.type === 'touchstart' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureTouched = true;\n        gesture.scaleStart = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$slideEl || !gesture.$slideEl.length) {\n        gesture.$slideEl = $(e.target).closest('.swiper-slide');\n        if (gesture.$slideEl.length === 0) { gesture.$slideEl = swiper.slides.eq(swiper.activeIndex); }\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n        gesture.maxRatio = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n        if (gesture.$imageWrapEl.length === 0) {\n          gesture.$imageEl = undefined;\n          return;\n        }\n      }\n      gesture.$imageEl.transition(0);\n      swiper.zoom.isScaling = true;\n    },\n    onGestureChange: function onGestureChange(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (e.type !== 'touchmove' || (e.type === 'touchmove' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureMoved = true;\n        gesture.scaleMove = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (Support.gestures) {\n        swiper.zoom.scale = e.scale * zoom.currentScale;\n      } else {\n        zoom.scale = (gesture.scaleMove / gesture.scaleStart) * zoom.currentScale;\n      }\n      if (zoom.scale > gesture.maxRatio) {\n        zoom.scale = (gesture.maxRatio - 1) + (Math.pow( ((zoom.scale - gesture.maxRatio) + 1), 0.5 ));\n      }\n      if (zoom.scale < params.minRatio) {\n        zoom.scale = (params.minRatio + 1) - (Math.pow( ((params.minRatio - zoom.scale) + 1), 0.5 ));\n      }\n      gesture.$imageEl.transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    onGestureEnd: function onGestureEnd(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (!zoom.fakeGestureTouched || !zoom.fakeGestureMoved) {\n          return;\n        }\n        if (e.type !== 'touchend' || (e.type === 'touchend' && e.changedTouches.length < 2 && !Device.android)) {\n          return;\n        }\n        zoom.fakeGestureTouched = false;\n        zoom.fakeGestureMoved = false;\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n      gesture.$imageEl.transition(swiper.params.speed).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n      zoom.currentScale = zoom.scale;\n      zoom.isScaling = false;\n      if (zoom.scale === 1) { gesture.$slideEl = undefined; }\n    },\n    onTouchStart: function onTouchStart(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (image.isTouched) { return; }\n      if (Device.android) { e.preventDefault(); }\n      image.isTouched = true;\n      image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    },\n    onTouchMove: function onTouchMove(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      swiper.allowClick = false;\n      if (!image.isTouched || !gesture.$slideEl) { return; }\n\n      if (!image.isMoved) {\n        image.width = gesture.$imageEl[0].offsetWidth;\n        image.height = gesture.$imageEl[0].offsetHeight;\n        image.startX = Utils.getTranslate(gesture.$imageWrapEl[0], 'x') || 0;\n        image.startY = Utils.getTranslate(gesture.$imageWrapEl[0], 'y') || 0;\n        gesture.slideWidth = gesture.$slideEl[0].offsetWidth;\n        gesture.slideHeight = gesture.$slideEl[0].offsetHeight;\n        gesture.$imageWrapEl.transition(0);\n        if (swiper.rtl) {\n          image.startX = -image.startX;\n          image.startY = -image.startY;\n        }\n      }\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n\n      if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) { return; }\n\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n\n      image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n\n      if (!image.isMoved && !zoom.isScaling) {\n        if (\n          swiper.isHorizontal()\n          && (\n            (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x)\n            || (Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        } if (\n          !swiper.isHorizontal()\n          && (\n            (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y)\n            || (Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        }\n      }\n      e.preventDefault();\n      e.stopPropagation();\n\n      image.isMoved = true;\n      image.currentX = (image.touchesCurrent.x - image.touchesStart.x) + image.startX;\n      image.currentY = (image.touchesCurrent.y - image.touchesStart.y) + image.startY;\n\n      if (image.currentX < image.minX) {\n        image.currentX = (image.minX + 1) - (Math.pow( ((image.minX - image.currentX) + 1), 0.8 ));\n      }\n      if (image.currentX > image.maxX) {\n        image.currentX = (image.maxX - 1) + (Math.pow( ((image.currentX - image.maxX) + 1), 0.8 ));\n      }\n\n      if (image.currentY < image.minY) {\n        image.currentY = (image.minY + 1) - (Math.pow( ((image.minY - image.currentY) + 1), 0.8 ));\n      }\n      if (image.currentY > image.maxY) {\n        image.currentY = (image.maxY - 1) + (Math.pow( ((image.currentY - image.maxY) + 1), 0.8 ));\n      }\n\n      // Velocity\n      if (!velocity.prevPositionX) { velocity.prevPositionX = image.touchesCurrent.x; }\n      if (!velocity.prevPositionY) { velocity.prevPositionY = image.touchesCurrent.y; }\n      if (!velocity.prevTime) { velocity.prevTime = Date.now(); }\n      velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n      velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n      if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) { velocity.x = 0; }\n      if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) { velocity.y = 0; }\n      velocity.prevPositionX = image.touchesCurrent.x;\n      velocity.prevPositionY = image.touchesCurrent.y;\n      velocity.prevTime = Date.now();\n\n      gesture.$imageWrapEl.transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTouchEnd: function onTouchEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (!image.isTouched || !image.isMoved) {\n        image.isTouched = false;\n        image.isMoved = false;\n        return;\n      }\n      image.isTouched = false;\n      image.isMoved = false;\n      var momentumDurationX = 300;\n      var momentumDurationY = 300;\n      var momentumDistanceX = velocity.x * momentumDurationX;\n      var newPositionX = image.currentX + momentumDistanceX;\n      var momentumDistanceY = velocity.y * momentumDurationY;\n      var newPositionY = image.currentY + momentumDistanceY;\n\n      // Fix duration\n      if (velocity.x !== 0) { momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x); }\n      if (velocity.y !== 0) { momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y); }\n      var momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n\n      image.currentX = newPositionX;\n      image.currentY = newPositionY;\n\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n      image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n      image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n\n      gesture.$imageWrapEl.transition(momentumDuration).transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTransitionEnd: function onTransitionEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (gesture.$slideEl && swiper.previousIndex !== swiper.activeIndex) {\n        gesture.$imageEl.transform('translate3d(0,0,0) scale(1)');\n        gesture.$imageWrapEl.transform('translate3d(0,0,0)');\n        gesture.$slideEl = undefined;\n        gesture.$imageEl = undefined;\n        gesture.$imageWrapEl = undefined;\n\n        zoom.scale = 1;\n        zoom.currentScale = 1;\n      }\n    },\n    // Toggle Zoom\n    toggle: function toggle(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n\n      if (zoom.scale && zoom.scale !== 1) {\n        // Zoom Out\n        zoom.out();\n      } else {\n        // Zoom In\n        zoom.in(e);\n      }\n    },\n    in: function in$1(e) {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      gesture.$slideEl.addClass((\"\" + (params.zoomedSlideClass)));\n\n      var touchX;\n      var touchY;\n      var offsetX;\n      var offsetY;\n      var diffX;\n      var diffY;\n      var translateX;\n      var translateY;\n      var imageWidth;\n      var imageHeight;\n      var scaledWidth;\n      var scaledHeight;\n      var translateMinX;\n      var translateMinY;\n      var translateMaxX;\n      var translateMaxY;\n      var slideWidth;\n      var slideHeight;\n\n      if (typeof image.touchesStart.x === 'undefined' && e) {\n        touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n        touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n      } else {\n        touchX = image.touchesStart.x;\n        touchY = image.touchesStart.y;\n      }\n\n      zoom.scale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      zoom.currentScale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      if (e) {\n        slideWidth = gesture.$slideEl[0].offsetWidth;\n        slideHeight = gesture.$slideEl[0].offsetHeight;\n        offsetX = gesture.$slideEl.offset().left;\n        offsetY = gesture.$slideEl.offset().top;\n        diffX = (offsetX + (slideWidth / 2)) - touchX;\n        diffY = (offsetY + (slideHeight / 2)) - touchY;\n\n        imageWidth = gesture.$imageEl[0].offsetWidth;\n        imageHeight = gesture.$imageEl[0].offsetHeight;\n        scaledWidth = imageWidth * zoom.scale;\n        scaledHeight = imageHeight * zoom.scale;\n\n        translateMinX = Math.min(((slideWidth / 2) - (scaledWidth / 2)), 0);\n        translateMinY = Math.min(((slideHeight / 2) - (scaledHeight / 2)), 0);\n        translateMaxX = -translateMinX;\n        translateMaxY = -translateMinY;\n\n        translateX = diffX * zoom.scale;\n        translateY = diffY * zoom.scale;\n\n        if (translateX < translateMinX) {\n          translateX = translateMinX;\n        }\n        if (translateX > translateMaxX) {\n          translateX = translateMaxX;\n        }\n\n        if (translateY < translateMinY) {\n          translateY = translateMinY;\n        }\n        if (translateY > translateMaxY) {\n          translateY = translateMaxY;\n        }\n      } else {\n        translateX = 0;\n        translateY = 0;\n      }\n      gesture.$imageWrapEl.transition(300).transform((\"translate3d(\" + translateX + \"px, \" + translateY + \"px,0)\"));\n      gesture.$imageEl.transition(300).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    out: function out() {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      zoom.scale = 1;\n      zoom.currentScale = 1;\n      gesture.$imageWrapEl.transition(300).transform('translate3d(0,0,0)');\n      gesture.$imageEl.transition(300).transform('translate3d(0,0,0) scale(1)');\n      gesture.$slideEl.removeClass((\"\" + (params.zoomedSlideClass)));\n      gesture.$slideEl = undefined;\n    },\n    // Attach/Detach Events\n    enable: function enable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (zoom.enabled) { return; }\n      zoom.enabled = true;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.on('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.on(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.on(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n    disable: function disable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (!zoom.enabled) { return; }\n\n      swiper.zoom.enabled = false;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.off('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.off(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.off(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n  };\n\n  var Zoom$1 = {\n    name: 'zoom',\n    params: {\n      zoom: {\n        enabled: false,\n        maxRatio: 3,\n        minRatio: 1,\n        toggle: true,\n        containerClass: 'swiper-zoom-container',\n        zoomedSlideClass: 'swiper-slide-zoomed',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      var zoom = {\n        enabled: false,\n        scale: 1,\n        currentScale: 1,\n        isScaling: false,\n        gesture: {\n          $slideEl: undefined,\n          slideWidth: undefined,\n          slideHeight: undefined,\n          $imageEl: undefined,\n          $imageWrapEl: undefined,\n          maxRatio: 3,\n        },\n        image: {\n          isTouched: undefined,\n          isMoved: undefined,\n          currentX: undefined,\n          currentY: undefined,\n          minX: undefined,\n          minY: undefined,\n          maxX: undefined,\n          maxY: undefined,\n          width: undefined,\n          height: undefined,\n          startX: undefined,\n          startY: undefined,\n          touchesStart: {},\n          touchesCurrent: {},\n        },\n        velocity: {\n          x: undefined,\n          y: undefined,\n          prevPositionX: undefined,\n          prevPositionY: undefined,\n          prevTime: undefined,\n        },\n      };\n      ('onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out').split(' ').forEach(function (methodName) {\n        zoom[methodName] = Zoom[methodName].bind(swiper);\n      });\n      Utils.extend(swiper, {\n        zoom: zoom,\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.zoom.enabled) {\n          swiper.zoom.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.zoom.disable();\n      },\n      touchStart: function touchStart(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchStart(e);\n      },\n      touchEnd: function touchEnd(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchEnd(e);\n      },\n      doubleTap: function doubleTap(e) {\n        var swiper = this;\n        if (swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n          swiper.zoom.toggle(e);\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n          swiper.zoom.onTransitionEnd();\n        }\n      },\n    },\n  };\n\n  var Lazy = {\n    loadInSlide: function loadInSlide(index, loadInDuplicate) {\n      if ( loadInDuplicate === void 0 ) loadInDuplicate = true;\n\n      var swiper = this;\n      var params = swiper.params.lazy;\n      if (typeof index === 'undefined') { return; }\n      if (swiper.slides.length === 0) { return; }\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n      var $slideEl = isVirtual\n        ? swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\"))\n        : swiper.slides.eq(index);\n\n      var $images = $slideEl.find((\".\" + (params.elementClass) + \":not(.\" + (params.loadedClass) + \"):not(.\" + (params.loadingClass) + \")\"));\n      if ($slideEl.hasClass(params.elementClass) && !$slideEl.hasClass(params.loadedClass) && !$slideEl.hasClass(params.loadingClass)) {\n        $images = $images.add($slideEl[0]);\n      }\n      if ($images.length === 0) { return; }\n\n      $images.each(function (imageIndex, imageEl) {\n        var $imageEl = $(imageEl);\n        $imageEl.addClass(params.loadingClass);\n\n        var background = $imageEl.attr('data-background');\n        var src = $imageEl.attr('data-src');\n        var srcset = $imageEl.attr('data-srcset');\n        var sizes = $imageEl.attr('data-sizes');\n\n        swiper.loadImage($imageEl[0], (src || background), srcset, sizes, false, function () {\n          if (typeof swiper === 'undefined' || swiper === null || !swiper || (swiper && !swiper.params) || swiper.destroyed) { return; }\n          if (background) {\n            $imageEl.css('background-image', (\"url(\\\"\" + background + \"\\\")\"));\n            $imageEl.removeAttr('data-background');\n          } else {\n            if (srcset) {\n              $imageEl.attr('srcset', srcset);\n              $imageEl.removeAttr('data-srcset');\n            }\n            if (sizes) {\n              $imageEl.attr('sizes', sizes);\n              $imageEl.removeAttr('data-sizes');\n            }\n            if (src) {\n              $imageEl.attr('src', src);\n              $imageEl.removeAttr('data-src');\n            }\n          }\n\n          $imageEl.addClass(params.loadedClass).removeClass(params.loadingClass);\n          $slideEl.find((\".\" + (params.preloaderClass))).remove();\n          if (swiper.params.loop && loadInDuplicate) {\n            var slideOriginalIndex = $slideEl.attr('data-swiper-slide-index');\n            if ($slideEl.hasClass(swiper.params.slideDuplicateClass)) {\n              var originalSlide = swiper.$wrapperEl.children((\"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]:not(.\" + (swiper.params.slideDuplicateClass) + \")\"));\n              swiper.lazy.loadInSlide(originalSlide.index(), false);\n            } else {\n              var duplicatedSlide = swiper.$wrapperEl.children((\".\" + (swiper.params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]\"));\n              swiper.lazy.loadInSlide(duplicatedSlide.index(), false);\n            }\n          }\n          swiper.emit('lazyImageReady', $slideEl[0], $imageEl[0]);\n        });\n\n        swiper.emit('lazyImageLoad', $slideEl[0], $imageEl[0]);\n      });\n    },\n    load: function load() {\n      var swiper = this;\n      var $wrapperEl = swiper.$wrapperEl;\n      var swiperParams = swiper.params;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var isVirtual = swiper.virtual && swiperParams.virtual.enabled;\n      var params = swiperParams.lazy;\n\n      var slidesPerView = swiperParams.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = 0;\n      }\n\n      function slideExist(index) {\n        if (isVirtual) {\n          if ($wrapperEl.children((\".\" + (swiperParams.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\")).length) {\n            return true;\n          }\n        } else if (slides[index]) { return true; }\n        return false;\n      }\n      function slideIndex(slideEl) {\n        if (isVirtual) {\n          return $(slideEl).attr('data-swiper-slide-index');\n        }\n        return $(slideEl).index();\n      }\n\n      if (!swiper.lazy.initialImageLoaded) { swiper.lazy.initialImageLoaded = true; }\n      if (swiper.params.watchSlidesVisibility) {\n        $wrapperEl.children((\".\" + (swiperParams.slideVisibleClass))).each(function (elIndex, slideEl) {\n          var index = isVirtual ? $(slideEl).attr('data-swiper-slide-index') : $(slideEl).index();\n          swiper.lazy.loadInSlide(index);\n        });\n      } else if (slidesPerView > 1) {\n        for (var i = activeIndex; i < activeIndex + slidesPerView; i += 1) {\n          if (slideExist(i)) { swiper.lazy.loadInSlide(i); }\n        }\n      } else {\n        swiper.lazy.loadInSlide(activeIndex);\n      }\n      if (params.loadPrevNext) {\n        if (slidesPerView > 1 || (params.loadPrevNextAmount && params.loadPrevNextAmount > 1)) {\n          var amount = params.loadPrevNextAmount;\n          var spv = slidesPerView;\n          var maxIndex = Math.min(activeIndex + spv + Math.max(amount, spv), slides.length);\n          var minIndex = Math.max(activeIndex - Math.max(spv, amount), 0);\n          // Next Slides\n          for (var i$1 = activeIndex + slidesPerView; i$1 < maxIndex; i$1 += 1) {\n            if (slideExist(i$1)) { swiper.lazy.loadInSlide(i$1); }\n          }\n          // Prev Slides\n          for (var i$2 = minIndex; i$2 < activeIndex; i$2 += 1) {\n            if (slideExist(i$2)) { swiper.lazy.loadInSlide(i$2); }\n          }\n        } else {\n          var nextSlide = $wrapperEl.children((\".\" + (swiperParams.slideNextClass)));\n          if (nextSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(nextSlide)); }\n\n          var prevSlide = $wrapperEl.children((\".\" + (swiperParams.slidePrevClass)));\n          if (prevSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(prevSlide)); }\n        }\n      }\n    },\n  };\n\n  var Lazy$1 = {\n    name: 'lazy',\n    params: {\n      lazy: {\n        enabled: false,\n        loadPrevNext: false,\n        loadPrevNextAmount: 1,\n        loadOnTransitionStart: false,\n\n        elementClass: 'swiper-lazy',\n        loadingClass: 'swiper-lazy-loading',\n        loadedClass: 'swiper-lazy-loaded',\n        preloaderClass: 'swiper-lazy-preloader',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        lazy: {\n          initialImageLoaded: false,\n          load: Lazy.load.bind(swiper),\n          loadInSlide: Lazy.loadInSlide.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && swiper.params.preloadImages) {\n          swiper.params.preloadImages = false;\n        }\n      },\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.loop && swiper.params.initialSlide === 0) {\n          swiper.lazy.load();\n        }\n      },\n      scroll: function scroll() {\n        var swiper = this;\n        if (swiper.params.freeMode && !swiper.params.freeModeSticky) {\n          swiper.lazy.load();\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      scrollbarDragMove: function scrollbarDragMove() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      transitionStart: function transitionStart() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          if (swiper.params.lazy.loadOnTransitionStart || (!swiper.params.lazy.loadOnTransitionStart && !swiper.lazy.initialImageLoaded)) {\n            swiper.lazy.load();\n          }\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.lazy.loadOnTransitionStart) {\n          swiper.lazy.load();\n        }\n      },\n    },\n  };\n\n  /* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\n\n  var Controller = {\n    LinearSpline: function LinearSpline(x, y) {\n      var binarySearch = (function search() {\n        var maxIndex;\n        var minIndex;\n        var guess;\n        return function (array, val) {\n          minIndex = -1;\n          maxIndex = array.length;\n          while (maxIndex - minIndex > 1) {\n            guess = maxIndex + minIndex >> 1;\n            if (array[guess] <= val) {\n              minIndex = guess;\n            } else {\n              maxIndex = guess;\n            }\n          }\n          return maxIndex;\n        };\n      }());\n      this.x = x;\n      this.y = y;\n      this.lastIndex = x.length - 1;\n      // Given an x value (x2), return the expected y2 value:\n      // (x1,y1) is the known point before given value,\n      // (x3,y3) is the known point after given value.\n      var i1;\n      var i3;\n\n      this.interpolate = function interpolate(x2) {\n        if (!x2) { return 0; }\n\n        // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n        i3 = binarySearch(this.x, x2);\n        i1 = i3 - 1;\n\n        // We have our indexes i1 & i3, so we can calculate already:\n        // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n        return (((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1])) + this.y[i1];\n      };\n      return this;\n    },\n    // xxx: for now i will just save one spline function to to\n    getInterpolateFunction: function getInterpolateFunction(c) {\n      var swiper = this;\n      if (!swiper.controller.spline) {\n        swiper.controller.spline = swiper.params.loop\n          ? new Controller.LinearSpline(swiper.slidesGrid, c.slidesGrid)\n          : new Controller.LinearSpline(swiper.snapGrid, c.snapGrid);\n      }\n    },\n    setTranslate: function setTranslate(setTranslate$1, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var multiplier;\n      var controlledTranslate;\n      function setControlledTranslate(c) {\n        // this will create an Interpolate function based on the snapGrids\n        // x is the Grid of the scrolled scroller and y will be the controlled scroller\n        // it makes sense to create this only once and recall it for the interpolation\n        // the function does a lot of value caching for performance\n        var translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n        if (swiper.params.controller.by === 'slide') {\n          swiper.controller.getInterpolateFunction(c);\n          // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n          // but it did not work out\n          controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n        }\n\n        if (!controlledTranslate || swiper.params.controller.by === 'container') {\n          multiplier = (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n          controlledTranslate = ((translate - swiper.minTranslate()) * multiplier) + c.minTranslate();\n        }\n\n        if (swiper.params.controller.inverse) {\n          controlledTranslate = c.maxTranslate() - controlledTranslate;\n        }\n        c.updateProgress(controlledTranslate);\n        c.setTranslate(controlledTranslate, swiper);\n        c.updateActiveIndex();\n        c.updateSlidesClasses();\n      }\n      if (Array.isArray(controlled)) {\n        for (var i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTranslate(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTranslate(controlled);\n      }\n    },\n    setTransition: function setTransition(duration, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var i;\n      function setControlledTransition(c) {\n        c.setTransition(duration, swiper);\n        if (duration !== 0) {\n          c.transitionStart();\n          if (c.params.autoHeight) {\n            Utils.nextTick(function () {\n              c.updateAutoHeight();\n            });\n          }\n          c.$wrapperEl.transitionEnd(function () {\n            if (!controlled) { return; }\n            if (c.params.loop && swiper.params.controller.by === 'slide') {\n              c.loopFix();\n            }\n            c.transitionEnd();\n          });\n        }\n      }\n      if (Array.isArray(controlled)) {\n        for (i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTransition(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTransition(controlled);\n      }\n    },\n  };\n  var Controller$1 = {\n    name: 'controller',\n    params: {\n      controller: {\n        control: undefined,\n        inverse: false,\n        by: 'slide', // or 'container'\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        controller: {\n          control: swiper.params.controller.control,\n          getInterpolateFunction: Controller.getInterpolateFunction.bind(swiper),\n          setTranslate: Controller.setTranslate.bind(swiper),\n          setTransition: Controller.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      update: function update() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      setTranslate: function setTranslate(translate, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTranslate(translate, byController);\n      },\n      setTransition: function setTransition(duration, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTransition(duration, byController);\n      },\n    },\n  };\n\n  var a11y = {\n    makeElFocusable: function makeElFocusable($el) {\n      $el.attr('tabIndex', '0');\n      return $el;\n    },\n    addElRole: function addElRole($el, role) {\n      $el.attr('role', role);\n      return $el;\n    },\n    addElLabel: function addElLabel($el, label) {\n      $el.attr('aria-label', label);\n      return $el;\n    },\n    disableEl: function disableEl($el) {\n      $el.attr('aria-disabled', true);\n      return $el;\n    },\n    enableEl: function enableEl($el) {\n      $el.attr('aria-disabled', false);\n      return $el;\n    },\n    onEnterKey: function onEnterKey(e) {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (e.keyCode !== 13) { return; }\n      var $targetEl = $(e.target);\n      if (swiper.navigation && swiper.navigation.$nextEl && $targetEl.is(swiper.navigation.$nextEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          swiper.a11y.notify(params.lastSlideMessage);\n        } else {\n          swiper.a11y.notify(params.nextSlideMessage);\n        }\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl && $targetEl.is(swiper.navigation.$prevEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          swiper.a11y.notify(params.firstSlideMessage);\n        } else {\n          swiper.a11y.notify(params.prevSlideMessage);\n        }\n      }\n      if (swiper.pagination && $targetEl.is((\".\" + (swiper.params.pagination.bulletClass)))) {\n        $targetEl[0].click();\n      }\n    },\n    notify: function notify(message) {\n      var swiper = this;\n      var notification = swiper.a11y.liveRegion;\n      if (notification.length === 0) { return; }\n      notification.html('');\n      notification.html(message);\n    },\n    updateNavigation: function updateNavigation() {\n      var swiper = this;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          swiper.a11y.disableEl($prevEl);\n        } else {\n          swiper.a11y.enableEl($prevEl);\n        }\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          swiper.a11y.disableEl($nextEl);\n        } else {\n          swiper.a11y.enableEl($nextEl);\n        }\n      }\n    },\n    updatePagination: function updatePagination() {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.bullets.each(function (bulletIndex, bulletEl) {\n          var $bulletEl = $(bulletEl);\n          swiper.a11y.makeElFocusable($bulletEl);\n          swiper.a11y.addElRole($bulletEl, 'button');\n          swiper.a11y.addElLabel($bulletEl, params.paginationBulletMessage.replace(/{{index}}/, $bulletEl.index() + 1));\n        });\n      }\n    },\n    init: function init() {\n      var swiper = this;\n\n      swiper.$el.append(swiper.a11y.liveRegion);\n\n      // Navigation\n      var params = swiper.params.a11y;\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        swiper.a11y.makeElFocusable($nextEl);\n        swiper.a11y.addElRole($nextEl, 'button');\n        swiper.a11y.addElLabel($nextEl, params.nextSlideMessage);\n        $nextEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        swiper.a11y.makeElFocusable($prevEl);\n        swiper.a11y.addElRole($prevEl, 'button');\n        swiper.a11y.addElLabel($prevEl, params.prevSlideMessage);\n        $prevEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.on('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.a11y.liveRegion && swiper.a11y.liveRegion.length > 0) { swiper.a11y.liveRegion.remove(); }\n\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        $nextEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        $prevEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.off('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n  };\n  var A11y = {\n    name: 'a11y',\n    params: {\n      a11y: {\n        enabled: true,\n        notificationClass: 'swiper-notification',\n        prevSlideMessage: 'Previous slide',\n        nextSlideMessage: 'Next slide',\n        firstSlideMessage: 'This is the first slide',\n        lastSlideMessage: 'This is the last slide',\n        paginationBulletMessage: 'Go to slide {{index}}',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        a11y: {\n          liveRegion: $((\"<span class=\\\"\" + (swiper.params.a11y.notificationClass) + \"\\\" aria-live=\\\"assertive\\\" aria-atomic=\\\"true\\\"></span>\")),\n        },\n      });\n      Object.keys(a11y).forEach(function (methodName) {\n        swiper.a11y[methodName] = a11y[methodName].bind(swiper);\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.init();\n        swiper.a11y.updateNavigation();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      paginationUpdate: function paginationUpdate() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updatePagination();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.destroy();\n      },\n    },\n  };\n\n  var History = {\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.history) { return; }\n      if (!win.history || !win.history.pushState) {\n        swiper.params.history.enabled = false;\n        swiper.params.hashNavigation.enabled = true;\n        return;\n      }\n      var history = swiper.history;\n      history.initialized = true;\n      history.paths = History.getPathValues();\n      if (!history.paths.key && !history.paths.value) { return; }\n      history.scrollToSlide(0, history.paths.value, swiper.params.runCallbacksOnInit);\n      if (!swiper.params.history.replaceState) {\n        win.addEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (!swiper.params.history.replaceState) {\n        win.removeEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    setHistoryPopState: function setHistoryPopState() {\n      var swiper = this;\n      swiper.history.paths = History.getPathValues();\n      swiper.history.scrollToSlide(swiper.params.speed, swiper.history.paths.value, false);\n    },\n    getPathValues: function getPathValues() {\n      var pathArray = win.location.pathname.slice(1).split('/').filter(function (part) { return part !== ''; });\n      var total = pathArray.length;\n      var key = pathArray[total - 2];\n      var value = pathArray[total - 1];\n      return { key: key, value: value };\n    },\n    setHistory: function setHistory(key, index) {\n      var swiper = this;\n      if (!swiper.history.initialized || !swiper.params.history.enabled) { return; }\n      var slide = swiper.slides.eq(index);\n      var value = History.slugify(slide.attr('data-history'));\n      if (!win.location.pathname.includes(key)) {\n        value = key + \"/\" + value;\n      }\n      var currentState = win.history.state;\n      if (currentState && currentState.value === value) {\n        return;\n      }\n      if (swiper.params.history.replaceState) {\n        win.history.replaceState({ value: value }, null, value);\n      } else {\n        win.history.pushState({ value: value }, null, value);\n      }\n    },\n    slugify: function slugify(text) {\n      return text.toString().toLowerCase()\n        .replace(/\\s+/g, '-')\n        .replace(/[^\\w-]+/g, '')\n        .replace(/--+/g, '-')\n        .replace(/^-+/, '')\n        .replace(/-+$/, '');\n    },\n    scrollToSlide: function scrollToSlide(speed, value, runCallbacks) {\n      var swiper = this;\n      if (value) {\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHistory = History.slugify(slide.attr('data-history'));\n          if (slideHistory === value && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, runCallbacks);\n          }\n        }\n      } else {\n        swiper.slideTo(0, speed, runCallbacks);\n      }\n    },\n  };\n\n  var History$1 = {\n    name: 'history',\n    params: {\n      history: {\n        enabled: false,\n        replaceState: false,\n        key: 'slides',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        history: {\n          init: History.init.bind(swiper),\n          setHistory: History.setHistory.bind(swiper),\n          setHistoryPopState: History.setHistoryPopState.bind(swiper),\n          scrollToSlide: History.scrollToSlide.bind(swiper),\n          destroy: History.destroy.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.history.initialized) {\n          swiper.history.setHistory(swiper.params.history.key, swiper.activeIndex);\n        }\n      },\n    },\n  };\n\n  var HashNavigation = {\n    onHashCange: function onHashCange() {\n      var swiper = this;\n      var newHash = doc.location.hash.replace('#', '');\n      var activeSlideHash = swiper.slides.eq(swiper.activeIndex).attr('data-hash');\n      if (newHash !== activeSlideHash) {\n        var newIndex = swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-hash=\\\"\" + newHash + \"\\\"]\")).index();\n        if (typeof newIndex === 'undefined') { return; }\n        swiper.slideTo(newIndex);\n      }\n    },\n    setHash: function setHash() {\n      var swiper = this;\n      if (!swiper.hashNavigation.initialized || !swiper.params.hashNavigation.enabled) { return; }\n      if (swiper.params.hashNavigation.replaceState && win.history && win.history.replaceState) {\n        win.history.replaceState(null, null, ((\"#\" + (swiper.slides.eq(swiper.activeIndex).attr('data-hash'))) || ''));\n      } else {\n        var slide = swiper.slides.eq(swiper.activeIndex);\n        var hash = slide.attr('data-hash') || slide.attr('data-history');\n        doc.location.hash = hash || '';\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.hashNavigation.enabled || (swiper.params.history && swiper.params.history.enabled)) { return; }\n      swiper.hashNavigation.initialized = true;\n      var hash = doc.location.hash.replace('#', '');\n      if (hash) {\n        var speed = 0;\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHash = slide.attr('data-hash') || slide.attr('data-history');\n          if (slideHash === hash && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, swiper.params.runCallbacksOnInit, true);\n          }\n        }\n      }\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).on('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).off('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n  };\n  var HashNavigation$1 = {\n    name: 'hash-navigation',\n    params: {\n      hashNavigation: {\n        enabled: false,\n        replaceState: false,\n        watchState: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        hashNavigation: {\n          initialized: false,\n          init: HashNavigation.init.bind(swiper),\n          destroy: HashNavigation.destroy.bind(swiper),\n          setHash: HashNavigation.setHash.bind(swiper),\n          onHashCange: HashNavigation.onHashCange.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.hashNavigation.initialized) {\n          swiper.hashNavigation.setHash();\n        }\n      },\n    },\n  };\n\n  /* eslint no-underscore-dangle: \"off\" */\n\n  var Autoplay = {\n    run: function run() {\n      var swiper = this;\n      var $activeSlideEl = swiper.slides.eq(swiper.activeIndex);\n      var delay = swiper.params.autoplay.delay;\n      if ($activeSlideEl.attr('data-swiper-autoplay')) {\n        delay = $activeSlideEl.attr('data-swiper-autoplay') || swiper.params.autoplay.delay;\n      }\n      swiper.autoplay.timeout = Utils.nextTick(function () {\n        if (swiper.params.autoplay.reverseDirection) {\n          if (swiper.params.loop) {\n            swiper.loopFix();\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.isBeginning) {\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.params.autoplay.stopOnLastSlide) {\n            swiper.slideTo(swiper.slides.length - 1, swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else {\n            swiper.autoplay.stop();\n          }\n        } else if (swiper.params.loop) {\n          swiper.loopFix();\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.isEnd) {\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else {\n          swiper.autoplay.stop();\n        }\n      }, delay);\n    },\n    start: function start() {\n      var swiper = this;\n      if (typeof swiper.autoplay.timeout !== 'undefined') { return false; }\n      if (swiper.autoplay.running) { return false; }\n      swiper.autoplay.running = true;\n      swiper.emit('autoplayStart');\n      swiper.autoplay.run();\n      return true;\n    },\n    stop: function stop() {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return false; }\n      if (typeof swiper.autoplay.timeout === 'undefined') { return false; }\n\n      if (swiper.autoplay.timeout) {\n        clearTimeout(swiper.autoplay.timeout);\n        swiper.autoplay.timeout = undefined;\n      }\n      swiper.autoplay.running = false;\n      swiper.emit('autoplayStop');\n      return true;\n    },\n    pause: function pause(speed) {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return; }\n      if (swiper.autoplay.paused) { return; }\n      if (swiper.autoplay.timeout) { clearTimeout(swiper.autoplay.timeout); }\n      swiper.autoplay.paused = true;\n      if (speed === 0 || !swiper.params.autoplay.waitForTransition) {\n        swiper.autoplay.paused = false;\n        swiper.autoplay.run();\n      } else {\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n      }\n    },\n  };\n\n  var Autoplay$1 = {\n    name: 'autoplay',\n    params: {\n      autoplay: {\n        enabled: false,\n        delay: 3000,\n        waitForTransition: true,\n        disableOnInteraction: true,\n        stopOnLastSlide: false,\n        reverseDirection: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        autoplay: {\n          running: false,\n          paused: false,\n          run: Autoplay.run.bind(swiper),\n          start: Autoplay.start.bind(swiper),\n          stop: Autoplay.stop.bind(swiper),\n          pause: Autoplay.pause.bind(swiper),\n          onTransitionEnd: function onTransitionEnd(e) {\n            if (!swiper || swiper.destroyed || !swiper.$wrapperEl) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n            swiper.autoplay.paused = false;\n            if (!swiper.autoplay.running) {\n              swiper.autoplay.stop();\n            } else {\n              swiper.autoplay.run();\n            }\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.autoplay.enabled) {\n          swiper.autoplay.start();\n        }\n      },\n      beforeTransitionStart: function beforeTransitionStart(speed, internal) {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (internal || !swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.pause(speed);\n          } else {\n            swiper.autoplay.stop();\n          }\n        }\n      },\n      sliderFirstMove: function sliderFirstMove() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.stop();\n          } else {\n            swiper.autoplay.pause();\n          }\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          swiper.autoplay.stop();\n        }\n      },\n    },\n  };\n\n  var Fade = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = swiper.slides.eq(i);\n        var offset = $slideEl[0].swiperSlideOffset;\n        var tx = -offset;\n        if (!swiper.params.virtualTranslate) { tx -= swiper.translate; }\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n        }\n        var slideOpacity = swiper.params.fadeEffect.crossFade\n          ? Math.max(1 - Math.abs($slideEl[0].progress), 0)\n          : 1 + Math.min(Math.max($slideEl[0].progress, -1), 0);\n        $slideEl\n          .css({\n            opacity: slideOpacity,\n          })\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides.transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        slides.transitionEnd(function () {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFade = {\n    name: 'effect-fade',\n    params: {\n      fadeEffect: {\n        crossFade: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        fadeEffect: {\n          setTranslate: Fade.setTranslate.bind(swiper),\n          setTransition: Fade.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"fade\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Cube = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var rtl = swiper.rtlTranslate;\n      var swiperSize = swiper.size;\n      var params = swiper.params.cubeEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      var wrapperRotate = 0;\n      var $cubeShadowEl;\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl = $wrapperEl.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $wrapperEl.append($cubeShadowEl);\n          }\n          $cubeShadowEl.css({ height: (swiperWidth + \"px\") });\n        } else {\n          $cubeShadowEl = $el.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $el.append($cubeShadowEl);\n          }\n        }\n      }\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideIndex = i;\n        if (isVirtual) {\n          slideIndex = parseInt($slideEl.attr('data-swiper-slide-index'), 10);\n        }\n        var slideAngle = slideIndex * 90;\n        var round = Math.floor(slideAngle / 360);\n        if (rtl) {\n          slideAngle = -slideAngle;\n          round = Math.floor(-slideAngle / 360);\n        }\n        var progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        var tx = 0;\n        var ty = 0;\n        var tz = 0;\n        if (slideIndex % 4 === 0) {\n          tx = -round * 4 * swiperSize;\n          tz = 0;\n        } else if ((slideIndex - 1) % 4 === 0) {\n          tx = 0;\n          tz = -round * 4 * swiperSize;\n        } else if ((slideIndex - 2) % 4 === 0) {\n          tx = swiperSize + (round * 4 * swiperSize);\n          tz = swiperSize;\n        } else if ((slideIndex - 3) % 4 === 0) {\n          tx = -swiperSize;\n          tz = (3 * swiperSize) + (swiperSize * 4 * round);\n        }\n        if (rtl) {\n          tx = -tx;\n        }\n\n        if (!isHorizontal) {\n          ty = tx;\n          tx = 0;\n        }\n\n        var transform = \"rotateX(\" + (isHorizontal ? 0 : -slideAngle) + \"deg) rotateY(\" + (isHorizontal ? slideAngle : 0) + \"deg) translate3d(\" + tx + \"px, \" + ty + \"px, \" + tz + \"px)\";\n        if (progress <= 1 && progress > -1) {\n          wrapperRotate = (slideIndex * 90) + (progress * 90);\n          if (rtl) { wrapperRotate = (-slideIndex * 90) - (progress * 90); }\n        }\n        $slideEl.transform(transform);\n        if (params.slideShadows) {\n          // Set shadows\n          var shadowBefore = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n      }\n      $wrapperEl.css({\n        '-webkit-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-moz-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-ms-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        'transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n      });\n\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl.transform((\"translate3d(0px, \" + ((swiperWidth / 2) + params.shadowOffset) + \"px, \" + (-swiperWidth / 2) + \"px) rotateX(90deg) rotateZ(0deg) scale(\" + (params.shadowScale) + \")\"));\n        } else {\n          var shadowAngle = Math.abs(wrapperRotate) - (Math.floor(Math.abs(wrapperRotate) / 90) * 90);\n          var multiplier = 1.5 - (\n            (Math.sin((shadowAngle * 2 * Math.PI) / 360) / 2)\n            + (Math.cos((shadowAngle * 2 * Math.PI) / 360) / 2)\n          );\n          var scale1 = params.shadowScale;\n          var scale2 = params.shadowScale / multiplier;\n          var offset = params.shadowOffset;\n          $cubeShadowEl.transform((\"scale3d(\" + scale1 + \", 1, \" + scale2 + \") translate3d(0px, \" + ((swiperHeight / 2) + offset) + \"px, \" + (-swiperHeight / 2 / scale2) + \"px) rotateX(-90deg)\"));\n        }\n      }\n      var zFactor = (Browser.isSafari || Browser.isUiWebView) ? (-swiperSize / 2) : 0;\n      $wrapperEl\n        .transform((\"translate3d(0px,0,\" + zFactor + \"px) rotateX(\" + (swiper.isHorizontal() ? 0 : wrapperRotate) + \"deg) rotateY(\" + (swiper.isHorizontal() ? -wrapperRotate : 0) + \"deg)\"));\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n        $el.find('.swiper-cube-shadow').transition(duration);\n      }\n    },\n  };\n\n  var EffectCube = {\n    name: 'effect-cube',\n    params: {\n      cubeEffect: {\n        slideShadows: true,\n        shadow: true,\n        shadowOffset: 20,\n        shadowScale: 0.94,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        cubeEffect: {\n          setTranslate: Cube.setTranslate.bind(swiper),\n          setTransition: Cube.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"cube\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          resistanceRatio: 0,\n          spaceBetween: 0,\n          centeredSlides: false,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Flip = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      var rtl = swiper.rtlTranslate;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var progress = $slideEl[0].progress;\n        if (swiper.params.flipEffect.limitRotation) {\n          progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        }\n        var offset = $slideEl[0].swiperSlideOffset;\n        var rotate = -180 * progress;\n        var rotateY = rotate;\n        var rotateX = 0;\n        var tx = -offset;\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n          rotateX = -rotateY;\n          rotateY = 0;\n        } else if (rtl) {\n          rotateY = -rotateY;\n        }\n\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n\n        if (swiper.params.flipEffect.slideShadows) {\n          // Set shadows\n          var shadowBefore = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n        $slideEl\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px) rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        // eslint-disable-next-line\n        slides.eq(activeIndex).transitionEnd(function onTransitionEnd() {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          // if (!$(this).hasClass(swiper.params.slideActiveClass)) return;\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFlip = {\n    name: 'effect-flip',\n    params: {\n      flipEffect: {\n        slideShadows: true,\n        limitRotation: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        flipEffect: {\n          setTranslate: Flip.setTranslate.bind(swiper),\n          setTransition: Flip.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"flip\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Coverflow = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slidesSizesGrid = swiper.slidesSizesGrid;\n      var params = swiper.params.coverflowEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var transform = swiper.translate;\n      var center = isHorizontal ? -transform + (swiperWidth / 2) : -transform + (swiperHeight / 2);\n      var rotate = isHorizontal ? params.rotate : -params.rotate;\n      var translate = params.depth;\n      // Each slide offset from center\n      for (var i = 0, length = slides.length; i < length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideSize = slidesSizesGrid[i];\n        var slideOffset = $slideEl[0].swiperSlideOffset;\n        var offsetMultiplier = ((center - slideOffset - (slideSize / 2)) / slideSize) * params.modifier;\n\n        var rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n        var rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n        // var rotateZ = 0\n        var translateZ = -translate * Math.abs(offsetMultiplier);\n\n        var translateY = isHorizontal ? 0 : params.stretch * (offsetMultiplier);\n        var translateX = isHorizontal ? params.stretch * (offsetMultiplier) : 0;\n\n        // Fix for ultra small values\n        if (Math.abs(translateX) < 0.001) { translateX = 0; }\n        if (Math.abs(translateY) < 0.001) { translateY = 0; }\n        if (Math.abs(translateZ) < 0.001) { translateZ = 0; }\n        if (Math.abs(rotateY) < 0.001) { rotateY = 0; }\n        if (Math.abs(rotateX) < 0.001) { rotateX = 0; }\n\n        var slideTransform = \"translate3d(\" + translateX + \"px,\" + translateY + \"px,\" + translateZ + \"px)  rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\";\n\n        $slideEl.transform(slideTransform);\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n        if (params.slideShadows) {\n          // Set shadows\n          var $shadowBeforeEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var $shadowAfterEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if ($shadowBeforeEl.length === 0) {\n            $shadowBeforeEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append($shadowBeforeEl);\n          }\n          if ($shadowAfterEl.length === 0) {\n            $shadowAfterEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append($shadowAfterEl);\n          }\n          if ($shadowBeforeEl.length) { $shadowBeforeEl[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0; }\n          if ($shadowAfterEl.length) { $shadowAfterEl[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0; }\n        }\n      }\n\n      // Set correct perspective for IE10\n      if (Support.pointerEvents || Support.prefixedPointerEvents) {\n        var ws = $wrapperEl[0].style;\n        ws.perspectiveOrigin = center + \"px 50%\";\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      swiper.slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n    },\n  };\n\n  var EffectCoverflow = {\n    name: 'effect-coverflow',\n    params: {\n      coverflowEffect: {\n        rotate: 50,\n        stretch: 0,\n        depth: 100,\n        modifier: 1,\n        slideShadows: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        coverflowEffect: {\n          setTranslate: Coverflow.setTranslate.bind(swiper),\n          setTransition: Coverflow.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"coverflow\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n\n        swiper.params.watchSlidesProgress = true;\n        swiper.originalParams.watchSlidesProgress = true;\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Thumbs = {\n    init: function init() {\n      var swiper = this;\n      var ref = swiper.params;\n      var thumbsParams = ref.thumbs;\n      var SwiperClass = swiper.constructor;\n      if (thumbsParams.swiper instanceof SwiperClass) {\n        swiper.thumbs.swiper = thumbsParams.swiper;\n        Utils.extend(swiper.thumbs.swiper.originalParams, {\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        });\n        Utils.extend(swiper.thumbs.swiper.params, {\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        });\n      } else if (Utils.isObject(thumbsParams.swiper)) {\n        swiper.thumbs.swiper = new SwiperClass(Utils.extend({}, thumbsParams.swiper, {\n          watchSlidesVisibility: true,\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        }));\n        swiper.thumbs.swiperCreated = true;\n      }\n      swiper.thumbs.swiper.$el.addClass(swiper.params.thumbs.thumbsContainerClass);\n      swiper.thumbs.swiper.on('tap', swiper.thumbs.onThumbClick);\n    },\n    onThumbClick: function onThumbClick() {\n      var swiper = this;\n      var thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) { return; }\n      var clickedIndex = thumbsSwiper.clickedIndex;\n      if (typeof clickedIndex === 'undefined' || clickedIndex === null) { return; }\n      var slideToIndex;\n      if (thumbsSwiper.params.loop) {\n        slideToIndex = parseInt($(thumbsSwiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n      } else {\n        slideToIndex = clickedIndex;\n      }\n      if (swiper.params.loop) {\n        var currentIndex = swiper.activeIndex;\n        if (swiper.slides.eq(currentIndex).hasClass(swiper.params.slideDuplicateClass)) {\n          swiper.loopFix();\n          // eslint-disable-next-line\n          swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n          currentIndex = swiper.activeIndex;\n        }\n        var prevIndex = swiper.slides.eq(currentIndex).prevAll((\"[data-swiper-slide-index=\\\"\" + slideToIndex + \"\\\"]\")).eq(0).index();\n        var nextIndex = swiper.slides.eq(currentIndex).nextAll((\"[data-swiper-slide-index=\\\"\" + slideToIndex + \"\\\"]\")).eq(0).index();\n        if (typeof prevIndex === 'undefined') { slideToIndex = nextIndex; }\n        else if (typeof nextIndex === 'undefined') { slideToIndex = prevIndex; }\n        else if (nextIndex - currentIndex < currentIndex - prevIndex) { slideToIndex = nextIndex; }\n        else { slideToIndex = prevIndex; }\n      }\n      swiper.slideTo(slideToIndex);\n    },\n    update: function update(initial) {\n      var swiper = this;\n      var thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) { return; }\n\n      var slidesPerView = thumbsSwiper.params.slidesPerView === 'auto'\n        ? thumbsSwiper.slidesPerViewDynamic()\n        : thumbsSwiper.params.slidesPerView;\n\n      if (swiper.realIndex !== thumbsSwiper.realIndex) {\n        var currentThumbsIndex = thumbsSwiper.activeIndex;\n        var newThumbsIndex;\n        if (thumbsSwiper.params.loop) {\n          if (thumbsSwiper.slides.eq(currentThumbsIndex).hasClass(thumbsSwiper.params.slideDuplicateClass)) {\n            thumbsSwiper.loopFix();\n            // eslint-disable-next-line\n            thumbsSwiper._clientLeft = thumbsSwiper.$wrapperEl[0].clientLeft;\n            currentThumbsIndex = thumbsSwiper.activeIndex;\n          }\n          // Find actual thumbs index to slide to\n          var prevThumbsIndex = thumbsSwiper.slides.eq(currentThumbsIndex).prevAll((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex) + \"\\\"]\")).eq(0).index();\n          var nextThumbsIndex = thumbsSwiper.slides.eq(currentThumbsIndex).nextAll((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex) + \"\\\"]\")).eq(0).index();\n          if (typeof prevThumbsIndex === 'undefined') { newThumbsIndex = nextThumbsIndex; }\n          else if (typeof nextThumbsIndex === 'undefined') { newThumbsIndex = prevThumbsIndex; }\n          else if (nextThumbsIndex - currentThumbsIndex < currentThumbsIndex - prevThumbsIndex) { newThumbsIndex = nextThumbsIndex; }\n          else { newThumbsIndex = prevThumbsIndex; }\n        } else {\n          newThumbsIndex = swiper.realIndex;\n        }\n\n        if (thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0) {\n          if (thumbsSwiper.params.centeredSlides) {\n            if (newThumbsIndex > currentThumbsIndex) {\n              newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n            } else {\n              newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n            }\n          } else if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - slidesPerView + 1;\n          }\n          thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n        }\n      }\n\n      // Activate thumbs\n      var thumbsToActivate = 1;\n      var thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n\n      if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n        thumbsToActivate = swiper.params.slidesPerView;\n      }\n\n      thumbsSwiper.slides.removeClass(thumbActiveClass);\n      if (thumbsSwiper.params.loop) {\n        for (var i = 0; i < thumbsToActivate; i += 1) {\n          thumbsSwiper.$wrapperEl.children((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex + i) + \"\\\"]\")).addClass(thumbActiveClass);\n        }\n      } else {\n        for (var i$1 = 0; i$1 < thumbsToActivate; i$1 += 1) {\n          thumbsSwiper.slides.eq(swiper.realIndex + i$1).addClass(thumbActiveClass);\n        }\n      }\n    },\n  };\n  var Thumbs$1 = {\n    name: 'thumbs',\n    params: {\n      thumbs: {\n        swiper: null,\n        slideThumbActiveClass: 'swiper-slide-thumb-active',\n        thumbsContainerClass: 'swiper-container-thumbs',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        thumbs: {\n          swiper: null,\n          init: Thumbs.init.bind(swiper),\n          update: Thumbs.update.bind(swiper),\n          onThumbClick: Thumbs.onThumbClick.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        var ref = swiper.params;\n        var thumbs = ref.thumbs;\n        if (!thumbs || !thumbs.swiper) { return; }\n        swiper.thumbs.init();\n        swiper.thumbs.update(true);\n      },\n      slideChange: function slideChange() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      update: function update() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        var thumbsSwiper = swiper.thumbs.swiper;\n        if (!thumbsSwiper) { return; }\n        thumbsSwiper.setTransition(duration);\n      },\n      beforeDestroy: function beforeDestroy() {\n        var swiper = this;\n        var thumbsSwiper = swiper.thumbs.swiper;\n        if (!thumbsSwiper) { return; }\n        if (swiper.thumbs.swiperCreated && thumbsSwiper) {\n          thumbsSwiper.destroy();\n        }\n      },\n    },\n  };\n\n  // Swiper Class\n\n  var components = [\n    Device$1,\n    Support$1,\n    Browser$1,\n    Resize,\n    Observer$1,\n    Virtual$1,\n    Keyboard$1,\n    Mousewheel$1,\n    Navigation$1,\n    Pagination$1,\n    Scrollbar$1,\n    Parallax$1,\n    Zoom$1,\n    Lazy$1,\n    Controller$1,\n    A11y,\n    History$1,\n    HashNavigation$1,\n    Autoplay$1,\n    EffectFade,\n    EffectCube,\n    EffectFlip,\n    EffectCoverflow,\n    Thumbs$1\n  ];\n\n  if (typeof Swiper.use === 'undefined') {\n    Swiper.use = Swiper.Class.use;\n    Swiper.installModule = Swiper.Class.installModule;\n  }\n\n  Swiper.use(components);\n\n  return Swiper;\n\n})));\n"]}