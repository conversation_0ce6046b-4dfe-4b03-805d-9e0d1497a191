:root {
    --color-yellow: #edb417;
    --color-light-grey: #666;
}

.clearfix {
    clear: both;
}

section#ratings {
    background-color: var(--background-light-gray);
    padding: 1rem 0;
}

section#ratings h2 {
    color: var(--color-red);
    font-size: 44px;
    line-height: 53px;
    font-weight: 800;
    margin-bottom: 20px;
    text-align: center;
}

section#ratings h3 {
    font-size: 25px;
    line-height: 30px;
    font-weight: 700;
    color: var(--color-black);
    text-align: center;
}

span.score {
    background-color: #fff;
    border: 1px solid var(--color-red);
    border-radius: 11px;
    padding: .25rem .5rem;
    color: var(--color-red);
}

.ratings {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem 1.5rem;
    justify-content: center;
    margin: 4rem 0;
}

.rating-item {
    flex-basis: 30%;
    background-color: #fff;
    border-radius: 11px;
}

.rating-item .rating-heading {
    display: flex;
    align-items: center;
    column-gap: 1rem;
    padding: 1rem;
}

.rating-item .rating-heading .rating-score {
    color: var(--color-red);
    font-size: 2.4rem;
    padding: .1rem .75rem;
    border: 1px solid var(--color-yellow);
    font-weight: 700;
    border-radius: 6px;
}

.rating-item .rating-heading h4 {
    font-size: 1.6rem;
    margin: 0;
}

.rating-item .rating-heading .verified {
    color: var(--color-red);
}

.rating-item .rating-heading .verified .badge {
    border-radius: 50%;
    background: var(--color-yellow);
    padding: 0.1rem .3rem;
}

.rating-item .rating-date {
    float: right;
    padding: 0 0.75rem 1.2rem;
    color: var(--color-light-grey);
}

.rating-body {
    background-color: var(--color-yellow);
    border-radius: 0 0 11px 11px;
    padding: 2rem;
    position: relative;
}

.rating-body::before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f10d";
    font-size: 6rem;
    position: absolute;
    left: 1rem;
    color: #fff;
}

.rating-body .content {
    padding-left: 6rem;
}

.rating-body blockquote {
    margin-bottom: 3rem;
}

.rating-body .moreButton {
    float: right;
    margin-top: -3rem;
}

.rating-body .content .stars {
    display: flex;
    justify-content: center;
}

.rating-body .content .stars .active {
    color: var(--color-red);
}

.rating-body .content .stars .inactive {
    color: #fff;
}

.read-more {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.read-more a {
    padding: 1rem;
    background: var(--color-red);
    color: #fff;
    text-decoration: none;
    border-radius: 3rem;
    font-size: 1.2rem;
    font-weight: 700;
}

@media screen and (max-width: 768px) {
    section#ratings h3 {
        line-height: 3rem;
        padding: 0 1rem;
    }
    .rating-item {
        flex-basis: 90%;
    }
    .rating-body {
        padding: 1rem;
    }
    .rating-body::before {
        font-size: 2.3rem;
    }
    .rating-body .content {
        padding-left: 0;
    }
    .rating-body blockquote {
        margin: 1rem 0rem 2rem 3rem;
    }
}