@charset "UTF-8";

/* ==========================================================================
    SCSS Styles [VARIABLES]
   Theme Settings
   ========================================================================== */


/* ========================================================== */


/* 			            01. general                               */


/* ========================================================== */


/* ========================================================== */


/* 			            02. grid                                  */


/* ========================================================== */


/* We can have extra column sizes */


/* ========================================================== */


/* 			            03. utils                                 */


/* ========================================================== */


/* ========================================================== */


/* 			            04. breakpoints                           */


/* ========================================================== */


/* ========================================================== */


/* 			            05. css variables                         */


/* ========================================================== */

:root {
    /* html|body */
    --main-font-family: "Inter", sans-serif;
    --main-font-size: 16px;
    --main-font-color: #323335;
    --body-background: #ffffff;
    /* layout */
    --container-width: 1446px;
    --container-pad: min(20px, 20px);
    /*--> Place custom settings here <--*/
    --color-black: #231f20;
    --color-red: #CB3832;
    --color-gray: #60585A;
}


/* ==========================================================================
   Media Queries
   ========================================================================== */

@media only screen and (max-width: 500px) {
     :root {
        --container-pad: 20px;
    }
}


/* ==========================================================================
    SCSS FUNCTIONS [TYPES]

   A collection of function for advanced type checking
   ========================================================================== */


/* ==========================================================================
    SCSS FUNCTIONS [INTERNAL]
   ========================================================================== */


/**
 * Replaces substring on a string variable
 */


/**
 * Splits a string with a specific separator
 */


/* ==========================================================================
    SCSS FUNCTIONS [UNITS]
   ========================================================================== */


/**
 * Removes unit from value
 */


/**
 * Adds unit to a unitless value
 */


/**
 * Converts to rem values
 */


/**
 * Converts to vw values
 */


/* ==========================================================================
    SCSS MIXINS [VARIOUS]
   ========================================================================== */


/**
 * Set placeholder color to inputs
 * Usage:
 * element{
 *   @placeholder-color(#cccccc)
 * }
 */


/**
 * Gives size to an element
 * Usage:
 * @include box(150, 450);
 */


/**
 * Truncates the text
 * Usage:
 * @include ellipsis-text();
 */


/**
 * Support for retina background images
 */


/**
 * CSS Opacity with fallback for IE8+.
 * Usage:
 * .opacity-box { @include opacity(.4); }
 */


/**
 * Implementing font-size with rem units and pixel fallback.
 */


/**
 * Adds a cover background to an element
 * Usage:
 * div {
 *   background-image: url("banner.jpg");
 *   @include cover-background;
 * }
 */


/**
 * A quick placeholder maker (with custom size/colors/text)
 * Uses: https://placeholder.com/
 * Usage:
 * .thumb {
 *   @include placeholder-image(400, 200);
 *   width: 400px;
 *   height: 200px;
 * }
 */


/* ==========================================================================
    SCSS Styles [UTILITIES]

   A tiny utility-first CSS "framework"
   ========================================================================== */

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.float-left {
    float: left;
}

.float-right {
    float: right;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-visible {
    overflow: visible;
}

.position-static {
    position: static;
}

.position-relative {
    position: relative;
}

.position-absolute {
    position: absolute;
}

.d-block {
    display: block !important;
}

.d-inline-block {
    display: inline-block !important;
}

.d-flex {
    display: flex !important;
}

.d-inline-flex {
    display: inline-flex !important;
}

.d-none {
    display: none !important;
}

@media screen and (max-width: 1366px) {
    .text-xl-center {
        text-align: center;
    }
    .text-xl-left {
        text-align: left;
    }
    .text-xl-right {
        text-align: right;
    }
    .float-xl-left {
        float: left;
    }
    .float-xl-right {
        float: right;
    }
    .overflow-xl-hidden {
        overflow: hidden;
    }
    .overflow-xl-visible {
        overflow: visible;
    }
    .position-xl-static {
        position: static;
    }
    .position-xl-relative {
        position: relative;
    }
    .position-xl-absolute {
        position: absolute;
    }
    .d-xl-block {
        display: block !important;
    }
    .d-xl-inline-block {
        display: inline-block !important;
    }
    .d-xl-flex {
        display: flex !important;
    }
    .d-xl-inline-flex {
        display: inline-flex !important;
    }
    .d-xl-none {
        display: none !important;
    }
}

@media screen and (max-width: 1024px) {
    .text-lg-center {
        text-align: center;
    }
    .text-lg-left {
        text-align: left;
    }
    .text-lg-right {
        text-align: right;
    }
    .float-lg-left {
        float: left;
    }
    .float-lg-right {
        float: right;
    }
    .overflow-lg-hidden {
        overflow: hidden;
    }
    .overflow-lg-visible {
        overflow: visible;
    }
    .position-lg-static {
        position: static;
    }
    .position-lg-relative {
        position: relative;
    }
    .position-lg-absolute {
        position: absolute;
    }
    .d-lg-block {
        display: block !important;
    }
    .d-lg-inline-block {
        display: inline-block !important;
    }
    .d-lg-flex {
        display: flex !important;
    }
    .d-lg-inline-flex {
        display: inline-flex !important;
    }
    .d-lg-none {
        display: none !important;
    }
}

@media screen and (max-width: 768px) {
    .text-md-center {
        text-align: center;
    }
    .text-md-left {
        text-align: left;
    }
    .text-md-right {
        text-align: right;
    }
    .float-md-left {
        float: left;
    }
    .float-md-right {
        float: right;
    }
    .overflow-md-hidden {
        overflow: hidden;
    }
    .overflow-md-visible {
        overflow: visible;
    }
    .position-md-static {
        position: static;
    }
    .position-md-relative {
        position: relative;
    }
    .position-md-absolute {
        position: absolute;
    }
    .d-md-block {
        display: block !important;
    }
    .d-md-inline-block {
        display: inline-block !important;
    }
    .d-md-flex {
        display: flex !important;
    }
    .d-md-inline-flex {
        display: inline-flex !important;
    }
    .d-md-none {
        display: none !important;
    }
}

@media screen and (max-width: 500px) {
    .text-sm-center {
        text-align: center;
    }
    .text-sm-left {
        text-align: left;
    }
    .text-sm-right {
        text-align: right;
    }
    .float-sm-left {
        float: left;
    }
    .float-sm-right {
        float: right;
    }
    .overflow-sm-hidden {
        overflow: hidden;
    }
    .overflow-sm-visible {
        overflow: visible;
    }
    .position-sm-static {
        position: static;
    }
    .position-sm-relative {
        position: relative;
    }
    .position-sm-absolute {
        position: absolute;
    }
    .d-sm-block {
        display: block !important;
    }
    .d-sm-inline-block {
        display: inline-block !important;
    }
    .d-sm-flex {
        display: flex !important;
    }
    .d-sm-inline-flex {
        display: inline-flex !important;
    }
    .d-sm-none {
        display: none !important;
    }
}


/* ==========================================================================
    SCSS Styles [GRID]
   ========================================================================== */

.row {
    display: flex;
    flex-wrap: wrap;
}

.row--hcenter {
    justify-content: center;
}

.row--vcenter {
    align-items: center;
}


/**
 * A small hack so we don't have to include max-width
 * with the appropriate percentage everywhere (less file size)
 *
 * Generally it works but it seems to fail when a col is
 * alone in its row (it will expand to 100%)
 */


/**
 * Builders
 */


/**
 * Generate everything
 */

.col {
    flex: 1;
    max-width: 100%;
}

.col-auto {
    flex: auto;
    max-width: 100%;
    width: auto;
}


/* Generate all column/offset classes */

.col-1 {
    flex: 8.3333333333%;
    max-width: 8.3333333333%;
}

.off-1 {
    margin-left: 8.3333333333%;
}

.col-2 {
    flex: 16.6666666667%;
    max-width: 16.6666666667%;
}

.off-2 {
    margin-left: 16.6666666667%;
}

.col-3 {
    flex: 25%;
    max-width: 25%;
}

.off-3 {
    margin-left: 25%;
}

.col-4 {
    flex: 33.3333333333%;
    max-width: 33.3333333333%;
}

.off-4 {
    margin-left: 33.3333333333%;
}

.col-5 {
    flex: 41.6666666667%;
    max-width: 41.6666666667%;
}

.off-5 {
    margin-left: 41.6666666667%;
}

.col-6 {
    flex: 50%;
    max-width: 50%;
}

.off-6 {
    margin-left: 50%;
}

.col-7 {
    flex: 58.3333333333%;
    max-width: 58.3333333333%;
}

.off-7 {
    margin-left: 58.3333333333%;
}

.col-8 {
    flex: 66.6666666667%;
    max-width: 66.6666666667%;
}

.off-8 {
    margin-left: 66.6666666667%;
}

.col-9 {
    flex: 75%;
    max-width: 75%;
}

.off-9 {
    margin-left: 75%;
}

.col-10 {
    flex: 83.3333333333%;
    max-width: 83.3333333333%;
}

.off-10 {
    margin-left: 83.3333333333%;
}

.col-11 {
    flex: 91.6666666667%;
    max-width: 91.6666666667%;
}

.off-11 {
    margin-left: 91.6666666667%;
}

.col-12 {
    flex: 100%;
    max-width: 100%;
}

.off-12 {
    margin-left: 100%;
}


/* Generate the extra classes */

.col-20 {
    flex: 20%;
    max-width: 20%;
}


/* Generate all gap classes */

.gap-0,
.gap-0-x {
    margin-left: 0;
}

.gap-0>*,
.gap-0-x>* {
    padding-left: 0;
}

.gap-0,
.gap-0-y {
    margin-top: 0;
}

.gap-0>*,
.gap-0-y>* {
    padding-top: 0;
}

.gap-1,
.gap-1-x {
    margin-left: -12px;
}

.gap-1>*,
.gap-1-x>* {
    padding-left: 12px;
}

.gap-1,
.gap-1-y {
    margin-top: -12px;
}

.gap-1>*,
.gap-1-y>* {
    padding-top: 12px;
}

.gap-2,
.gap-2-x {
    margin-left: -24px;
}

.gap-2>*,
.gap-2-x>* {
    padding-left: 24px;
}

.gap-2,
.gap-2-y {
    margin-top: -24px;
}

.gap-2>*,
.gap-2-y>* {
    padding-top: 24px;
}

.gap-3,
.gap-3-x {
    margin-left: -45px;
}

.gap-3>*,
.gap-3-x>* {
    padding-left: 45px;
}

.gap-3,
.gap-3-y {
    margin-top: -45px;
}

.gap-3>*,
.gap-3-y>* {
    padding-top: 45px;
}


/* Generate all responsive steps */

@media screen and (max-width: 1366px) {
    .col-xl {
        flex: 1;
        max-width: 100%;
    }
    .col-xl-auto {
        flex: auto;
        max-width: 100%;
        width: auto;
    }
    /* Generate all column/offset classes */
    .col-xl-1 {
        flex: 8.3333333333%;
        max-width: 8.3333333333%;
    }
    .off-xl-1 {
        margin-left: 8.3333333333%;
    }
    .col-xl-2 {
        flex: 16.6666666667%;
        max-width: 16.6666666667%;
    }
    .off-xl-2 {
        margin-left: 16.6666666667%;
    }
    .col-xl-3 {
        flex: 25%;
        max-width: 25%;
    }
    .off-xl-3 {
        margin-left: 25%;
    }
    .col-xl-4 {
        flex: 33.3333333333%;
        max-width: 33.3333333333%;
    }
    .off-xl-4 {
        margin-left: 33.3333333333%;
    }
    .col-xl-5 {
        flex: 41.6666666667%;
        max-width: 41.6666666667%;
    }
    .off-xl-5 {
        margin-left: 41.6666666667%;
    }
    .col-xl-6 {
        flex: 50%;
        max-width: 50%;
    }
    .off-xl-6 {
        margin-left: 50%;
    }
    .col-xl-7 {
        flex: 58.3333333333%;
        max-width: 58.3333333333%;
    }
    .off-xl-7 {
        margin-left: 58.3333333333%;
    }
    .col-xl-8 {
        flex: 66.6666666667%;
        max-width: 66.6666666667%;
    }
    .off-xl-8 {
        margin-left: 66.6666666667%;
    }
    .col-xl-9 {
        flex: 75%;
        max-width: 75%;
    }
    .off-xl-9 {
        margin-left: 75%;
    }
    .col-xl-10 {
        flex: 83.3333333333%;
        max-width: 83.3333333333%;
    }
    .off-xl-10 {
        margin-left: 83.3333333333%;
    }
    .col-xl-11 {
        flex: 91.6666666667%;
        max-width: 91.6666666667%;
    }
    .off-xl-11 {
        margin-left: 91.6666666667%;
    }
    .col-xl-12 {
        flex: 100%;
        max-width: 100%;
    }
    .off-xl-12 {
        margin-left: 100%;
    }
    /* Generate the extra classes */
    .col-xl-20 {
        flex: 20%;
        max-width: 20%;
    }
    /* Generate all gap classes */
    .gap-xl-0,
    .gap-xl-0-x {
        margin-left: 0;
    }
    .gap-xl-0>*,
    .gap-xl-0-x>* {
        padding-left: 0;
    }
    .gap-xl-0,
    .gap-xl-0-y {
        margin-top: 0;
    }
    .gap-xl-0>*,
    .gap-xl-0-y>* {
        padding-top: 0;
    }
    .gap-xl-1,
    .gap-xl-1-x {
        margin-left: -12px;
    }
    .gap-xl-1>*,
    .gap-xl-1-x>* {
        padding-left: 12px;
    }
    .gap-xl-1,
    .gap-xl-1-y {
        margin-top: -12px;
    }
    .gap-xl-1>*,
    .gap-xl-1-y>* {
        padding-top: 12px;
    }
    .gap-xl-2,
    .gap-xl-2-x {
        margin-left: -24px;
    }
    .gap-xl-2>*,
    .gap-xl-2-x>* {
        padding-left: 24px;
    }
    .gap-xl-2,
    .gap-xl-2-y {
        margin-top: -24px;
    }
    .gap-xl-2>*,
    .gap-xl-2-y>* {
        padding-top: 24px;
    }
    .gap-xl-3,
    .gap-xl-3-x {
        margin-left: -45px;
    }
    .gap-xl-3>*,
    .gap-xl-3-x>* {
        padding-left: 45px;
    }
    .gap-xl-3,
    .gap-xl-3-y {
        margin-top: -45px;
    }
    .gap-xl-3>*,
    .gap-xl-3-y>* {
        padding-top: 45px;
    }
    .reset-xl {
        margin-left: 0;
    }
}

@media screen and (max-width: 1024px) {
    .col-lg {
        flex: 1;
        max-width: 100%;
    }
    .col-lg-auto {
        flex: auto;
        max-width: 100%;
        width: auto;
    }
    /* Generate all column/offset classes */
    .col-lg-1 {
        flex: 8.3333333333%;
        max-width: 8.3333333333%;
    }
    .off-lg-1 {
        margin-left: 8.3333333333%;
    }
    .col-lg-2 {
        flex: 16.6666666667%;
        max-width: 16.6666666667%;
    }
    .off-lg-2 {
        margin-left: 16.6666666667%;
    }
    .col-lg-3 {
        flex: 25%;
        max-width: 25%;
    }
    .off-lg-3 {
        margin-left: 25%;
    }
    .col-lg-4 {
        flex: 33.3333333333%;
        max-width: 33.3333333333%;
    }
    .off-lg-4 {
        margin-left: 33.3333333333%;
    }
    .col-lg-5 {
        flex: 41.6666666667%;
        max-width: 41.6666666667%;
    }
    .off-lg-5 {
        margin-left: 41.6666666667%;
    }
    .col-lg-6 {
        flex: 50%;
        max-width: 50%;
    }
    .off-lg-6 {
        margin-left: 50%;
    }
    .col-lg-7 {
        flex: 58.3333333333%;
        max-width: 58.3333333333%;
    }
    .off-lg-7 {
        margin-left: 58.3333333333%;
    }
    .col-lg-8 {
        flex: 66.6666666667%;
        max-width: 66.6666666667%;
    }
    .off-lg-8 {
        margin-left: 66.6666666667%;
    }
    .col-lg-9 {
        flex: 75%;
        max-width: 75%;
    }
    .off-lg-9 {
        margin-left: 75%;
    }
    .col-lg-10 {
        flex: 83.3333333333%;
        max-width: 83.3333333333%;
    }
    .off-lg-10 {
        margin-left: 83.3333333333%;
    }
    .col-lg-11 {
        flex: 91.6666666667%;
        max-width: 91.6666666667%;
    }
    .off-lg-11 {
        margin-left: 91.6666666667%;
    }
    .col-lg-12 {
        flex: 100%;
        max-width: 100%;
    }
    .off-lg-12 {
        margin-left: 100%;
    }
    /* Generate the extra classes */
    .col-lg-20 {
        flex: 20%;
        max-width: 20%;
    }
    /* Generate all gap classes */
    .gap-lg-0,
    .gap-lg-0-x {
        margin-left: 0;
    }
    .gap-lg-0>*,
    .gap-lg-0-x>* {
        padding-left: 0;
    }
    .gap-lg-0,
    .gap-lg-0-y {
        margin-top: 0;
    }
    .gap-lg-0>*,
    .gap-lg-0-y>* {
        padding-top: 0;
    }
    .gap-lg-1,
    .gap-lg-1-x {
        margin-left: -12px;
    }
    .gap-lg-1>*,
    .gap-lg-1-x>* {
        padding-left: 12px;
    }
    .gap-lg-1,
    .gap-lg-1-y {
        margin-top: -12px;
    }
    .gap-lg-1>*,
    .gap-lg-1-y>* {
        padding-top: 12px;
    }
    .gap-lg-2,
    .gap-lg-2-x {
        margin-left: -24px;
    }
    .gap-lg-2>*,
    .gap-lg-2-x>* {
        padding-left: 24px;
    }
    .gap-lg-2,
    .gap-lg-2-y {
        margin-top: -24px;
    }
    .gap-lg-2>*,
    .gap-lg-2-y>* {
        padding-top: 24px;
    }
    .gap-lg-3,
    .gap-lg-3-x {
        margin-left: -45px;
    }
    .gap-lg-3>*,
    .gap-lg-3-x>* {
        padding-left: 45px;
    }
    .gap-lg-3,
    .gap-lg-3-y {
        margin-top: -45px;
    }
    .gap-lg-3>*,
    .gap-lg-3-y>* {
        padding-top: 45px;
    }
    .reset-lg {
        margin-left: 0;
    }
}

@media screen and (max-width: 768px) {
    .col-md {
        flex: 1;
        max-width: 100%;
    }
    .col-md-auto {
        flex: auto;
        max-width: 100%;
        width: auto;
    }
    /* Generate all column/offset classes */
    .col-md-1 {
        flex: 8.3333333333%;
        max-width: 8.3333333333%;
    }
    .off-md-1 {
        margin-left: 8.3333333333%;
    }
    .col-md-2 {
        flex: 16.6666666667%;
        max-width: 16.6666666667%;
    }
    .off-md-2 {
        margin-left: 16.6666666667%;
    }
    .col-md-3 {
        flex: 25%;
        max-width: 25%;
    }
    .off-md-3 {
        margin-left: 25%;
    }
    .col-md-4 {
        flex: 33.3333333333%;
        max-width: 33.3333333333%;
    }
    .off-md-4 {
        margin-left: 33.3333333333%;
    }
    .col-md-5 {
        flex: 41.6666666667%;
        max-width: 41.6666666667%;
    }
    .off-md-5 {
        margin-left: 41.6666666667%;
    }
    .col-md-6 {
        flex: 50%;
        max-width: 50%;
    }
    .off-md-6 {
        margin-left: 50%;
    }
    .col-md-7 {
        flex: 58.3333333333%;
        max-width: 58.3333333333%;
    }
    .off-md-7 {
        margin-left: 58.3333333333%;
    }
    .col-md-8 {
        flex: 66.6666666667%;
        max-width: 66.6666666667%;
    }
    .off-md-8 {
        margin-left: 66.6666666667%;
    }
    .col-md-9 {
        flex: 75%;
        max-width: 75%;
    }
    .off-md-9 {
        margin-left: 75%;
    }
    .col-md-10 {
        flex: 83.3333333333%;
        max-width: 83.3333333333%;
    }
    .off-md-10 {
        margin-left: 83.3333333333%;
    }
    .col-md-11 {
        flex: 91.6666666667%;
        max-width: 91.6666666667%;
    }
    .off-md-11 {
        margin-left: 91.6666666667%;
    }
    .col-md-12 {
        flex: 100%;
        max-width: 100%;
    }
    .off-md-12 {
        margin-left: 100%;
    }
    /* Generate the extra classes */
    .col-md-20 {
        flex: 20%;
        max-width: 20%;
    }
    /* Generate all gap classes */
    .gap-md-0,
    .gap-md-0-x {
        margin-left: 0;
    }
    .gap-md-0>*,
    .gap-md-0-x>* {
        padding-left: 0;
    }
    .gap-md-0,
    .gap-md-0-y {
        margin-top: 0;
    }
    .gap-md-0>*,
    .gap-md-0-y>* {
        padding-top: 0;
    }
    .gap-md-1,
    .gap-md-1-x {
        margin-left: -12px;
    }
    .gap-md-1>*,
    .gap-md-1-x>* {
        padding-left: 12px;
    }
    .gap-md-1,
    .gap-md-1-y {
        margin-top: -12px;
    }
    .gap-md-1>*,
    .gap-md-1-y>* {
        padding-top: 12px;
    }
    .gap-md-2,
    .gap-md-2-x {
        margin-left: -24px;
    }
    .gap-md-2>*,
    .gap-md-2-x>* {
        padding-left: 24px;
    }
    .gap-md-2,
    .gap-md-2-y {
        margin-top: -24px;
    }
    .gap-md-2>*,
    .gap-md-2-y>* {
        padding-top: 24px;
    }
    .gap-md-3,
    .gap-md-3-x {
        margin-left: -45px;
    }
    .gap-md-3>*,
    .gap-md-3-x>* {
        padding-left: 45px;
    }
    .gap-md-3,
    .gap-md-3-y {
        margin-top: -45px;
    }
    .gap-md-3>*,
    .gap-md-3-y>* {
        padding-top: 45px;
    }
    .reset-md {
        margin-left: 0;
    }
}

@media screen and (max-width: 500px) {
    .col-sm {
        flex: 1;
        max-width: 100%;
    }
    .col-sm-auto {
        flex: auto;
        max-width: 100%;
        width: auto;
    }
    /* Generate all column/offset classes */
    .col-sm-1 {
        flex: 8.3333333333%;
        max-width: 8.3333333333%;
    }
    .off-sm-1 {
        margin-left: 8.3333333333%;
    }
    .col-sm-2 {
        flex: 16.6666666667%;
        max-width: 16.6666666667%;
    }
    .off-sm-2 {
        margin-left: 16.6666666667%;
    }
    .col-sm-3 {
        flex: 25%;
        max-width: 25%;
    }
    .off-sm-3 {
        margin-left: 25%;
    }
    .col-sm-4 {
        flex: 33.3333333333%;
        max-width: 33.3333333333%;
    }
    .off-sm-4 {
        margin-left: 33.3333333333%;
    }
    .col-sm-5 {
        flex: 41.6666666667%;
        max-width: 41.6666666667%;
    }
    .off-sm-5 {
        margin-left: 41.6666666667%;
    }
    .col-sm-6 {
        flex: 50%;
        max-width: 50%;
    }
    .off-sm-6 {
        margin-left: 50%;
    }
    .col-sm-7 {
        flex: 58.3333333333%;
        max-width: 58.3333333333%;
    }
    .off-sm-7 {
        margin-left: 58.3333333333%;
    }
    .col-sm-8 {
        flex: 66.6666666667%;
        max-width: 66.6666666667%;
    }
    .off-sm-8 {
        margin-left: 66.6666666667%;
    }
    .col-sm-9 {
        flex: 75%;
        max-width: 75%;
    }
    .off-sm-9 {
        margin-left: 75%;
    }
    .col-sm-10 {
        flex: 83.3333333333%;
        max-width: 83.3333333333%;
    }
    .off-sm-10 {
        margin-left: 83.3333333333%;
    }
    .col-sm-11 {
        flex: 91.6666666667%;
        max-width: 91.6666666667%;
    }
    .off-sm-11 {
        margin-left: 91.6666666667%;
    }
    .col-sm-12 {
        flex: 100%;
        max-width: 100%;
    }
    .off-sm-12 {
        margin-left: 100%;
    }
    /* Generate the extra classes */
    .col-sm-20 {
        flex: 20%;
        max-width: 20%;
    }
    /* Generate all gap classes */
    .gap-sm-0,
    .gap-sm-0-x {
        margin-left: 0;
    }
    .gap-sm-0>*,
    .gap-sm-0-x>* {
        padding-left: 0;
    }
    .gap-sm-0,
    .gap-sm-0-y {
        margin-top: 0;
    }
    .gap-sm-0>*,
    .gap-sm-0-y>* {
        padding-top: 0;
    }
    .gap-sm-1,
    .gap-sm-1-x {
        margin-left: -12px;
    }
    .gap-sm-1>*,
    .gap-sm-1-x>* {
        padding-left: 12px;
    }
    .gap-sm-1,
    .gap-sm-1-y {
        margin-top: -12px;
    }
    .gap-sm-1>*,
    .gap-sm-1-y>* {
        padding-top: 12px;
    }
    .gap-sm-2,
    .gap-sm-2-x {
        margin-left: -24px;
    }
    .gap-sm-2>*,
    .gap-sm-2-x>* {
        padding-left: 24px;
    }
    .gap-sm-2,
    .gap-sm-2-y {
        margin-top: -24px;
    }
    .gap-sm-2>*,
    .gap-sm-2-y>* {
        padding-top: 24px;
    }
    .gap-sm-3,
    .gap-sm-3-x {
        margin-left: -45px;
    }
    .gap-sm-3>*,
    .gap-sm-3-x>* {
        padding-left: 45px;
    }
    .gap-sm-3,
    .gap-sm-3-y {
        margin-top: -45px;
    }
    .gap-sm-3>*,
    .gap-sm-3-y>* {
        padding-top: 45px;
    }
    .reset-sm {
        margin-left: 0;
    }
}


/* ==========================================================================
    SCSS Styles [BASE]
   ========================================================================== */

::-moz-selection {
    background: #b3d4fc;
    text-shadow: none;
}

::selection {
    background: #b3d4fc;
    text-shadow: none;
}

hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #ccc;
    margin: 1em 0;
    padding: 0;
}

audio,
canvas,
iframe,
img,
svg,
video {
    vertical-align: middle;
}

fieldset {
    border: 0;
    margin: 0;
    padding: 0;
}

textarea {
    resize: vertical;
}

picture,
img {
    max-width: 100%;
    height: auto;
}

input,
button,
textarea,
select {
    font: inherit;
}

a {
    text-decoration: none;
    color: inherit;
}


/**
 * Helpers
 */

.clearfix::before,
.clearfix::after {
    content: " ";
    display: table;
}

.clearfix::after {
    clear: both;
}


/**
 * The Container
 */

.container {
    width: 100%;
    max-width: var(--container-width);
    padding: 0 var(--container-pad);
    margin: 0 auto;
}

.container--fluid {
    max-width: 100%;
}


/**
 * Global styles
 */

* {
    margin: 0;
    padding: 0;
}

*,
*:before,
*:after {
    box-sizing: border-box;
}

html {
    color: var(--main-font-color);
    font-family: var(--main-font-family);
    font-size: var(--main-font-size);
    font-weight: 400;
    line-height: 1.5;
}

body {
    background-color: var(--body-background);
}


/* ==========================================================================
    SCSS Styles [PARTIALS:HEADER]
   ========================================================================== */

.site-header {
    position: sticky;
    top: 0;
    z-index: 100000;
}

.site-header--top {
    background-color: #edb417;
    padding: 9px 0px 6px 0px;
}

@media only screen and (max-width: 768px) {
    .site-header--top {
        padding: 12px 0;
    }
}

.site-header--top a {
    display: flex;
    align-items: center;
}

.site-header--top a span {
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.05em;
    color: #231f20;
}

@media only screen and (max-width: 768px) {
    .site-header--top a span {
        display: none;
    }
}

.site-header--top a img {
    margin-right: 6px;
}

@media only screen and (max-width: 768px) {
    .site-header--top a img {
        margin-right: 0px;
    }
}

.site-header--top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@media only screen and (max-width: 768px) {
    .site-header--top .container {
        justify-content: flex-start;
    }
}

.site-header--top .site-header__contact {
    display: flex;
    align-items: center;
}

.site-header--top .site-header__contact a:first-of-type {
    margin-right: 42px;
}

@media only screen and (max-width: 768px) {
    .site-header--top .site-header__contact a:first-of-type {
        margin-right: 4px;
    }
}

@media only screen and (max-width: 768px) {
    .site-header--top .site-header__contact a:last-of-type {
        display: none;
    }
}

.site-header__bottom {
    padding: 23px 0 16px;
    background-color: #ffffff;
}

@media only screen and (max-width: 768px) {
    .site-header__bottom {
        padding: 14px 0 18px;
    }
}

.site-header__bottom .container>a {
    flex-basis: 23%;
}

@media only screen and (max-width: 1024px) {
    .site-header__bottom .container>a {
        flex-basis: unset;
    }
}

.site-header__bottom .site-header__bottom-right {
    flex-basis: 23%;
    justify-content: flex-end;
}

@media only screen and (max-width: 1024px) {
    .site-header__bottom .site-header__bottom-right {
        flex-basis: unset;
        justify-content: unset;
    }
}

.site-header__bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-header__bottom-right {
    display: flex;
}

@media only screen and (max-width: 768px) {
    .site-header__bottom-right .choose-language,
    .site-header__bottom-right .btn {
        display: none;
    }
}

/* @media only screen and (max-width: 1024px) {
    .main-menu {
        display: none;
    }
} */

.main-menu ul {
    list-style: none;
    display: flex;
}

.main-menu ul li:hover>a {
    color: #cb3832;
}

.main-menu ul li.current>a {
    color: #cb3832;
}

/* .main-menu ul li:not(:last-of-type) {
    margin-right: 34px;
} */

/* @media only screen and (max-width: 1366px) {
    .main-menu ul li:not(:last-of-type) {
        margin-right: 18px;
    }
} */

.main-menu ul li.has-submenu {
    position: relative;
}

.main-menu ul li.has-submenu:hover .choose-car {
    opacity: 1;
    pointer-events: all;
}

.main-menu ul li.has-submenu:hover:after {
    background-image: url(../images/icons/icon-arrow-down-red.svg);
}

.main-menu ul li.has-submenu:after {
    content: "";
    position: absolute;
    background-image: url(../images/icons/icon-arrow-down.svg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    width: 9.3px;
    height: 5.6px;
    left: 50%;
    transform: translate(-50%, 0);
    bottom: -10px;
    cursor: pointer;
}

.main-menu ul li.has-submenu:before {
    content: "";
    position: absolute;
    height: 38px;
    bottom: -35px;
    width: 100%;
    cursor: pointer;
}

.main-menu ul li.has-submenu .choose-car {
    opacity: 0;
    pointer-events: none;
    position: fixed;
    background-color: #ffffff;
    padding: 45px 0 50px;
    top: 121px;
    width: 100%;
    left: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #000000;
    z-index: 999;
}

@media only screen and (max-width: 1366px) {
    .main-menu ul li.has-submenu .choose-car {
        top: 115px;
    }
}

.main-menu ul li.has-submenu .choose-car li {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}

.main-menu ul li.has-submenu .choose-car li span {
    display: block;
    font-size: 17px;
    font-weight: 500;
    line-height: 20px;
    color: #000000;
    margin-top: 15px;
}

.main-menu ul li.has-submenu .choose-car li:hover path {
    fill: #cb3832;
}

.main-menu ul li.has-submenu .choose-car li:hover #Path_260,
.main-menu ul li.has-submenu .choose-car li:hover #Path_261,
.main-menu ul li.has-submenu .choose-car li:hover #Path_268,
.main-menu ul li.has-submenu .choose-car li:hover #Path_272,
.main-menu ul li.has-submenu .choose-car li:hover #Path_273,
.main-menu ul li.has-submenu .choose-car li:hover #Path_274 {
    fill: #fff;
}

.main-menu ul li.has-submenu .choose-car li:hover span {
    color: #cb3832;
}

.main-menu ul li.has-submenu .choose-car li:not(:first-of-type) {
    padding-left: 40px;
}

.main-menu ul li.has-submenu .choose-car li:not(:last-of-type) {
    padding-right: 40px;
    margin-right: 0;
    border-right: 1px solid #969696;
}

.main-menu ul li.has-submenu .choose-car--no-icon li span {
    margin-bottom: 15px;
}

.main-menu ul li a {
    font-size: 18px;
    line-height: 21px;
    letter-spacing: 0.005em;
    font-weight: 700;
    color: #231f20;
}

@media only screen and (max-width: 1366px) {
    .main-menu ul li a {
        font-size: 16px;
        line-height: 19px;
    }
}

.choose-language {
    display: flex;
    margin-right: 23px;
    cursor: pointer;
    position: relative;
    z-index: 10000000;
}

.choose-language:hover .language-menu {
    display: flex;
}

.choose-language::after {
    content: "";
    position: absolute;
    top: 100%;
    width: calc(100% + 20px);
    left: -10px;
    height: 16px;
}

@media only screen and (max-width: 768px) {
    .choose-language {
        margin-right: 0;
    }
}

.choose-language .language {
    display: flex;
    align-items: center;
}

.choose-language .language img {
    margin-right: 5.5px;
}

@media only screen and (max-width: 768px) {
    .choose-language .language img {
        width: 22px;
    }
}

.choose-language .language span {
    display: block;
    margin-right: 5.5px;
    font-size: 15px;
    line-height: 18px;
    font-weight: 700;
    letter-spacing: 0.005em;
}

@media only screen and (max-width: 1366px) {
    .choose-language .language span {
        font-size: 13px;
        line-height: 15px;
    }
}

.language-menu {
    position: absolute;
    width: calc(100% + 20px);
    left: -10px;
    top: calc(100% + 16px);
    background: #fff;
    display: none;
    flex-direction: column;
}

.language-menu .language {
    padding: 10px;
}

.language-menu .language:hover {
    box-shadow: inset 0 0 10px #00000017;
}

.icon-mail {
    width: 22px;
}

.icon-telephone {
    width: 22px;
}

@media only screen and (max-width: 768px) {
    .icon-telephone {
        width: 26px;
    }
}

.icon-exclamation-mark {
    width: 20px;
}

@media only screen and (max-width: 768px) {
    .icon-exclamation-mark {
        width: 26px;
    }
}

.icon-arrow-down {
    width: 9.3px;
}

.header-logo {
    width: 241.33px;
}

@media only screen and (max-width: 1366px) {
    .header-logo {
        width: 200px;
    }
}

@media only screen and (max-width: 500px) {
    .header-logo {
        width: 132px;
    }
}

.trigger-menu {
    overflow: hidden;
    display: none;
    color: #fff;
    margin: auto 0;
    margin-left: 20px;
    text-decoration: none;
    transition: all 700ms ease;
    -webkit-transition: all 700ms ease;
    -ms-transition: all 700ms ease;
    z-index: 9999999;
}

@media only screen and (max-width: 1024px) {
    .trigger-menu {
        display: block;
    }
}

.trigger-menu>div,
.trigger-menu svg {
    height: 32px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.trigger-menu .mobile-menu-close {
    display: none;
}

.trigger-menu.is_active .mobile-menu-close {
    display: block;
}

.trigger-menu.is_active .mobile-menu-open {
    display: none;
}

.trigger-menu.is_active .icon span {
    opacity: 0;
}

.trigger-menu.is_active .icon span:first-of-type {
    opacity: 1;
    transform: rotate(-45deg) translate(-3px, 5px);
}

.trigger-menu.is_active .icon span:last-of-type {
    opacity: 1;
    transform: rotate(45deg) translate(-6px, -7px);
}

.trigger-menu .icon {
    width: 29px;
    display: inline-block;
    vertical-align: middle;
}

.trigger-menu .icon span {
    display: block;
    height: 2.5px;
    background: #231f20;
    position: relative;
    border-radius: 4px;
    transition: all 700ms ease;
    -webkit-transition: all 700ms ease;
    -ms-transition: all 700ms ease;
}

.trigger-menu .icon span:not(:last-of-type) {
    margin-bottom: 5px;
}

.trigger-menu:hover,
.trigger-menu:focus {
    text-decoration: none;
}

.trigger-menu:hover .icon span:first-child {
    top: -1px;
}

.trigger-menu:hover .icon span:last-child {
    top: 1px;
}

.header-top-mobile {
    display: none;
}

.header-top-mobile .btn {
    margin-right: 7px;
}

@media only screen and (max-width: 768px) {
    .header-top-mobile {
        display: flex;
        margin-left: auto;
    }
}

.mobile-menu {
    padding-top: 80px;
    display: none;
    position: fixed;
    background-color: #ffffff;
    left: 0;
    right: 0;
    top: 37px;
    bottom: 0;
    z-index: 9;
    opacity: 0;
    pointer-events: none;
    color: #000000;
    font-size: 20px;
    line-height: 24px;
}

@media only screen and (max-width: 768px) {
    .mobile-menu {
        top: 53px;
    }
}

.mobile-menu ul {
    list-style: none;
}

.mobile-menu .container>ul>li {
    /*
  .sub-menu {
  	> li {
  		&:not(:last-of-type) {
  			margin-bottom: 13px;
  		}
  	}
  }*/
}

/* .mobile-menu .container>ul>li:not(:last-of-type) {
    margin-bottom: 13px;
} */

.mobile-menu .container>ul>li.has-submenu {
    position: relative;
    width: fit-content;
}

.mobile-menu .container>ul>li.has-submenu:hover {
    width: 100%;
}

.mobile-menu .container>ul>li.has-submenu.fleet:hover .choose-car {
    display: flex;
}

.mobile-menu .container>ul>li.has-submenu.fleet:hover a {
    color: #cb3832;
}

.mobile-menu .container>ul>li.has-submenu.fleet:hover:after {
    left: 102px;
    right: initial;
    background-image: url(../images/icons/icon-arrow-down-red.svg);
    transform: initial;
    top: 9px;
}

.mobile-menu .container>ul>li.has-submenu:after {
    content: "";
    position: absolute;
    background-image: url(../images/icons/icon-arrow-down.svg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    width: 9.3px;
    height: 5.6px;
    right: -20px;
    transform: translate(0, -50%);
    top: 50%;
}

.mobile-menu .container>ul>li a {
    color: #000000;
    font-weight: 700;
}

.mobile-menu .choose-car {
    display: none;
    /*		display: flex;*/
    flex-wrap: wrap;
    margin-top: 44px;
}

.mobile-menu .choose-car>li {
    flex-basis: 33.333%;
    max-width: 33.333%;
    display: flex;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    justify-content: space-between;
}

.mobile-menu .choose-car>li:hover path {
    fill: #cb3832;
}

.mobile-menu .choose-car>li:hover #Path_260,
.mobile-menu .choose-car>li:hover #Path_261,
.mobile-menu .choose-car>li:hover #Path_268,
.mobile-menu .choose-car>li:hover #Path_272,
.mobile-menu .choose-car>li:hover #Path_273,
.mobile-menu .choose-car>li:hover #Path_274 {
    fill: #fff;
}

.mobile-menu .choose-car>li:hover span {
    color: #cb3832;
}

.mobile-menu .choose-car>li:hover span {
    color: #cb3832;
}

.mobile-menu .choose-car>li.margin-bottom {
    margin-bottom: 37px !important;
}

.mobile-menu .choose-car>li.borders {
    border-right: 1px solid #969696;
    border-left: 1px solid #969696;
}

.mobile-menu .choose-car>li span {
    display: block;
    margin-top: 10px;
    font-size: 17px;
    font-weight: 400;
}

.mobile-menu.is_active {
    opacity: 1;
    pointer-events: all;
    transition: all 700ms ease;
    -webkit-transition: all 700ms ease;
    -ms-transition: all 700ms ease;
}

@media only screen and (max-width: 1024px) {
    .mobile-menu {
        display: block;
    }
}

.mobile-menu .locations::after {
    display: none;
}

.mobile-menu .locations ul {
    display: none;
    list-style: none;
    margin: 10px 0;
}

.mobile-menu .locations ul a {
    color: #969696 !important;
}

.mobile-menu .locations ul a:hover {
    color: var(--color-red) !important;
}

.mobile-menu .locations:hover>ul {
    display: block;
}

.mobile-menu .locations>a {
    position: relative;
}

.mobile-menu .locations>a::after {
    content: "";
    position: absolute;
    background-image: url(../images/icons/icon-arrow-down.svg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    width: 9.3px;
    height: 5.6px;
    right: -20px;
    transform: translate(0, -50%);
    top: 50%;
}


/* ==========================================================================
    SCSS Styles [PARTIALS:MAIN]
   ========================================================================== */

.container-1680 {
    max-width: 1680px;
}

.container-1291 {
    max-width: 1291px;
}

.container-1143 {
    max-width: 1143px;
}

.section-hero {
    display: flex;
    padding: 303px 0 253px;
    color: #ffffff;
    height: 666px;
    background-image: url('/images/home-hero-banner.jpg');
}

@media only screen and (max-width: 768px) {
    .section-hero-w-form {
        padding: 1rem 0 5rem;
        height: auto;
    }
}

.section-hero--black-text {
    color: var(--color-black);
}

.section-hero .hero-txt h1 {
    font-size: 54px;
    line-height: 64px;
    font-weight: 800;
    margin-bottom: 15px;
    margin-top: 0;
    width: 75%;
}

.section-hero .hero-txt p {
    width: 54%;
}

@media only screen and (max-width: 768px) {
    .section-hero .hero-txt h1 {
        font-size: 25px;
        line-height: 30px;
        margin-bottom: 6px;
    }
}

.section-hero .hero-txt p {
    font-size: 26px;
    line-height: 31px;
    font-weight: 400;
}

@media only screen and (max-width: 768px) {
    .section-hero .hero-txt p {
        font-size: 16px;
        line-height: 14px;
    }
}

@media only screen and (max-width: 768px) {
    .section-hero:not(.section-hero-w-form) {
        padding: 0;
        height: 50vh;
    }
    .section-hero:not(.section-hero-w-form)>.container {
        display: flex;
        align-items: center;
    }
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    background-color: #cb3832;
    font-size: 15px;
    line-height: 18px;
    letter-spacing: 0.025em;
    font-weight: 500;
    color: #ffffff;
    padding: 12.5px 37px;
    border: none;
    cursor: pointer;
}

@media only screen and (max-width: 1366px) {
    .btn {
        font-size: 14px;
        line-height: 17px;
        padding: 10.5px 30px;
    }
}

@media only screen and (max-width: 500px) {
    .btn {
        font-size: 10px;
        line-height: 12px;
        padding: 5px 8px;
    }
}

.btn-yellow {
    background-color: #edb417;
}

.btn-yellow:hover {
    background-color: #cb3832;
}

.background-image {
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: cover !important;
    position: relative;
}

.background-image::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0) 0%, black 100%);
    opacity: 0.6;
    z-index: -1;
}

.section-features {
    padding: 50px 0;
}

.section-features h2 {
    font-size: 54px;
    line-height: 65px;
    font-weight: 800;
    color: #edb417;
    margin-bottom: 55px;
    text-align: center;
}

@media only screen and (max-width: 768px) {
    .section-features h2 {
        font-size: 35px;
        line-height: 42px;
        margin-bottom: 30px;
    }
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 45px;
}

@media only screen and (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media only screen and (max-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

.feature-box {
    background-color: #ededee;
    text-align: center;
    height: 420px;
    padding: 0 35px 15px;
}

@media only screen and (max-width: 768px) {
    .feature-box {
        min-height: 300px;
        height: unset;
        padding: 0 25px 25px;
    }
}

.feature-box__image-wrapper {
    margin-top: 100px;
    height: 70px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media only screen and (max-width: 768px) {
    .feature-box__image-wrapper {
        margin-top: 30px;
    }
}

.feature-box__image {
    display: block;
}

.feature-box__title {
    font-size: 21px;
    line-height: 25px;
    font-weight: bold;
    color: var(--color-red);
    margin-bottom: 12.5px;
}

.feature-box__description {
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    color: var(--color-gray);
}

.section-introduction {
    padding: 50px 0;
}

.section-introduction h2 {
    font-size: 54px;
    line-height: 65px;
    font-weight: 800;
    color: #edb417;
    margin-bottom: 55px;
    text-align: center;
}

@media only screen and (max-width: 768px) {
    .section-introduction h2 {
        font-size: 35px;
        line-height: 42px;
    }
}

.content-with-image {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 60px 0;
}

@media only screen and (max-width: 768px) {
    .content-with-image {
        padding: 30px 0;
    }
}

@media only screen and (max-width: 768px) {
    .content-with-image {
        flex-direction: column;
        gap: 30px;
    }
}

.content-with-image:first-of-type {
    padding-top: 0;
}

.content-with-image__image-wrapper {
    flex-basis: 49%;
}

@media only screen and (max-width: 768px) {
    .content-with-image__image-wrapper {
        order: 0;
        flex-basis: unset;
        width: 100%;
    }
}

.content-with-image__text-wrapper {
    flex-basis: 40%;
}

@media only screen and (max-width: 768px) {
    .content-with-image__text-wrapper {
        order: 1;
        flex-basis: unset;
    }
}

.content-with-image h3 {
    font-size: 30px;
    line-height: 36px;
    font-weight: 800;
    color: var(--color-red);
    margin-bottom: 5px;
}

@media only screen and (max-width: 768px) {
    .content-with-image h3 {
        font-size: 25px;
    }
}

.content-with-image h4 {
    font-size: 16px;
    line-height: 22px;
    font-weight: 700;
    color: var(--color-gray);
}

.content-with-image p {
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    color: var(--color-gray);
}

.content-with-image img {
    width: 100%;
    object-fit: cover;
    object-position: center;
}

.content-with-image .read-more {
    font-size: 16px;
    line-height: 19px;
    font-weight: 500;
    text-decoration: none;
    color: var(--color-black);
    display: block;
    margin-top: 35px;
}

.content-with-image--home:first-of-type {
    padding-top: 60px;
}

.content-with-image--home .content-with-image__image-wrapper {
    position: relative;
}

.content-with-image--home .content-with-image__image-wrapper img:nth-of-type(1) {
    position: absolute;
    bottom: -60px;
    right: 0;
    width: 70%;
}

@media only screen and (max-width: 500px) {
    .content-with-image--home .content-with-image__image-wrapper img:nth-of-type(1) {
        position: absolute;
        top: -60px;
        right: 0;
        width: 90%;
        bottom: unset;
    }
}

.content-with-image--home .content-with-image__image-wrapper img:nth-of-type(2) {
    position: absolute;
    left: 0;
    top: -60px;
    width: 60%;
}

@media only screen and (max-width: 500px) {
    .content-with-image--home .content-with-image__image-wrapper img:nth-of-type(2) {
        position: absolute;
        left: 0;
        bottom: 15px;
        width: 60%;
        top: unset;
    }
}

@media only screen and (max-width: 768px) {
    .content-with-image--home .content-with-image__image-wrapper {
        margin: 25% 0;
    }
}

@media only screen and (max-width: 500px) {
    .content-with-image--home .content-with-image__image-wrapper {
        margin: 0;
        height: 200px;
    }
}

.content-with-image--home h2 {
    text-align: left;
    margin-bottom: 20px;
}

.read-more {
    display: block;
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    color: #231f20;
    padding-right: 10px;
    margin-top: 15px;
}

.read-more img {
    width: 9.3px;
}

.section-testimonials {
    padding: 90px 0 30px;
}

@media only screen and (max-width: 768px) {
    .section-testimonials {
        padding: 50px 0 30px;
    }
}

.section-testimonials h2 {
    font-size: 34px;
    line-height: 41px;
    font-weight: 800;
    color: #edb417;
    margin-bottom: 20px;
    text-align: center;
}

@media only screen and (max-width: 768px) {
    .section-testimonials h2 {
        font-size: 26px;
        line-height: 31px;
    }
}

.testimonial-slide {
    text-align: center;
}

.testimonial-slide__text {
    font-size: 24px;
    line-height: 33px;
    font-weight: bold;
    color: var(--color-black);
    max-width: 800px;
    margin: 0 auto 20px;
}

@media only screen and (max-width: 768px) {
    .testimonial-slide__text {
        font-size: 14px;
        line-height: 23px;
    }
}

.testimonial-slide__client {
    font-size: 20px;
    line-height: 24px;
    font-weight: bold;
    color: var(--color-red);
    margin-bottom: 5px;
}

@media only screen and (max-width: 768px) {
    .testimonial-slide__client {
        font-size: 15px;
        line-height: 18px;
    }
}

.testimonial-slide__location {
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    color: #000;
    margin-bottom: 65px;
}

@media only screen and (max-width: 768px) {
    .testimonial-slide__location {
        font-size: 12px;
        line-height: 18px;
    }
}

.slick-dots {
    display: flex;
    justify-content: center;
    margin: 0;
    padding: 1rem 0;
    list-style-type: none;
}

.slick-dots li {
    margin: 0 0.25rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slick-dots li button {
    display: block;
    width: 10px;
    height: 10px;
    padding: 0;
    border: none;
    border-radius: 100%;
    background-color: #989a9d;
    text-indent: -9999px;
}

.slick-dots li.slick-active button {
    background-color: #f7b710;
    width: 23px;
    height: 23px;
    border: 3px solid #fff;
    position: relative;
}

.slick-dots li.slick-active button::after {
    content: "";
    position: absolute;
    width: 27px;
    height: 27px;
    border: 1px solid #f7b710;
    left: -5px;
    top: -5px;
    border-radius: 50%;
}

.section-text {
    padding: 120px 0 70px;
}

@media only screen and (max-width: 768px) {
    .section-text {
        padding: 20px 0;
    }
}

.text-area h1 {
    font-size: 44px;
    line-height: 53px;
    font-weight: 800;
    color: var(--color-red);
    padding-bottom: 30px;
    border-bottom: 1px solid #8c8e90;
    margin-bottom: 20px;
    margin-top: 0;
}

@media only screen and (max-width: 768px) {
    .text-area h1 {
        font-size: 34px;
        padding-bottom: 15px;
        margin-bottom: 10px;
    }
}

.text-area h2 {
    font-size: 17px;
    line-height: 19px;
    font-weight: bold;
    margin-bottom: 5px;
}

.text-area p {
    font-size: 15px;
    line-height: 19px;
    font-weight: 400;
    color: var(--color-black);
    margin-bottom: 20px;
}

.text-area a {
    text-decoration: underline;
}

.text-area ul {
    margin-bottom: 25px;
    list-style: none;
}

.text-area li {
    padding-left: 15px;
    position: relative;
}

.text-area li::before {
    position: absolute;
    content: "•";
    color: var(--color-red);
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

.section-contact-form {
    padding: 100px 0 50px;
}

@media only screen and (max-width: 1024px) {
    .section-contact-form {
        padding: 50px 0;
    }
}

.contact-form-flexbox-wrapper {
    display: flex;
    gap: 45px;
    justify-content: space-between;
}

@media only screen and (max-width: 1366px) {
    .contact-form-flexbox-wrapper {
        gap: 25px;
    }
}

@media only screen and (max-width: 1024px) {
    .contact-form-flexbox-wrapper {
        flex-direction: column;
    }
}

.contact-form-flexbox-wrapper>div {
    flex: 1;
}

.contact-form-flexbox {
    background-color: #ededee;
    padding: 80px 120px 45px;
}

@media only screen and (max-width: 1366px) {
    .contact-form-flexbox {
        padding: 60px 40px 45px;
    }
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox {
        padding: 45px 25px 35px;
        margin: 0 -20px;
    }
}

.contact-form-flexbox h3 {
    color: #edb417;
    font-size: 36px;
    line-height: 43px;
    font-weight: 800;
    margin-bottom: 20px;
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox h3 {
        font-size: 26px;
        line-height: 31px;
    }
}

.contact-form-flexbox--right h3 {
    padding-left: 24px;
}

.contact-form-flexbox p {
    font-size: 18px;
    line-height: 32px;
    font-weight: bold;
    color: var(--color-black);
    margin-bottom: 20px;
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox p {
        font-size: 15px;
        line-height: 20px;
    }
}

.contact-form-flexbox .address {
    font-size: 18px;
    line-height: 32px;
    font-weight: 500;
    color: var(--color-black);
    margin-bottom: 20px;
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox .address {
        font-size: 15px;
        line-height: 20px;
    }
}

.contact-form-flexbox .address span {
    color: var(--color-red);
    display: block;
}

.contact-form-flexbox .phone-number,
.contact-form-flexbox .email-address {
    font-size: 24px;
    line-height: 32px;
    font-weight: bold;
    color: var(--color-black);
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox .phone-number,
    .contact-form-flexbox .email-address {
        font-size: 16px;
        line-height: 20px;
    }
}

.contact-form-flexbox .phone-number span,
.contact-form-flexbox .email-address span {
    color: var(--color-red);
    font-size: 22px;
    line-height: 32px;
    font-weight: 600;
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox .phone-number span,
    .contact-form-flexbox .email-address span {
        font-size: 15px;
        line-height: 20px;
    }
}

.contact-form .input-wrapper {
    margin-bottom: 12px;
}

.contact-form .input-label {
    text-transform: uppercase;
    font-size: 13px;
    line-height: 15px;
    font-weight: bold;
    color: var(--color-black);
    padding-bottom: 5px;
}

@media only screen and (max-width: 768px) {
    .contact-form .input-label {
        font-size: 10px;
        line-height: 12px;
    }
}

@media only screen and (max-width: 768px) {
    .contact-form input {
        font-size: 10px;
        line-height: 12px;
    }
}

.contact-form textarea {
    border-radius: 34px;
    border: none;
    height: 95px;
    letter-spacing: 0.005em;
    padding: 10px 24px;
    width: 100%;
    resize: none;
}

.contact-form textarea:focus {
    outline: none;
}

.contact-form button {
    background-color: var(--color-red);
    color: #fff;
    text-transform: uppercase;
    font-size: 16px;
    line-height: 19px;
    font-weight: 500;
    padding: 0 26.5px;
    border: none;
    outline: none;
    border-radius: 19px;
}

@media only screen and (max-width: 768px) {
    .contact-form button {
        padding: 0 18px;
        font-size: 13px;
        line-height: 15px;
    }
}


/* The container */

.custom-checkbox-container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 10px;
    line-height: 1;
    font-weight: 400;
    color: #a8aaac;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin-left: 24px;
}

.custom-checkbox-container a {
    color: #000;
}


/* Hide the browser's default checkbox */

.custom-checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    left: 0;
}


/* Create a custom checkbox */

.custom-checkbox {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    height: 22px;
    width: 22px;
    background-color: #a8aaac;
    border-radius: 50%;
}


/* When the checkbox is checked, add a blue background */

.custom-checkbox-container input:checked~.custom-checkbox {
    background-color: #edb417;
}


/* Create the custom-checkbox/indicator (hidden when not checked) */

.custom-checkbox:after {
    content: "";
    position: absolute;
    display: none;
}


/* Show the custom-checkbox when checked */

.custom-checkbox-container input:checked~.custom-checkbox:after {
    display: block;
}


/* Style the custom-checkbox/indicator */

.custom-checkbox-container .custom-checkbox:after {
    left: 9px;
    top: 5px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

.flex-input-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 40px;
}

@media only screen and (max-width: 768px) {
    .flex-input-wrapper img {
        width: 196px;
    }
}

.breadcrumbs {
    display: flex;
    align-items: center;
    margin: 15px;
    font-size: 11px;
    font-weight: 400;
    color: #a7a9ac;
    gap: 5px;
}

.breadcrumbs img {
    vertical-align: text-top;
}


/* The container */

.custom-radiobox-container {
    display: block;
    position: relative;
    padding-left: 70px;
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    font-weight: 400;
    color: var(--color-gray);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

@media only screen and (max-width: 768px) {
    .custom-radiobox-container {
        font-size: 16px;
        padding-left: 55px;
    }
}


/* Hide the browser's default radiobox */

.custom-radiobox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    left: 0;
}


/* Create a custom radiobox */

.custom-radiobox {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    height: 50px;
    width: 50px;
    background-color: #fff;
    border-radius: 50%;
    border: 1px solid #000;
}

@media only screen and (max-width: 768px) {
    .custom-radiobox {
        height: 35px;
        width: 35px;
    }
}


/* When the radiobox is checked, add a blue background */

.custom-radiobox-container input:checked~.custom-radiobox {
    background-color: #edb417;
}


/* Create the custom-radiobox/indicator (hidden when not checked) */

.custom-radiobox:after {
    content: "";
    position: absolute;
    display: none;
}


/* Show the custom-radiobox when checked */

.custom-radiobox-container input:checked~.custom-radiobox:after {
    display: block;
}


/* Style the custom-radiobox/indicator */

.custom-radiobox-container .custom-radiobox:after {
    left: 0;
    top: 0;
    width: 48px;
    height: 48px;
    border: solid white 10px;
    border-radius: 50%;
}

@media only screen and (max-width: 768px) {
    .custom-radiobox-container .custom-radiobox:after {
        height: 33px;
        width: 33px;
        border-width: 5px;
    }
}

.read-more-content {
    margin-top: 20px;
    display: none;
}


/* ==========================================================================
    SCSS Styles [PARTIALS:ELEMENTS]
   ========================================================================== */

.btn {
    display: inline-block;
}


/* Winona */

.button--winona {
    overflow: hidden;
    padding: 0;
    -webkit-transition: border-color 0.3s, background-color 0.3s;
    transition: border-color 0.3s, background-color 0.3s;
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    position: relative;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    -moz-osx-font-smoothing: grayscale;
}

.button--winona::after {
    content: attr(data-text);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    color: #fff;
    -webkit-transform: translate3d(0, 25%, 0);
    transform: translate3d(0, 25%, 0);
}

.button--winona>span {
    display: block;
}

.button--winona::after,
.button--winona>span {
    padding: 1em 2em;
    -webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
    transition: transform 0.3s, opacity 0.3s;
    -webkit-transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
    transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}

.button--winona:hover {
    background-color: #ac2f2a;
}

.button--winona:hover::after {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.button--winona:hover>span {
    opacity: 0;
    -webkit-transform: translate3d(0, -25%, 0);
    transform: translate3d(0, -25%, 0);
}


/* ==========================================================================
    SCSS Styles [PARTIALS:Rental In Chania]
   ========================================================================== */


/* ==========================================================================
    SCSS Styles [PARTIALS:Search Form]
   ========================================================================== */

.section-search-form.margin-top {
    margin-top: -206px;
    position: relative;
}

@media only screen and (max-width: 768px) {
    .section-search-form.margin-top {
        margin-top: 0;
        position: relative;
    }
}

.section-search-form--car {
    background-color: #edb417;
}

@media only screen and (max-width: 768px) {
    .section-search-form--car .search-form {
        position: static;
        padding: 25px 0;
        margin: 0px 20px;
        width: calc(100% - 40px);
    }
}

.search-form {
    background-color: #edb417;
    padding: 33px 30px 37px 30px;
    width: 100%;
    max-width: 1251px;
    margin: 0 auto;
    display: flex;
}

@media only screen and (max-width: 1230px) {
    .search-form {
        flex-direction: column;
        margin: 0px 20px;
        width: calc(100% - 40px);
    }
}

@media only screen and (max-width: 768px) {
    .search-form {
        padding: 25px 15px;
        position: relative;
        margin-top: -3.5rem;
    }
}

.search-form--left {
    flex-basis: calc(100% - 218px);
    max-width: calc(100% - 218px);
}

@media only screen and (max-width: 1230px) {
    .search-form--left {
        flex-basis: 100%;
        max-width: 100%;
    }
}

.search-form--right {
    flex-basis: 218px;
    max-width: 218px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

@media only screen and (max-width: 1230px) {
    .search-form--right {
        flex-basis: 100%;
        max-width: 100%;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
    }
}

@media only screen and (max-width: 768px) {
    .search-form--right {
        justify-content: space-between;
    }
}

.search-form--right ul {
    margin-top: 26px;
    margin-left: 10px;
}

@media only screen and (max-width: 768px) {
    .search-form--right ul {
        flex-basis: calc(50% - 8px);
        max-width: calc(50% - 8px);
        margin-right: 8px;
    }
}

@media only screen and (max-width: 1230px) {
    .search-form--right ul {
        margin-top: 0px;
        margin-left: 0px;
    }
}

/* .search-form--right ul li:not(:last-of-type) {
    margin-bottom: 9px;
} */

.search-form .row:first-of-type {
    margin-bottom: 27px;
}

@media only screen and (max-width: 768px) {
    .search-form .row:first-of-type {
        margin-bottom: 15px;
    }
}

@media only screen and (max-width: 1230px) {
    .search-form .row {
        justify-content: space-between;
    }
}

.search-form .col--1 {
    flex-basis: 345px;
    max-width: 345px;
    margin-right: 24px;
}

@media only screen and (max-width: 1230px) {
    .search-form .col--1 {
        margin-right: 10px;
    }
}

@media only screen and (max-width: 1024px) {
    .search-form .col--1 {
        flex-basis: 40%;
        max-width: 40%;
    }
}

@media only screen and (max-width: 768px) {
    .search-form .col--1 {
        flex-basis: 100%;
        max-width: 100%;
        margin-bottom: 15px;
        margin-right: 0;
    }
}

.search-form .col--2 {
    flex-basis: 245px;
    max-width: 245px;
    margin-right: 37px;
}

@media only screen and (max-width: 1230px) {
    .search-form .col--2 {
        margin-right: 10px;
    }
}

@media only screen and (max-width: 1024px) {
    .search-form .col--2 {
        flex-basis: 25%;
        max-width: 25%;
    }
}

@media only screen and (max-width: 768px) {
    .search-form .col--2 {
        flex-basis: calc(50% - 8px);
        max-width: calc(50% - 8px);
        margin-right: 8px;
    }
}

.search-form .col--3 {
    flex-basis: 245px;
    max-width: 245px;
    margin-right: 37px;
}

@media only screen and (max-width: 1230px) {
    .search-form .col--3 {
        margin-right: 0px;
    }
}

@media only screen and (max-width: 1024px) {
    .search-form .col--3 {
        flex-basis: 25%;
        max-width: 25%;
    }
}

@media only screen and (max-width: 768px) {
    .search-form .col--3 {
        flex-basis: calc(50% - 8px);
        max-width: calc(50% - 8px);
    }
}

.input-label {
    display: block;
    font-size: 19px;
    font-weight: 700;
    line-height: 22px;
    letter-spacing: 0.005em;
    color: #231f20;
    padding-bottom: 13px;
    padding-left: 24px;
}

@media only screen and (max-width: 1024px) {
    .input-label {
        font-size: 16px;
        line-height: 19px;
    }
}

.input-wrapper .select2-selection__arrow {
    top: 50% !important;
    transform: translateY(-50%);
    right: 15px !important;
}

.input-wrapper .select2-container {
    width: 100% !important;
}

.input-wrapper .select2-selection,
.input-wrapper select,
.input-wrapper input {
    border-radius: 26px;
    border: none;
    height: 51px;
    font-size: 15px;
    letter-spacing: 0.005em;
    padding: 0 15px 0 24px;
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
}

@media only screen and (max-width: 1024px) {
    .input-wrapper .select2-selection,
    .input-wrapper select,
    .input-wrapper input {
        height: 43px;
        font-size: 12px;
        line-height: 43px;
    }
}

@media only screen and (max-width: 768px) {
    .input-wrapper .select2-selection,
    .input-wrapper select,
    .input-wrapper input {
        padding: 0 15px 0 19px;
    }
}

.input-wrapper .select2-selection:focus,
.input-wrapper select:focus,
.input-wrapper input:focus {
    outline: none;
}

.tick-list {
    list-style: none;
}

.tick-list li {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    position: relative;
}

@media only screen and (max-width: 768px) {
    .tick-list li {
        font-size: 9px;
    }
}

.tick-list li:before {
    content: "";
    background-image: url(../images/icons/icon-tick-red.svg);
    width: 20px;
    height: 15px;
    display: inline-block;
    margin-right: 8px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

@media only screen and (max-width: 768px) {
    .tick-list li:before {
        width: 12px;
        height: 9.5px;
        margin-right: 5px;
    }
}

.search-form__submit {
    font-size: 20px;
    font-weight: 500;
    height: 62px;
    padding: 0;
    border-radius: 31px;
}

.search-form__submit::after,
.search-form__submit span {
    padding: 1em;
}

@media only screen and (max-width: 1230px) {
    .search-form__submit {
        margin-left: 20px;
        width: 145px;
        font-size: 13px;
        height: 47px;
    }
}

@media only screen and (max-width: 768px) {
    .search-form__submit {
        flex-basis: calc(50% - 8px);
        max-width: calc(50% - 8px);
        width: 100%;
    }
}

.input-location {
    position: relative;
}

.input-location:after {
    content: "";
    position: absolute;
    background-image: url(../images/icons/icon-pin.svg);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 19px;
    height: 27px;
    right: 17px;
    bottom: 13px;
}

@media only screen and (max-width: 1024px) {
    .input-location:after {
        width: 15px;
        height: 22px;
    }
}

.input-calendar {
    position: relative;
}

.input-calendar:after {
    content: "";
    position: absolute;
    background-image: url(../images/icons/icon-calendar.svg);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 24px;
    height: 24px;
    right: 17px;
    bottom: 14px;
    pointer-events: none;
}

@media only screen and (max-width: 1024px) {
    .input-calendar:after {
        width: 20px;
        height: 20px;
    }
}

.input-time {
    position: relative;
}

.input-time:after {
    content: "";
    position: absolute;
    background-image: url(../images/icons/icon-clock.svg);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 23px;
    height: 23px;
    right: 17px;
    bottom: 13px;
    pointer-events: none;
}

@media only screen and (max-width: 1024px) {
    .input-time:after {
        width: 19px;
        height: 19px;
    }
}

input[type=date]::-webkit-inner-spin-button,
input[type=date]::-webkit-calendar-picker-indicator {
    opacity: 0;
}

input[type=time]::-webkit-calendar-picker-indicator {
    opacity: 0;
}


/* ==========================================================================
    SCSS Styles [PARTIALS:FAQ]
   ========================================================================== */

.section-faq {
    padding: 70px 0;
}

.section-faq h2 {
    font-size: 44px;
    line-height: 53px;
    font-weight: 800;
    color: var(--color-red);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@media only screen and (max-width: 768px) {
    .section-faq h2 {
        font-size: 34px;
        line-height: 41px;
    }
}

.section-faq a.more-faq {
    font-size: 16px;
    line-height: 19px;
    font-weight: 500;
    color: var(--color-black);
    border-radius: 20px;
    padding: 10px 33px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
}

.section-faq a.more-faq svg {
    transition: all 0.3s;
}

.section-faq a.more-faq::before {
    content: "";
    position: absolute;
    width: calc(100% - 2px);
    z-index: -2;
    height: calc(100% - 0px);
    left: 1px;
    top: 0;
    border: 2px solid var(--color-red);
    border-radius: 18px;
}

.section-faq a.more-faq::after {
    width: calc(100% - 3px);
    height: 0;
    top: 1px;
    left: 2px;
    background: var(--color-red);
    content: "";
    position: absolute;
    z-index: -1;
    transition: all 0.3s;
    border-radius: 17px;
    border: 0;
}

.section-faq a.more-faq:hover {
    color: #fff;
}

.section-faq a.more-faq:hover svg {
    fill: #fff;
}

.section-faq a.more-faq:hover::after {
    height: 100%;
    border: 2px solid var(--color-red);
}

@media only screen and (max-width: 768px) {
    .section-faq a.more-faq {
        max-width: 162px;
        margin-top: 30px;
        text-align: center;
        padding: 10px 0;
    }
}

.faq-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.faq-list-item {
    border-top: 1px solid #8c8e90;
    padding: 20px 0;
}

.faq-list-item:last-child {
    border-bottom: 1px solid #8c8e90;
}

.faq-list-item .question {
    font-size: 25px;
    line-height: 30px;
    font-weight: bold;
    color: var(--color-black);
    padding-right: 10px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
}

.faq-list-item .question span {
    margin-right: 15px;
}

@media only screen and (max-width: 768px) {
    .faq-list-item .question {
        font-size: 18px;
        line-height: 21px;
    }
}

.faq-list-item .question img {
    transition: 300ms all;
}

@media only screen and (max-width: 768px) {
    .faq-list-item .question img {
        width: 21px;
        height: 21px;
    }
}

.faq-list-item .answer {
    font-size: 15px;
    line-height: 25px;
    font-weight: 400;
    color: var(--color-gray);
    margin-top: 20px;
    max-width: calc(1100px + 100px);
    padding-right: 100px;
    display: none;
}

@media only screen and (max-width: 768px) {
    .faq-list-item .answer {
        font-size: 13px;
        line-height: 20px;
        padding-right: 40px;
        max-width: 100%;
    }
}

.faq-list-item.open .question img {
    transform: rotate(180deg);
}


/* ==========================================================================
    SCSS Styles [PARTIALS:MAP]
   ========================================================================== */

.section-map {
    height: 575px;
    background-color: #f7b710;
    display: flex;
}

@media only screen and (max-width: 1024px) {
    .section-map {
        flex-direction: column;
        height: auto;
        justify-content: space-between;
    }
}

.map-text-wrapper {
    flex-basis: 40%;
    padding-top: 60px;
}

@media only screen and (max-width: 1024px) {
    .map-text-wrapper {
        padding-top: 0;
        flex-basis: unset;
    }
}

.map-text-wrapper h2 {
    font-size: 44px;
    line-height: 53px;
    font-weight: 800;
    color: var(--color-red);
    margin-bottom: 55px;
}

@media only screen and (max-width: 1024px) {
    .map-text-wrapper h2 {
        text-align: center;
        margin-bottom: 25px;
    }
}

@media only screen and (max-width: 768px) {
    .map-text-wrapper h2 {
        font-size: 33px;
        line-height: 40px;
        text-align: left;
        margin-bottom: 15px;
    }
}

.map-text-inner-wrapper {
    max-width: 290px;
    margin-right: 90px;
    margin-left: auto;
}

@media only screen and (max-width: 1024px) {
    .map-text-inner-wrapper {
        max-width: 80%;
        margin: 40px auto;
    }
}

.map-wrapper {
    flex-basis: 60%;
    position: relative;
    text-align: right;
    height: 100%;
}

@media only screen and (max-width: 1024px) {
    .map-wrapper {
        flex-basis: unset;
        height: unset;
    }
}

.map-wrapper #gmap_canvas {
    overflow: hidden;
    background: none !important;
    height: 575px;
    width: 100%;
}

@media only screen and (max-width: 1024px) {
    .map-wrapper #gmap_canvas {
        height: 480px;
    }
}

.map-wrapper #gmap_canvas div {
    width: 100%;
}

@media only screen and (max-width: 1024px) {
    .map-wrapper #gmap_canvas div {
        height: 480px;
    }
}

.locations-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.locations-list-item {
    padding-bottom: 15px;
    border-bottom: 1px solid #fff;
    margin-bottom: 15px;
}

.locations-list-item h3 {
    font-size: 25px;
    line-height: 30px;
    color: var(--color-black);
    padding-right: 10px;
    cursor: pointer;
}

.locations-list-item h3 img {
    float: right;
    transition: 300ms all;
}

.locations-list-item ul {
    list-style: none;
    margin: 0;
    padding: 0;
    padding-left: 10px;
    display: none;
}

.locations-list-item ul li {
    font-size: 18px;
    line-height: 30px;
    color: var(--color-black);
}

.locations-list-item ul li.active {
    color: var(--color-red);
}

.locations-list-item.open h3 img {
    transform: rotate(180deg);
}

.locations-list-item.open ul {
    display: block;
    margin-top: 20px;
}

.section-map--contact {
    height: 640px;
}

@media only screen and (max-width: 1366px) {
    .section-map--contact {
        flex-direction: column;
        height: 850px;
        justify-content: space-between;
    }
}

.section-map--contact #gmap_canvas,
.section-map--contact iframe {
    height: 640px;
}

@media only screen and (max-width: 1366px) {
    .section-map--contact #gmap_canvas,
    .section-map--contact iframe {
        height: 480px;
    }
}

.section-map--contact .map-text-wrapper {
    flex-basis: 25%;
}

@media only screen and (max-width: 1366px) {
    .section-map--contact .map-text-wrapper {
        padding-top: 0;
        flex-basis: unset;
    }
}

@media only screen and (max-width: 1366px) {
    .section-map--contact .map-text-wrapper h2 {
        text-align: center;
        margin-bottom: 25px;
    }
}

.section-map--contact .map-wrapper {
    flex-basis: 75%;
}

@media only screen and (max-width: 1366px) {
    .section-map--contact .map-wrapper {
        flex-basis: unset;
        height: unset;
    }
}

.section-map--contact .map-text-inner-wrapper {
    margin-left: auto;
    margin-right: auto;
}

@media only screen and (max-width: 1366px) {
    .section-map--contact .map-text-inner-wrapper {
        max-width: 80%;
        margin: 40px auto;
    }
}

.locations-contact-wrapper {
    margin-top: 44px;
}

@media only screen and (max-width: 768px) {
    .locations-contact-wrapper {
        margin-top: 25px;
    }
}

.locations-contact-item:not(:last-of-type) {
    margin-bottom: 50px;
}

@media only screen and (max-width: 768px) {
    .locations-contact-item:not(:last-of-type) {
        margin-bottom: 25px;
    }
}

.locations-contact-item h4 {
    font-size: 34px;
    line-height: 40px;
    color: var(--color-red);
    font-weight: 800;
}

@media only screen and (max-width: 768px) {
    .locations-contact-item h4 {
        font-size: 24px;
        line-height: 30px;
    }
}

.locations-contact-item span {
    display: block;
    font-size: 25px;
    font-weight: 800;
    line-height: 40px;
    color: var(--color-black);
}

@media only screen and (max-width: 768px) {
    .locations-contact-item span {
        font-size: 18px;
        line-height: 22px;
    }
}


/* ==========================================================================
    SCSS Styles [PARTIALS:Slider Cars]
   ========================================================================== */

.section-car-slider {
    margin: 75px 0 125px;
}

.section-car-slider--no-button .car-slider--top h2 {
    justify-content: center;
}

.car-slider--top h2 {
    font-size: 44px;
    line-height: 53px;
    font-weight: 800;
    color: var(--color-red);
    margin-bottom: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@media only screen and (max-width: 768px) {
    .car-slider--top h2 {
        font-size: 34px;
        line-height: 41px;
        margin-bottom: 20px;
    }
}

.car-slider--top a.check-fleet {
    font-size: 16px;
    line-height: 19px;
    font-weight: 500;
    color: var(--color-black);
    border-radius: 20px;
    padding: 10px 33px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
}

.car-slider--top a.check-fleet svg {
    transition: all 0.3s;
}

.car-slider--top a.check-fleet::before {
    content: "";
    position: absolute;
    width: calc(100% - 2px);
    z-index: -2;
    height: calc(100% - 0px);
    left: 1px;
    top: 0;
    border: 2px solid var(--color-red);
    border-radius: 18px;
}

.car-slider--top a.check-fleet::after {
    width: calc(100% - 3px);
    height: 0;
    top: 1px;
    left: 2px;
    background: var(--color-red);
    content: "";
    position: absolute;
    z-index: -1;
    transition: all 0.3s;
    border-radius: 17px;
    border: 0;
}

.car-slider--top a.check-fleet:hover {
    color: #fff;
}

.car-slider--top a.check-fleet:hover svg {
    fill: #fff;
}

.car-slider--top a.check-fleet:hover::after {
    height: 100%;
    border: 2px solid var(--color-red);
}

@media only screen and (max-width: 768px) {
    .car-slider--top a.check-fleet {
        max-width: 162px;
        margin-top: 30px;
        text-align: center;
        padding: 10px 0;
    }
}

.car-slider-container {
    margin: 0 -45px;
    /*	overflow: hidden;*/
}

@media only screen and (max-width: 1610px) {
    .car-slider-container {
        margin: 0 -15px;
    }
}

@media only screen and (max-width: 500px) {
    .car-slider-container {
        margin-left: -12%;
    }
}

.car-slider-container .slick-dots {
    bottom: -90px;
}

@media only screen and (max-width: 1610px) {
    .car-slider-container .slick-next {
        bottom: -75px;
        top: unset;
        right: 30px;
        z-index: 99999;
    }
}

.car-slider-container .slick-next:before {
    display: block;
    background-image: url(../images/icons/icon-slick-next.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 44px;
    height: 44px;
    color: transparent;
}

@media only screen and (max-width: 1610px) {
    .car-slider-container .slick-next:before {
        width: 35px;
        height: 35px;
    }
}

.car-slider-container .slick-prev {
    z-index: 99999;
}

@media only screen and (max-width: 1610px) {
    .car-slider-container .slick-prev {
        bottom: -75px;
        top: unset;
        left: 20px;
    }
}

@media only screen and (max-width: 768px) {
    .car-slider-container .slick-prev {
        left: 9%;
    }
}

.car-slider-container .slick-prev:before {
    display: block;
    background-image: url(../images/icons/icon-slick-prev.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 44px;
    height: 44px;
    color: transparent;
}

@media only screen and (max-width: 1610px) {
    .car-slider-container .slick-prev:before {
        width: 35px;
        height: 35px;
    }
}

.car-slider-item {
    border: 1px solid #e3e3e4;
    padding: 20px 15px 13px 15px;
    margin: 0 45px;
    /*	@media only screen and (max-width: $max-md) {
  	max-width: 318px;
  }*/
}

@media only screen and (max-width: 1610px) {
    .car-slider-item {
        margin: 0 15px;
    }
}

.car-slider-item .car-slider-info {
    display: flex;
}

.car-slider-item .car-slider-info--left h3 {
    font-size: 25px;
    line-height: 30px;
    font-weight: 700;
    color: #231f20;
    margin-bottom: 4px;
    flex-basis: 150px;
    max-width: 150px;
}

@media only screen and (max-width: 768px) {
    .car-slider-item .car-slider-info--left h3 {
        font-size: 20px;
        line-height: 24px;
    }
}

.car-slider-item .car-slider-info--right {
    margin-left: auto;
    display: flex;
    flex-direction: column;
}

.car-slider-item .car-slider-info--right__wrapper {
    width: fit-content;
    width: -webkit-fit-content;
    margin-left: auto;
}

.car-slider-item .car-slider-info .car-group {
    display: block;
    font-size: 15px;
    line-height: 18px;
    font-weight: 400;
    color: #60585a;
    margin-bottom: 18px;
}

@media only screen and (max-width: 768px) {
    .car-slider-item .car-slider-info .car-group {
        font-size: 12px;
        line-height: 14px;
    }
}

.car-slider-item .car-slider-info .car-from {
    display: block;
    font-size: 12px;
    font-weight: 400;
    color: #a7a9ac;
}

@media only screen and (max-width: 768px) {
    .car-slider-item .car-slider-info .car-from {
        font-size: 11px;
    }
}

.car-slider-item .car-slider-info .car-price {
    font-size: 48px;
    font-weight: 800;
    color: #cb3832;
    margin-top: -11px;
    display: block;
}

@media only screen and (max-width: 768px) {
    .car-slider-item .car-slider-info .car-price {
        font-size: 40px;
    }
}

.car-slider-item .car-slider-info .car-period {
    display: block;
    font-size: 17px;
    font-weight: 700;
    color: #cb3832;
    margin-top: -13px;
    position: relative;
    padding-right: 20px;
}

@media only screen and (max-width: 768px) {
    .car-slider-item .car-slider-info .car-period {
        font-size: 14px;
    }
}


/*.car-slider-item .car-slider-info .car-period:after {*/


/*  position: absolute;*/


/*  content: "";*/


/*  cursor: pointer;*/


/*  background-image: url(../images/icons/icon-info.svg);*/


/*  background-repeat: no-repeat;*/


/*  background-size: cover;*/


/*  background-position: center;*/


/*  width: 15px;*/


/*  height: 15px;*/


/*  top: -6px;*/


/*  right: 0px;*/


/*}*/

.car-slider-item .car-slider-info .btn {
    margin-top: auto;
    font-size: 18px;
    line-height: 36px;
    padding: 0 30px;
    height: 36px;
}

@media only screen and (max-width: 768px) {
    .car-slider-item .car-slider-info .btn {
        font-size: 15px;
        line-height: 33px;
        height: 33px;
        padding: 0 25px;
    }
}

.car-slider-item .car-slider-info .car-desc {
    display: flex;
}

.car-slider-item .car-slider-info .car-desc span {
    display: block;
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    color: #60585a;
}

@media only screen and (max-width: 768px) {
    .car-slider-item .car-slider-info .car-desc span {
        font-size: 11px;
        line-height: 16px;
    }
}

.car-slider-item .car-slider-info .car-desc--left {
    flex-basis: 104px;
    max-width: 104px;
}

.car-slider-item__img {
    text-align: center;
}

.car-slider-item__img img {
    margin: 0 auto;
}


/* ==========================================================================
    SCSS Styles [PARTIALS:HOME]
   ========================================================================== */

.section-services {
    background-color: #f7b710;
    padding: 50px 0;
}

.section-services h2 {
    font-size: 42px;
    line-height: 50px;
    font-weight: 800;
    color: var(--color-black);
    margin-bottom: 30px;
    text-align: center;
}

@media only screen and (max-width: 768px) {
    .section-services h2 {
        font-size: 30px;
        line-height: 36px;
        color: var(--color-red);
    }
}

.services-row {
    display: flex;
    gap: 30px;
}

@media only screen and (max-width: 1024px) {
    .services-row {
        flex-direction: column;
    }
}

.services-col {
    text-align: center;
}

.services-col--1 {
    flex-basis: 30%;
}

@media only screen and (max-width: 1366px) {
    .services-col--1 {
        flex-basis: 25%;
    }
}

@media only screen and (max-width: 1024px) {
    .services-col--1 {
        order: 2;
        flex-basis: 100%;
    }
}

.services-col--2 {
    flex-basis: 40%;
    align-self: flex-end;
}

@media only screen and (max-width: 1366px) {
    .services-col--2 {
        flex-basis: 50%;
    }
}

@media only screen and (max-width: 1024px) {
    .services-col--2 {
        order: 1;
        flex-basis: 100%;
        align-self: center;
    }
}

.services-col--3 {
    flex-basis: 30%;
}

@media only screen and (max-width: 1366px) {
    .services-col--3 {
        flex-basis: 25%;
    }
}

@media only screen and (max-width: 1024px) {
    .services-col--3 {
        order: 3;
        flex-basis: 100%;
    }
}

a.view-all-services {
    font-size: 16px;
    line-height: 19px;
    color: var(--color-black);
    border-radius: 20px;
    border: 2px solid var(--color-red);
    padding: 10px 23px;
    margin-top: 35px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    z-index: 1;
}

a.view-all-services svg {
    transition: all 0.3s;
}

a.view-all-services::after {
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    background: var(--color-red);
    content: "";
    position: absolute;
    z-index: -1;
    transition: all 0.3s;
    border-radius: 17px;
    border: 0;
}

a.view-all-services:hover {
    color: #fff;
}

a.view-all-services:hover svg {
    fill: #fff;
}

a.view-all-services:hover::after {
    height: 100%;
    border: 2px solid var(--color-red);
}

.service-wrapper {
    margin-bottom: 25px;
    text-align: left;
}

.service-wrapper:last-of-type {
    margin-bottom: 0;
}

.service-wrapper img {
    display: inline-block;
    margin-bottom: 5px;
}

.service-wrapper h3 {
    font-size: 21px;
    line-height: 25px;
    font-weight: bold;
    color: var(--color-red);
    margin-bottom: 5px;
}

@media only screen and (max-width: 768px) {
    .service-wrapper h3 {
        font-size: 20px;
        line-height: 24px;
    }
}

.service-wrapper p {
    font-size: 13px;
    line-height: 25px;
    font-weight: 400;
    color: var(--color-gray);
}

.service-wrapper--left {
    text-align: right;
}

@media only screen and (max-width: 1024px) {
    .service-wrapper--left {
        text-align: left;
    }
}


/* ==========================================================================
	 SCSS Styles [PARTIALS: Fleet]
	========================================================================== */

.section-car-listing {
    margin: 66px 0 0;
}

@media only screen and (max-width: 1024px) {
    .section-car-listing {
        margin: 21px 0 0;
    }
}

.filters-container {
    padding: 0 13px;
    border-top: 1px solid #edb417;
    border-bottom: 1px solid #edb417;
    display: flex;
    position: relative;
    z-index: 1;
}

@media only screen and (max-width: 1145px) {
    .filters-container {
        padding: 0 8px;
    }
}

.filters-container strong,
.filters-container span {
    display: block;
    font-size: 20px;
    font-weight: 500;
    line-height: 40px;
}

.filters-container span {
    color: #000000;
}

.filters-container strong {
    color: #cb3832;
}

.filters-container .count-cars {
    display: flex;
    flex-basis: 86px;
    max-width: 86px;
    border-right: 1px solid #e3e3e4;
    align-items: center;
}

.main-filters {
    display: flex;
    margin-left: auto;
    padding-right: 48px;
}

@media only screen and (max-width: 1366px) {
    .main-filters {
        padding-right: 24px;
    }
}

@media only screen and (max-width: 1145px) {
    .main-filters {
        display: none;
    }
}

.main-filters .filter-item:not(:last-of-type) {
    margin-right: 32px;
}

@media only screen and (max-width: 1024px) {
    .main-filters .filter-item:not(:last-of-type) {
        margin-right: 15px;
    }
}

.filter-item {
    cursor: pointer;
    display: flex;
    position: relative;
    padding: 15px 0;
}

.filter-item img {
    margin-left: 20px;
    width: 22px;
}

@media only screen and (max-width: 1024px) {
    .filter-item img {
        margin-left: 10px;
    }
}

@media only screen and (max-width: 1145px) {
    .filter-item {
        border-bottom: 1px solid #edb417;
        padding: 8px 0;
        display: flex;
        flex-direction: column;
    }
    .filter-item:last-child {
        border-color: #e3e3e4;
    }
    .filter-item>span {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
    }
}

.sort-by {
    padding-left: 23px;
    border-left: 1px solid #e3e3e4;
}

@media only screen and (max-width: 1024px) {
    .sort-by {
        margin-left: 24px;
    }
}

@media only screen and (max-width: 1145px) {
    .sort-by {
        display: none;
    }
}

.filter-mobile {
    display: none;
    margin-left: auto;
}

@media only screen and (max-width: 1145px) {
    .filter-mobile {
        display: flex;
    }
}

.filter-mobile img {
    margin-left: 22px;
}

.filter-mobile-btn {
    margin: 25px 0 15px;
    text-transform: uppercase;
    display: inline-block;
    border-radius: 20px;
    background-color: #cb3832;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: 0.025em;
    font-weight: 500;
    color: #ffffff;
    padding: 12.5px 37px;
    border: none;
    cursor: pointer;
}

.filter-mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: -28px;
    border: 1px solid #edb417;
    border-right: 0;
    background-color: #fff;
    z-index: 1000;
    min-width: 250px;
    padding: 20px 10px;
}

.filter-toggle {
    cursor: pointer;
    padding: 15px 0;
    position: relative;
}

.car-list-container {
    padding-top: 58px;
}

@media only screen and (max-width: 768px) {
    .car-list-container {
        padding-top: 45px;
    }
}

.car-list-item {
    display: flex;
    padding-right: 35px;
    padding-bottom: 21px;
    border: 1px solid #e3e3e4;
}

.car-list-item:not(:last-of-type) {
    margin-bottom: 37px;
}

@media only screen and (max-width: 768px) {
    .car-list-item {
        flex-direction: column;
        padding-right: 0px;
        padding-bottom: 28px;
    }
    .car-list-item:not(:last-of-type) {
        margin-bottom: 35px;
    }
}

.car-list-item--left {
    text-align: center;
    position: relative;
}

.car-list-item--left .bandge-1 {
    position: absolute;
    left: -3px;
    top: 10px;
}

@media only screen and (max-width: 768px) {
    .car-list-item--left .bandge-1 img {
        width: 62px;
    }
}

.car-list-item--left .bandge-2 {
    position: absolute;
    left: -3px;
    top: 71px;
}

@media only screen and (max-width: 768px) {
    .car-list-item--left .bandge-2 img {
        width: 62px;
    }
}

.car-list-item--right {
    flex-basis: 598px;
    max-width: 598px;
    margin-left: auto;
}

@media only screen and (max-width: 768px) {
    .car-list-item--right {
        flex-basis: unset;
        margin-left: 0;
        width: 100%;
        max-width: 534px;
        margin: 0 auto;
        padding: 0 15px;
    }
}

.car-list-item--right .car-group {
    display: block;
    margin-bottom: 15px;
    margin-top: 18px;
    font-size: 15px;
    font-weight: 400;
    line-height: 18px;
    color: #60585a;
}

@media only screen and (max-width: 768px) {
    .car-list-item--right .car-group {
        font-size: 12px;
        line-height: 14px;
        margin-top: 0px;
    }
}

.item-details {
    display: flex;
    height: 283px;
}

@media only screen and (max-width: 1024px) {
    .item-details {
        flex-direction: column;
        height: auto;
    }
}

.item-details--left {
    display: flex;
    flex-direction: column;
    flex-basis: 363px;
    max-width: 363px;
}

@media only screen and (max-width: 1024px) {
    .item-details--left {
        flex-basis: unset;
        max-width: unset;
    }
}

@media only screen and (max-width: 768px) {
    .item-details--left {
        margin-bottom: 30px;
    }
}

.item-details--left h3 {
    font-size: 30px;
    font-weight: 700;
    line-height: 36px;
    color: #231f20;
    margin-bottom: 15px;
}

@media only screen and (max-width: 768px) {
    .item-details--left h3 {
        font-size: 24px;
        line-height: 28px;
        margin-bottom: 13px;
    }
}

.item-details--right {
    flex-basis: 195px;
    max-width: 195px;
    margin-left: auto;
    display: flex;
    flex-direction: column;
}

@media only screen and (max-width: 1024px) {
    .item-details--right {
        flex-basis: unset;
        max-width: unset;
        margin-left: 0;
        flex-direction: row;
        align-items: baseline;
    }
}

.item-details--right .car-price-container {
    display: flex;
    align-items: flex-end;
}

@media only screen and (max-width: 768px) {
    .item-details--right .car-price-container {
        flex-direction: column;
        align-items: baseline;
    }
}

.item-details--right .car-price-container .car-price {
    display: block;
    font-size: 56px;
    line-height: 43px;
    font-weight: 800;
    color: #cb3832;
}

@media only screen and (max-width: 768px) {
    .item-details--right .car-price-container .car-price {
        font-size: 54px;
    }
}

.item-details--right .car-price-container .car-price sup {
    font-size: 21px;
    font-weight: 800;
    top: -26px;
    left: 0;
}

@media only screen and (max-width: 768px) {
    .item-details--right .car-price-container .car-price sup {
        font-size: 20px;
    }
}

.item-details--right .car-price-container .car-price-from {
    position: relative;
}

.item-details--right .car-price-container .car-price-from:before {
    content: "from";
    position: absolute;
    font-size: 12px;
    font-weight: 400;
    color: #a7a9ac;
    top: -46px;
    text-transform: uppercase;
    left: 5px;
}

@media only screen and (max-width: 1024px) {
    .item-details--right .car-price-container .car-price-from:before {
        position: unset;
        display: block;
    }
}

.item-details--right .car-price-container .car-period {
    display: block;
    font-size: 17px;
    line-height: 13px;
    font-weight: 700;
    color: #cb3832;
    position: relative;
    left: -12px;
}

.item-details--right .car-price-container .car-period-tooltip {
    background-color: #edb417;
    border-radius: 25px 25px 25px 0;
    width: 140px;
    position: absolute;
    color: #fff;
    font-size: 9px;
    line-height: 11px;
    text-align: center;
    padding: 15px 10px;
    bottom: 12px;
    z-index: 1;
    left: 100%;
    visibility: hidden;
    opacity: 0;
    transition: all 400ms ease-in;
}

@media only screen and (max-width: 1366px) {
    .item-details--right .car-price-container .car-period-tooltip {
        border-radius: 25px 25px 0 25px;
        right: -18px;
        left: unset;
    }
}


/*@media only screen and (min-width: 1024px) {*/


/*.item-details--right .car-price-container .car-period:hover .car-period-tooltip {*/


/*  visibility: visible;*/


/*  opacity: 1;*/


/*  transition: all 400ms ease-out;*/


/*}*/


/*}*/


/*.item-details--right .car-price-container .car-period:after {*/


/*  position: absolute;*/


/*  content: "";*/


/*  cursor: pointer;*/


/*  background-image: url(../images/icons/icon-info.svg);*/


/*  background-repeat: no-repeat;*/


/*  background-size: cover;*/


/*  background-position: center;*/


/*  width: 15px;*/


/*  height: 15px;*/


/*  top: -15px;*/


/*  right: -18px;*/


/*}*/


/*@media only screen and (max-width: 768px) {*/


/*  .item-details--right .car-price-container .car-period {*/


/*    font-size: 21px;*/


/*    line-height: 30px;*/


/*    left: 0;*/


/*  }*/


/*  .item-details--right .car-price-container .car-period:after {*/


/*    width: 18px;*/


/*    height: 18px;*/


/*    top: -3px;*/


/*    right: -24px;*/


/*  }*/


/*}*/

.car-extras {
    display: flex;
    justify-content: space-between;
    max-width: 350px;
}

@media only screen and (max-width: 768px) {
    .car-extras {
        margin-bottom: 30px;
        max-width: 245px;
    }
}

.car-extras__item {
    display: flex;
    align-items: center;
}

.car-extras__item:not(:last-of-type) {
    margin-right: 5px;
}

.car-extras__item img {
    margin-right: 8px;
}

.car-extras__item span {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #edb417;
}

@media only screen and (max-width: 768px) {
    .car-extras__item span {
        font-size: 11px;
    }
}

.car-information {
    display: flex;
    margin-top: auto;
    max-width: 280px;
    width: 100%;
    align-items: center;
    justify-content: space-between;
}

@media only screen and (max-width: 768px) {
    .car-information {
        align-items: flex-start;
        justify-content: flex-start;
    }
}

.car-information ul {
    margin-left: 17px;
}

.car-information ul li {
    font-size: 16px;
    font-weight: 400;
    line-height: 34px;
    color: #60585a;
}

@media only screen and (max-width: 768px) {
    .car-information ul li {
        font-size: 14px;
        line-height: 27px;
    }
}

.car-information--left {
    margin-right: 10px;
}

@media only screen and (max-width: 768px) {
    .car-information--left {
        margin-right: 54px;
    }
}

.item-extra-bottom {
    margin-top: auto;
}

@media only screen and (max-width: 1024px) {
    .item-extra-bottom {
        margin-top: 0;
        margin-left: auto;
    }
}

.item-extra-bottom ul {
    margin-bottom: 21px;
    list-style: none;
}

@media only screen and (max-width: 1024px) {
    .item-extra-bottom ul {
        margin-bottom: 10px;
    }
}

.item-extra-bottom ul li {
    font-size: 11px;
    font-weight: 400;
    line-height: 22px;
    color: #999b9e;
    position: relative;
}

@media only screen and (max-width: 768px) {
    .item-extra-bottom ul li {
        font-size: 9px;
        line-height: 18px;
    }
}

.item-extra-bottom ul li:before {
    content: "";
    background-image: url(../images/icons/icon-tick-yellow.svg);
    width: 15.5px;
    height: 11.5px;
    display: inline-block;
    margin-right: 7px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

@media only screen and (max-width: 768px) {
    .item-extra-bottom ul li:before {
        width: 13px;
        height: 9.7px;
        margin-right: 5px;
    }
}

.btn-grey {
    font-size: 20px;
    background-color: #8e8e8e;
    color: #ffffff;
}

@media only screen and (max-width: 1024px) {
    .btn-grey {
        font-size: 17px;
        padding: 13.5px 8px;
    }
}

.car-list-pagination {
    display: flex;
    align-items: center;
    margin-top: 50px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 166px;
}

@media only screen and (max-width: 1024px) {
    .car-list-pagination {
        display: none;
    }
}

.car-list-pagination img {
    margin-left: 35px;
    cursor: pointer;
}

.car-list-pagination .pagination-item {
    display: block;
    font-size: 16px;
    line-height: 19px;
    font-weight: 500;
    color: #686264;
    cursor: pointer;
}

.car-list-pagination .pagination-item.deactive {
    cursor: none;
    pointer-events: none;
}

.car-list-pagination .pagination-item.active {
    color: #231f20;
}

.car-list-pagination .pagination-item:not(:last-of-type) {
    margin-right: 12px;
}

.contact-form-flexbox.contact-form-flexbox__less-padding {
    padding: 80px 114px 45px;
}

@media only screen and (max-width: 1366px) {
    .contact-form-flexbox.contact-form-flexbox__less-padding {
        padding: 60px 40px 45px;
    }
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox.contact-form-flexbox__less-padding {
        padding: 45px 25px 35px;
    }
}

.contact-form-flexbox.contact-form-flexbox__less-padding p.normal-paragraph {
    font-size: 14px;
    line-height: 22px;
    color: #60585a;
}

.contact-form-flexbox.contact-form-flexbox__less-padding .read-more {
    font-size: 16px;
    line-height: 19px;
    font-weight: 500;
    text-decoration: none;
    color: var(--color-black);
    display: block;
    margin-top: 28px;
}

/* .filter-radio {
    margin: 0.5rem;
}

@media only screen and (max-width: 1145px) {
    .filter-radio {
        position: relative;
    }
}

.filter-radio input[type=radio] {
    position: absolute;
    opacity: 0;
}

@media only screen and (max-width: 1145px) {
    .filter-radio input[type=radio] {
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
}

.filter-radio input[type=radio]+.radio-label {
    display: flex;
    align-items: center;
    font-size: 12px;
    cursor: pointer;
}

.filter-radio input[type=radio]+.radio-label::after {
    content: "";
    background: #fff;
    border-radius: 100%;
    border: 1px solid #e3e3e4;
    display: inline-block;
    width: 1.4em;
    height: 1.4em;
    position: relative;
    margin-left: auto;
    vertical-align: top;
    cursor: pointer;
    text-align: center;
    transition: all 250ms ease;
}

.filter-radio input[type=radio]:checked+.radio-label::after {
    background-color: #edb417;
    box-shadow: inset 0 0 0 3px #fff;
}

.filter-radio input[type=radio]:focus+.radio-label::after {
    outline: none;
    border-color: #e3e3e4;
} */

.filter-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    border: 1px solid #edb417;
    background-color: #fff;
    z-index: 1000;
    min-width: 200px;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem;
}

.filter-dropdown-list {
    list-style: none;
}

@media only screen and (max-width: 1145px) {
    .filter-dropdown {
        position: relative;
        top: unset;
        left: unset;
        border: unset;
        min-width: unset;
        transform: unset;
        width: 100%;
    }
}


/* ==========================================================================
    SCSS Styles [PARTIALS:CAR]
   ========================================================================== */

.section-car-details .final-price-wrapper {
    position: sticky;
    top: 165px;
    margin-top: 100px;
    margin-bottom: 100px;
}

@media only screen and (max-width: 1024px) {
    .section-car-details .final-price-wrapper {
        margin-top: 0;
        padding: 10px 20px;
        top: 112px;
        z-index: 100000;
    }
    .section-car-details .final-price-wrapper .class {
        margin-bottom: 0;
    }
    .section-car-details .final-price-wrapper .rent-btn {
        grid-area: 1/3/3/4;
        padding: 0 30px;
        height: 62px;
        line-height: 62px;
    }
}

@media only screen and (max-width: 768px) {
    .section-car-details .final-price-wrapper .rent-btn {
        padding: 0 15px;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
    }
}

@media only screen and (max-width: 1024px) {
    .section-car-details .col-lg-12:nth-child(1) {
        order: 2;
    }
    .section-car-details .col-lg-12:nth-child(2) {
        order: 1;
    }
    .section-car-details .breadcrumbs {
        display: none;
    }
}

.car-section-title {
    text-transform: uppercase;
    color: #231f20;
    font-size: 20px;
    line-height: 32px;
    font-weight: bold;
}

@media only screen and (max-width: 768px) {
    .car-section-title {
        font-size: 18px;
    }
}

.final-price-wrapper {
    background-color: #ededee;
    padding: 40px 35px 30px;
}

@media only screen and (max-width: 1024px) {
    .final-price-wrapper {
        margin: 0 -20px;
        padding: 40px 20px 30px;
    }
}

.final-price-wrapper h4 {
    text-transform: uppercase;
    font-size: 16px;
    font-weight: 400;
    color: #515151;
    margin-bottom: 15px;
}

@media only screen and (max-width: 768px) {
    .final-price-wrapper h4 {
        font-size: 12px;
    }
}

.final-price-wrapper h3 {
    font-size: 26px;
    line-height: 31px;
    font-weight: bold;
    color: #231f20;
    margin-bottom: 10px;
}

@media only screen and (max-width: 768px) {
    .final-price-wrapper h3 {
        font-size: 20px;
        line-height: 24px;
    }
}

.final-price-wrapper .class {
    font-size: 16px;
    line-height: 19px;
    color: var(--color-gray);
    margin-bottom: 80px;
}

@media only screen and (max-width: 768px) {
    .final-price-wrapper .class {
        font-size: 12px;
        line-height: 14px;
    }
}

.final-price-wrapper .booking-dates {
    border-top: 1px dotted #9c9c9d;
    border-bottom: 1px dotted #9c9c9d;
    padding: 5px 15px;
    margin-bottom: 57px;
}

.final-price-wrapper .booking-dates span {
    color: #edb417;
    font-size: 16px;
    line-height: 19px;
    font-weight: bold;
}

.final-price-wrapper .order-summary {
    list-style: none;
    padding: 0;
    margin: 0;
    margin-bottom: 40px;
}

.final-price-wrapper .order-summary li {
    border-bottom: 1px solid #000;
    font-size: 20px;
    line-height: 52px;
    font-weight: 400;
    color: var(--color-gray);
    padding: 5px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.final-price-wrapper .order-summary li:last-child {
    font-size: 22px;
    line-height: 52px;
    font-weight: 700;
    color: var(--color-red);
}

.final-price-wrapper .order-summary .cost {
    text-align: left;
    flex-basis: 20%;
}

.final-price-wrapper a.rent-btn {
    font-size: 20px;
    font-weight: 500;
    background-color: var(--color-red);
    color: #fff;
    text-transform: uppercase;
    display: block;
    width: 100%;
    text-align: center;
    border-radius: 30px;
}

.price-box {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-rows: 1fr 1fr;
    color: var(--color-red);
    margin-bottom: 25px;
}

.price-box .amount {
    grid-area: 1/1/3/2;
    font-size: 71px;
    line-height: 62px;
    font-weight: 800;
}

@media only screen and (max-width: 768px) {
    .price-box .amount {
        font-size: 47px;
        line-height: 40px;
    }
}

.price-box .currency {
    grid-area: 1/2/2/3;
    font-size: 28px;
    line-height: 31px;
    font-weight: 800;
}

@media only screen and (max-width: 768px) {
    .price-box .currency {
        font-size: 15px;
        line-height: 20px;
    }
}

.price-box .rent-duration {
    grid-area: 2/2/3/3;
    font-size: 21px;
    line-height: 31px;
    font-weight: 700;
}

@media only screen and (max-width: 768px) {
    .price-box .rent-duration {
        font-size: 15px;
        line-height: 20px;
    }
}

.single-car-slider .slick-arrow {
    height: 44px;
    width: 44px;
}

@media only screen and (max-width: 1610px) {
    .single-car-slider .slick-arrow {
        height: 35px;
        width: 35px;
    }
}

.single-car-slider .slick-next {
    right: 25px;
}

.single-car-slider .slick-next:before {
    display: block;
    background-image: url(../images/icons/icon-slick-next.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 44px;
    height: 44px;
    color: transparent;
}

@media only screen and (max-width: 1610px) {
    .single-car-slider .slick-next:before {
        width: 35px;
        height: 35px;
    }
}

.single-car-slider .slick-prev {
    left: 0;
    z-index: 99999;
}

.single-car-slider .slick-prev:before {
    display: block;
    background-image: url(../images/icons/icon-slick-prev.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 44px;
    height: 44px;
    color: transparent;
}

@media only screen and (max-width: 1610px) {
    .single-car-slider .slick-prev:before {
        width: 35px;
        height: 35px;
    }
}

.single-car-slide img {
    width: 100%;
}

.car-description p {
    font-size: 18px;
    line-height: 32px;
    font-weight: 400;
    color: #000000;
}

@media only screen and (max-width: 768px) {
    .car-description p {
        font-size: 16px;
        line-height: 27px;
    }
}

.car-hr {
    background: #edb417;
    margin: 30px 0;
}

.car-features {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
}

@media only screen and (max-width: 768px) {
    .car-features {
        grid-template-columns: repeat(2, 1fr);
    }
}

.car-feature {
    display: flex;
    align-items: center;
    padding: 20px 0;
}

.car-feature span {
    color: #edb417;
    font-size: 20px;
    line-height: 24px;
    font-weight: 500;
    margin-left: 10px;
}

@media only screen and (max-width: 768px) {
    .car-feature span {
        font-size: 18px;
        line-height: 21px;
    }
}

.car-characteristics {
    display: grid;
    gap: 15px;
    grid-template-columns: repeat(3, 1fr);
}

@media only screen and (max-width: 1024px) {
    .car-characteristics {
        grid-template-columns: repeat(2, 1fr);
    }
}

.car-characteristic {
    position: relative;
    font-size: 22px;
    line-height: 44px;
    font-weight: 400;
    color: var(--color-gray);
    padding-left: 25px;
    display: flex;
    align-items: center;
}

@media only screen and (max-width: 768px) {
    .car-characteristic {
        font-size: 18px;
        line-height: 21px;
    }
}

.car-characteristic::before {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    content: "•";
}

.included .car-section-title {
    margin-bottom: 15px;
}

.included-list {
    list-style: none;
    margin: 0;
    padding: 0;
    columns: 2;
}

@media only screen and (max-width: 768px) {
    .included-list {
        columns: unset;
    }
}

.included-item {
    position: relative;
    padding-left: 25px;
    font-size: 20px;
    margin-bottom: 20px;
    font-weight: 400;
    color: var(--color-gray);
}

.included-item::before {
    position: absolute;
    left: 0;
    content: url(../images/check.svg);
}

.fuel-options-wrapper .car-section-title {
    margin-bottom: 45px;
}

@media only screen and (max-width: 768px) {
    .fuel-options-wrapper .car-section-title {
        margin-bottom: 20px;
    }
}

.fuel-options {
    display: flex;
    align-items: center;
    margin-bottom: 45px;
    gap: 40px;
}

@media only screen and (max-width: 1024px) {
    .fuel-options {
        flex-direction: column;
        align-items: flex-start;
    }
}

.section-optional-extras .car-section-title {
    margin-bottom: 40px;
}

@media only screen and (max-width: 768px) {
    .section-optional-extras .car-section-title {
        margin-bottom: 15px;
    }
}

.section-optional-extras .class {
    margin-bottom: 30px;
}

.section-optional-extras .final-price-wrapper {
    margin-top: 72px;
    padding-top: 20px;
    padding-bottom: 30px;
}

.section-optional-extras h4 {
    margin-bottom: 5px;
}

.section-optional-extras .price-box {
    margin-bottom: 10px;
}

.section-optional-extras .order-summary {
    margin-bottom: 30px;
}


/* The container */

.extras-checkbox-container {
    display: block;
    position: relative;
    padding-left: 110px;
    cursor: pointer;
    line-height: 30px;
    font-weight: 400;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

@media only screen and (max-width: 768px) {
    .extras-checkbox-container {
        padding-left: 55px;
    }
}


/* Hide the browser's default checkbox */

.extras-checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}


/* Create a custom checkbox */

.extras-checkbox {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    height: 57px;
    width: 57px;
    background-color: #edb417;
    border-radius: 50%;
}

@media only screen and (max-width: 768px) {
    .extras-checkbox {
        height: 37px;
        width: 37px;
    }
}


/* When the checkbox is checked, add a blue background */

.extras-checkbox-container input:checked~.extras-checkbox {
    background-color: #000;
}


/* Create the extras-checkbox/indicator (hidden when not checked) */

.extras-checkbox:after {
    position: absolute;
    display: block;
}


/* Show the extras-checkbox when checked */

.extras-checkbox-container input:checked~.extras-checkbox:after {
    content: "";
    background-image: url(../images/check-orange.svg);
    background-repeat: no-repeat;
    background-position: center;
    height: 40px;
    width: 40px;
}

@media only screen and (max-width: 768px) {
    .extras-checkbox-container input:checked~.extras-checkbox:after {
        height: 20px;
        width: 20px;
        background-size: contain;
    }
}


/* Style the extras-checkbox/indicator */

.extras-checkbox-container .extras-checkbox:after {
    content: "+";
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    height: 35px;
    font-size: 50px;
}

@media only screen and (max-width: 768px) {
    .extras-checkbox-container .extras-checkbox:after {
        font-size: 35px;
        height: 40px;
    }
}

.optional-extras-wrapper {
    margin-bottom: 65px;
}

@media only screen and (max-width: 768px) {
    .optional-extras-wrapper {
        margin-bottom: 20px;
    }
}

.optional-extra-item {
    background-color: #ededee;
    padding: 32px 20px;
    margin-bottom: 25px;
}

@media only screen and (max-width: 768px) {
    .optional-extra-item {
        margin-bottom: 15px;
        padding: 20px 12.5px;
        padding-right: 5px;
    }
}

.optional-extra-item.selected {
    background-color: #edb417;
}

.optional-extra-item:last-child {
    margin-bottom: 0;
}

.optional-extra-item .title {
    font-size: 20px;
    font-weight: 400;
    color: var(--color-gray);
}

@media only screen and (max-width: 768px) {
    .optional-extra-item .title {
        font-size: 13px;
        display: block;
        line-height: 13px;
        margin-bottom: 10px;
    }
}

.optional-extra-item .title strong {
    font-size: 25px;
    font-weight: bold;
    color: #000;
}

@media only screen and (max-width: 768px) {
    .optional-extra-item .title strong {
        font-size: 16px;
    }
}

.optional-extra-item .description {
    display: block;
    font-size: 20px;
    font-weight: 400;
    color: var(--color-gray);
}

@media only screen and (max-width: 768px) {
    .optional-extra-item .description {
        font-size: 13px;
        line-height: 1;
    }
}

.section-personal-details .class {
    margin-bottom: 10px;
}

.section-personal-details .car-section-title {
    margin-bottom: 30px;
}

@media only screen and (max-width: 768px) {
    .section-personal-details .car-section-title {
        margin-bottom: 15px;
    }
}

.section-personal-details .order-summary {
    margin-bottom: 20px;
}

.section-personal-details .custom-checkbox-container {
    margin: 0 16px 12px;
    text-transform: none;
}

.section-personal-details .custom-checkbox-container a {
    color: #000;
}

.section-personal-details .custom-checkbox-container .custom-checkbox {
    background-color: #a8aaac;
}

.client-form-wrapper {
    background-color: #ededee;
    padding: 30px 50px 35px;
}

@media only screen and (max-width: 1024px) {
    .client-form-wrapper {
        margin: 0 -20px;
        padding: 30px 20px 35px;
        margin-bottom: 20px;
    }
}

.client-form {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.client-form .input-wrapper {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 13px;
    width: 100%;
}

.client-form .input-wrapper input {
    height: 42px;
    font-size: 14px;
    /* Chrome/Opera/Safari */
    /* Firefox 19+ */
    /* IE 10+ */
    /* Firefox 18- */
}

.client-form .input-wrapper input::-webkit-input-placeholder {
    color: #cec4c7;
}

.client-form .input-wrapper input::-moz-placeholder {
    color: #cec4c7;
}

.client-form .input-wrapper input:-ms-input-placeholder {
    color: #cec4c7;
}

.client-form .input-wrapper input:-moz-placeholder {
    color: #cec4c7;
}

.client-form .input-wrapper input ::placeholder {
    color: #cec4c7;
}

.client-form .input-wrapper--half {
    width: 50%;
}

@media only screen and (max-width: 1024px) {
    .client-form .input-wrapper--half {
        width: 100%;
    }
}

.client-form .input-select {
    height: 42px;
    font-size: 14px;
    width: 100%;
    background: #fff;
    color: #cec4c7;
    /* Chrome/Opera/Safari */
    /* Firefox 19+ */
    /* IE 10+ */
    /* Firefox 18- */
    letter-spacing: 0.005em;
    padding-left: 14px;
    border-radius: 26px;
    border: 10px solid #fff;
}

.client-form .input-select::-webkit-input-placeholder {
    color: #cec4c7;
}

.client-form .input-select::-moz-placeholder {
    color: #cec4c7;
}

.client-form .input-select:-ms-input-placeholder {
    color: #cec4c7;
}

.client-form .input-select:-moz-placeholder {
    color: #cec4c7;
}

.client-form .input-select ::placeholder {
    color: #cec4c7;
}

.client-form .input-label {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
    padding-bottom: 0;
}

.client-form textarea {
    height: 83px;
    border-radius: 42px;
    border: none;
    background-color: #fff;
    width: 100%;
    resize: none;
    color: #000;
    /* Chrome/Opera/Safari */
    /* Firefox 19+ */
    /* IE 10+ */
    /* Firefox 18- */
    font-size: 14px;
    padding: 5px 25px;
}

.client-form textarea::-webkit-input-placeholder {
    color: #cec4c7;
}

.client-form textarea::-moz-placeholder {
    color: #cec4c7;
}

.client-form textarea:-ms-input-placeholder {
    color: #cec4c7;
}

.client-form textarea:-moz-placeholder {
    color: #cec4c7;
}

.client-form textarea ::placeholder {
    color: #cec4c7;
}


/* The container */

.client-form-checkbox-container {
    display: block;
    position: relative;
    padding-left: 55px;
    padding-right: 10px;
    cursor: pointer;
    line-height: 30px;
    font-weight: 400;
    font-size: 12px;
    text-transform: uppercase;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}


/* Hide the browser's default checkbox */

.client-form-checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}


/* Create a custom checkbox */

.client-form-checkbox {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    height: 42px;
    width: 42px;
    background-color: #fff;
    border-radius: 50%;
}


/* When the checkbox is checked, add a blue background */

.client-form-checkbox-container input:checked~.client-form-checkbox {
    background-color: #edb417;
    border: 6px solid #fff;
}


/* Create the client-form-checkbox/indicator (hidden when not checked) */

.client-form-checkbox:after {
    position: absolute;
    display: block;
}


/* Style the client-form-checkbox/indicator */

.client-form-checkbox-container .client-form-checkbox:after {
    content: "";
}

.input-wrapper--checkbox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 38px !important;
    padding-top: 26px;
}

.input-wrapper--checkbox .label {
    display: block;
    font-size: 12px;
    line-height: 22px;
    letter-spacing: 0.005em;
    color: #231f20;
    text-transform: uppercase;
    font-weight: bold;
    padding-bottom: 0;
}


/* ==========================================================================
    SCSS Styles [PARTIALS:FOOTER]
   ========================================================================== */
/* 
.site-footer {
    background-color: #cb3832;
    padding: 62px 0 40px;
}

@media only screen and (max-width: 768px) {
    .site-footer {
        padding: 30px 0 25px;
    }
}

.site-footer--top {
    display: flex;
    padding-bottom: 44px;
}

@media only screen and (max-width: 1366px) {
    .site-footer--top {
        justify-content: space-between;
    }
}

@media only screen and (max-width: 1024px) {
    .site-footer--top {
        flex-wrap: wrap;
    }
}

.site-footer--top span {
    display: block;
    font-size: 20px;
    line-height: 30px;
    font-weight: 800;
    color: #ffffff;
}

@media only screen and (max-width: 768px) {
    .site-footer--top span {
        font-size: 15px;
        line-height: 26px;
        display: inline-block;
    }
}

.site-footer--bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 30px;
    padding-bottom: 37px;
}

@media only screen and (max-width: 1366px) {
    .site-footer--bottom {
        flex-direction: column;
        padding-bottom: 0px;
    }
}

.footer-title {
    font-size: 20px;
    line-height: 24px;
    font-weight: 800;
    color: #edb417;
    padding-bottom: 7px;
}

@media only screen and (max-width: 768px) {
    .footer-title {
        font-size: 15px;
        line-height: 18px;
    }
}

.footer-col-1 {
    flex-basis: 216px;
    max-width: 216px;
}

@media only screen and (max-width: 1366px) {
    .footer-col-1 {
        flex-basis: initial;
        max-width: initial;
    }
}

@media only screen and (max-width: 1024px) {
    .footer-col-1 {
        display: none;
    }
}

.footer-col-2 {
    flex-basis: 465px;
    max-width: 465px;
}

@media only screen and (max-width: 1366px) {
    .footer-col-2 {
        flex-basis: initial;
        max-width: 180px;
    }
}

@media only screen and (max-width: 1024px) {
    .footer-col-2 {
        flex-basis: 60%;
        max-width: unset;
        flex-grow: 1;
        margin-bottom: 44px;
    }
}

@media only screen and (max-width: 768px) {
    .footer-col-2 {
        flex-basis: unset;
        max-width: unset;
    }
}

.footer-col-3 {
    flex-basis: 210px;
    max-width: 210px;
}

@media only screen and (max-width: 1366px) {
    .footer-col-3 {
        flex-basis: initial;
        max-width: initial;
    }
}

@media only screen and (max-width: 1024px) {
    .footer-col-3 {
        margin-bottom: 44px;
        max-width: 188px;
        flex-basis: 188px;
        margin-left: 20px;
    }
}

@media only screen and (max-width: 768px) {
    .footer-col-3 {
        flex-basis: unset;
        max-width: unset;
        display: flex;
        align-items: flex-end;
        padding-left: 32px;
    }
    .footer-col-3 .footer-title {
        display: none;
    }
}

.footer-col-4 {
    flex-basis: 316px;
    max-width: 316px;
}

@media only screen and (max-width: 1366px) {
    .footer-col-4 {
        flex-basis: initial;
        max-width: initial;
    }
}

@media only screen and (max-width: 1024px) {
    .footer-col-4 {
        flex-basis: 60%;
        max-width: 60%;
    }
}

@media only screen and (max-width: 768px) {
    .footer-col-4 {
        flex-basis: 100%;
        max-width: 100%;
        margin-bottom: 25px;
    }
}

.footer-col-5 {
    flex-basis: 184px;
    max-width: 184px;
}

@media only screen and (max-width: 1366px) {
    .footer-col-5 {
        flex-basis: initial;
        max-width: initial;
    }
}

@media only screen and (max-width: 1024px) {
    .footer-col-5 {
        flex-basis: 188px;
        max-width: 188px;
    }
}

@media only screen and (max-width: 768px) {
    .footer-col-5 {
        flex-basis: 100%;
        max-width: 100%;
        display: flex;
    }
}

@media only screen and (max-width: 768px) {
    .footer-col-5 .footer-contact {
        flex-basis: 50%;
        max-width: 50%;
    }
}

.footer-col-5 .footer-contact:not(:first-of-type) {
    margin-top: 22px;
}

@media only screen and (max-width: 768px) {
    .footer-col-5 .footer-contact:not(:first-of-type) {
        margin-top: 0;
    }
}

.footer-col-5 a {
    display: block;
    font-size: 20px;
    line-height: 30px;
    font-weight: 800;
    color: #ffffff;
}

@media only screen and (max-width: 768px) {
    .footer-col-5 a {
        font-size: 15px;
        line-height: 26px;
    }
}

.footer-menu {
    list-style: none;
}

.footer-menu li a {
    font-size: 20px;
    line-height: 36px;
    font-weight: 800;
    color: #ffffff;
    letter-spacing: -0.01em;
}

.footer-menu li a:hover {
    color: #edb417;
}

.footer-form__newsletter {
    position: relative;
    margin-top: 8px;
    width: 100%;
    max-width: 385px;
}

@media only screen and (max-width: 1024px) {
    .footer-form__newsletter {
        max-width: 540px;
    }
}

.footer-form__newsletter .submit-footer-form {
    position: absolute;
    cursor: pointer;
    right: 12px;
    top: 50%;
    transform: translate(0, -50%);
    width: 30px;
    height: 30px;
    background-image: url(../images/icons/icon-play.svg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

@media only screen and (max-width: 768px) {
    .footer-form__newsletter .submit-footer-form {
        width: 23px;
        height: 23px;
        right: 9px;
    }
}

.footer-form__newsletter input {
    border-radius: 20px;
    background-color: #ffffff;
    width: 100%;
    max-width: 385px;
    font-size: 14px;
    font-weight: 400;
    color: #60585a;
    line-height: 47px;
    height: 47px;
    padding: 19px;
    border: none;
}

@media only screen and (max-width: 1024px) {
    .footer-form__newsletter input {
        max-width: 100%;
    }
}

@media only screen and (max-width: 768px) {
    .footer-form__newsletter input {
        font-size: 10px;
        line-height: 36px;
        height: 36px;
    }
}

.footer-form__newsletter input:focus {
    outline: none;
}

.footer-social {
    display: flex;
}

.footer-social a:first-of-type {
    margin-right: 11px;
}

.footer-social svg {
    width: 56px;
    height: 56px;
}

@media only screen and (max-width: 768px) {
    .footer-social svg {
        width: 40px;
        height: 40px;
    }
}

.footer-social svg:hover .cls-1 {
    stroke: #edb417;
}

.footer-social svg:hover .cls-2 {
    fill: #edb417;
}

.icon-fb,
.icon-instagram {
    width: 56px;
}

@media only screen and (max-width: 768px) {
    .icon-fb,
    .icon-instagram {
        width: 35px;
    }
}

.border-bottom {
    border-bottom: 0.5px solid #ffffff;
}

.icon-footer-google {
    width: 248px;
}

@media only screen and (max-width: 1024px) {
    .icon-footer-google {
        width: 186px;
    }
}

.icon-eot {
    width: 56px;
    height: 56px;
}

@media only screen and (max-width: 1024px) {
    .icon-eot {
        width: 54px;
        height: 54px;
    }
}

.footer-eot {
    display: flex;
    align-items: center;
}

@media only screen and (max-width: 1366px) {
    .footer-eot {
        margin-bottom: 22.5px;
    }
}

.footer-eot span {
    display: block;
    font-size: 10px;
    line-height: 18px;
    font-weight: 400;
    letter-spacing: 0.005em;
    color: #ffffff;
    margin-left: 13px;
}

@media only screen and (max-width: 1366px) {
    .footer-rights {
        margin-bottom: 22.5px;
        text-align: center;
    }
}

.footer-rights br {
    display: none;
}

@media only screen and (max-width: 1366px) {
    .footer-rights br {
        display: block;
    }
}

.footer-rights .seperator {
    display: inline-block;
}

@media only screen and (max-width: 1366px) {
    .footer-rights .seperator {
        display: none;
    }
}

.footer-rights span,
.footer-rights a {
    display: inline-block;
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.005em;
    color: #ffffff;
} */


/* ==========================================================================
   Print styles.
   https://www.phpied.com/delay-loading-your-print-css/
   ========================================================================== */

@media print {
    *,
    *::before,
    *::after {
        background: #fff !important;
        color: #000 !important;
        /* Black prints faster */
        box-shadow: none !important;
        text-shadow: none !important;
    }
    a,
    a:visited {
        text-decoration: underline;
    }
    a[href]::after {
        content: " (" attr(href) ")";
    }
    abbr[title]::after {
        content: " (" attr(title) ")";
    }
    /*
      * Don't show links that are fragment identifiers,
      * or use the `javascript:` pseudo protocol
      */
    a[href^="#"]::after,
    a[href^="javascript:"]::after {
        content: "";
    }
    pre {
        white-space: pre-wrap !important;
    }
    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid;
    }
    /*
      * Printing Tables:
      * https://web.archive.org/web/20180815150934/http://css-discuss.incutio.com/wiki/Printing_Tables
      */
    thead {
        display: table-header-group;
    }
    tr,
    img {
        page-break-inside: avoid;
    }
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3;
    }
    h2,
    h3 {
        page-break-after: avoid;
    }
}


/*# sourceMappingURL=bundle.css.map */