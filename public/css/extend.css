:root {
    --background-yellow: #edb417;
    --background-orange: #FD4600;
    --background-indigo: #4830E0;
    --background-green: #2FC059;
    --color-black: #231f20;
    --color-light-gray: #a7a9ac;
}

.contact-form-flexbox h1 {
    color: #edb417;
    font-size: 36px;
    line-height: 43px;
    font-weight: 800;
    margin-bottom: 20px;
    margin-top: 0;
}

@media only screen and (max-width: 768px) {
    .contact-form-flexbox h1 {
        font-size: 26px;
        line-height: 31px;
    }
}

.contact-form-flexbox--right h1 {
    padding-left: 24px;
}

.captcha-wrapper {
    margin-top: 13px;
}

.section-text {
    padding: 50px 0px;
}

.section-features.no-top-padding,
.section-faq.no-top-padding {
    padding-top: 0;
}

.content-with-image.no-top-padding {
    padding-top: 0;
}

.section-car-slider {
    margin: 50px 0 125px;
}

.footer-form__newsletter input {
    padding: 19px 55px 19px 13px;
}

/***********************/
/****** Home Slider *****/
/***********************/
.car-slider-item {
    padding: 0 1rem 2rem;
}

.car-slider-item .badges {
    display: flex;
    justify-content: center;
    font-size: .5rem;
    gap: .4rem;
    color: #fff;
    align-items: center;
}


.car-slider-item .badges .badge-item {
    display: flex;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: 32%;
    justify-content: center;
    column-gap: 0px;
}

.car-slider-item .badges .badge-item img {
    width: 40px;
}

.car-slider-item .badges .badge-item.offer {
    background: var(--background-orange);
}

.car-slider-item .badges .badge-item.offer img{
    height: 30px;
}

.car-slider-item .badges .badge-item.popular {
    background: var(--background-indigo);
}

.car-slider-item .badges .badge-item.eco {
    background: var(--background-green);
}

.car-slider-item .badges .badge-item img {
    padding: 0 0.3rem;
}

.car-slider-item .badges .badge-item span {
    flex-shrink: 0;
}

.car-slider-item__img img {
    margin: 0 auto 10px;
}


.car-slider-item .car-slider-info--left h3 {
    margin: .5rem 0;
}

.car-slider-item .car-slider-info .car-props {
    display: flex;
    flex-direction: column;
    gap: .5rem;
}

.car-slider-item .car-props .car-prop-item {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.car-slider-item .car-props .car-prop-item img {
    width: 35px;
}

.car-slider-item .car-slider-info .car-group {
    margin: 0;
    color: var(--color-light-gray);
}

.car-slider-item .car-slider-info .car-from {
    text-transform: uppercase;
}

.car-slider-item .car-slider-info--right__wrapper {
    background-color: var(--background-yellow);
    margin: .5rem 0;
    border-radius: 1rem;
    padding: .8rem;
}

.car-slider-item .car-slider-info--right {
    align-items: center;
}

.car-slider-item .car-slider-info .car-price {
    color: var(--color-black);
    margin: 0;
    line-height: 1;
}

.car-slider-item .car-slider-info .car-period {
    color: var(--color-black);
    margin: 0;
}

.car-slider-item .car-slider-info .btn {
    margin: unset;
}

/* Slider End */


.car-list-item__image {
    width: 100%;
    max-width: unset;
    margin-right: unset;
}

.section-car-listing {
    margin: 30px 0 0;
}

.btn {
    text-align: center;
}

.btn-yellow {
    font-size: 18px;
}

.btn-grey {
    font-size: 18px;
}

span.error {
    color: #f00;
    padding-left: 25px;
    font-size: 13px;
}

.footer-menu li a {
    font-size: 18px;
    line-height: 32px;
}

.site-footer--top span {
    font-size: 17px;
}

.footer-col-5 a {
    font-size: 18px;
}

.section-search-form.margin-top {
    margin-top: -130px;
    position: relative;
}

.filters-container strong,
.filters-container span {
    display: block;
    font-size: 18px;
    font-weight: 500;
    line-height: 40px;
}

.filters-container .count-cars {
    display: flex;
    flex-basis: 86px;
    max-width: 84px;
    border-right: 0px;
    align-items: center;
}

.alert {
    padding: 10px 20px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.alert-success {
    background-color: #D4EEDA;
    color: #245725;
    border: 1px solid #C3E7CB;
}

.contains-links a {
    color: #CB3832;
}

.contains-links a:hover {
    color: #CB3832;
    text-decoration: underline;
}

.car-extras__item span.red-text,
.car-feature span.red-text {
    color: #CB3832;
}

.red-text {
    color: #CB3832;
}

.feature-box__image--height {
    height: 86px;
}

.posts-box__image-wrapper {
    margin-top: 40px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-services--small-icons .service-wrapper img {
    height: 25px;
}

.final-price-wrapper h1 {
    font-size: 26px;
    line-height: 31px;
    font-weight: bold;
    color: #231f20;
    margin-bottom: 10px;
}

.final-price-wrapper .rent-btn {
    font-size: 20px;
    font-weight: 500;
    background-color: var(--color-red);
    color: #fff;
    text-transform: uppercase;
    display: block;
    width: 100%;
    text-align: center;
    border-radius: 30px;
    border: 0;
    cursor: pointer;
}

.final-price-wrapper .rent-btn.btn-grey {
    background-color: var(--color-gray);
}

.final-price-wrapper .order-summary li {
    line-height: 1.3;
    padding: 13px 15px;
}

.consent-container {
    margin-bottom: 12px;
    font-size: 12px;
    font-weight: 400;
    color: #a8aaac;
}

.consent-container a {
    color: #000000;
}

.final-price-wrapper .order-summary .cost {
    flex-basis: 23%;
}

.client-form-wrapper .error {
    display: none;
}

.client-form-wrapper .error.error--visible {
    display: block;
}

.input-explain {
    font-size: 12px;
    margin-top: 2px;
}

#btn-back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    background: transparent;
    border: 0;
}

#btn-back-to-top:hover {
    cursor: pointer;
}

.ll-skin-nigran .ui-datepicker .ui-datepicker-next span {
    background-image: url('../packages/datepicker/images/ui-icons_222222_256x240.png');
    background-position: -32px -32px;
    margin-top: 0;
    top: 0;
    font-weight: bold;
}

.ll-skin-nigran .ui-datepicker .ui-datepicker-prev span {
    background-image: url('../packages/datepicker/images/ui-icons_222222_256x240.png');
    background-position: -96px -32px;
    margin-top: 0;
    top: 0;
    font-weight: bold;
}

.slick-track {
    display: flex !important;
}

.slick-slide {
    height: auto;
}

.car-description p {
    font-size: 17px;
    line-height: 1.6em;
    font-weight: 400;
    color: #555;
}


/**** Car List Item ****/

.car-list-item {
    display: flex;
    padding: 0;
    border: 1px solid #e3e3e4;
    justify-content: space-between;
    column-gap: 1rem;
}

.car-list-item:not(:last-of-type) {
    margin-bottom: 37px;
}

/* Car List Item Left Side */

.car-list-item--left {
    text-align: center;
    position: relative;
    flex-basis: 45%;
}

.car-list-item--left .badges {
    display: flex;
    justify-content: center;
    font-size: .5rem;
    gap: .4rem;
    color: #fff;
    align-items: center;
}

.car-list-item--left .badges .badge-item {
    display: flex;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: 20%;
    justify-content: center;
    column-gap: 2px;
}

.car-list-item--left .badges .badge-item img {
    width: 40px;
}

.car-list-item--left .badges .badge-item.offer {
    background: var(--background-orange);
}

.car-list-item--left .badges .badge-item.offer img{
    height: 30px;
}



.car-list-item--left .badges .badge-item.popular {
    background: var(--background-indigo);
}

.car-list-item--left .badges .badge-item.eco {
    background: var(--background-green);
}

.car-list-item--left .badges .badge-item img {
    padding: 0 0.3rem;
}

.car-list-item--left .badges .badge-item span {
    flex-shrink: 0;
}

/* Car List Item Right Side */

.car-list-item--right {
    flex-basis: 55%;
    max-width: unset;
    margin-left: unset;
}

.car-list-item--right .car-group {
    display: block;
    margin-bottom: 15px;
    margin-top: 0;
    font-size: 15px;
    font-weight: 400;
    line-height: 18px;
    color: #60585a;
}

.item-details {
    display: flex;
    height: unset;
    justify-content: space-evenly;
    padding: 1.5rem 0;
}

/* Item Details feft side */

.item-details--left {
    display: grid;
    grid-template-rows: 1.5rem 1.5rem auto;
    flex-direction: column;
    flex-basis: 60%;
    max-width: unset;
}

.item-details--left h3 {
    font-size: 30px;
    font-weight: 700;
    line-height: 36px;
    color: #231f20;
    margin-bottom: 15px;
}

/* Car Extras */
.car-extras {
    display: flex;
    justify-content: start;
    column-gap: 1rem;
    max-width: unset;
}

.car-extras__item {
    display: flex;
    align-items: center;
}

.car-extras__item:not(:last-of-type) {
    margin-right: 5px;
}

.car-extras__item img {
    margin-right: 8px;
    width: 2rem;
}

.car-extras__item span {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: initial;
}


/* Car Information */

.car-information {
    display: flex;
    margin-top: unset;
    max-width: 280px;
    width: 100%;
    align-items: start;
    justify-content: space-between;
}

.car-information ul {
    margin-left: 17px;
    list-style: none;
    margin: 0;
}

.car-information ul li {
    font-size: 16px;
    font-weight: 400;
    line-height: 34px;
    color: #60585a;
}

.car-information ul li:before {
    content: "✔";
    color: var(--color-red);
    background-color: var(--background-yellow);
    border-radius: 50%;
    padding: .2rem .4rem;
    margin-right: .5rem;
}

.car-information--left {
    margin-right: 10px;
}

/* Item details right side */
.item-details--right {
    flex-basis: 25%;
    max-width: unset;
    margin-left: unset;
    display: flex;
    flex-direction: column;
}



/* "From" indicator above price */
.item-details--right>.car-price-from {
    position: relative;
    text-transform: uppercase;
    color: #a7a9ac;
    margin-bottom: .5rem;
}

.item-details--right .car-price-container .car-price-from:before {
    content: unset;
    position: unset;
    font-size: unset;
    font-weight: unset;
    color: unset;
    top: unset;
    text-transform: unset;
    left: unset;
}

/* Car Price Container */
.item-details--right .car-price-container {
    display: block;
    align-items: flex-end;
    background-color: var(--background-yellow);
    color: var(--color-black);
    border-radius: 1rem;
    padding: 1rem;
    text-align: center;
}

.item-details--right .car-price-container .car-price {
    display: block;
    font-size: 4rem;
    line-height: 1.1;
    font-weight: 800;
    color: unset;
}

.item-details--right .car-price-container .car-price sup {
    font-size: 21px;
    font-weight: 800;
    top: -26px;
    left: 0;
}

.item-details--right .car-price-container .car-period {
    display: block;
    font-size: 1rem;
    line-height: 1;
    font-weight: 700;
    color: unset;
    position: relative;
    left: unset;
}

.item-details--right .car-price-container .car-period-tooltip {
    background-color: #edb417;
    border-radius: 25px 25px 25px 0;
    width: 140px;
    position: absolute;
    color: #fff;
    font-size: 9px;
    line-height: 11px;
    text-align: center;
    padding: 15px 10px;
    bottom: 12px;
    z-index: 1;
    left: 100%;
    visibility: hidden;
    opacity: 0;
    transition: all 400ms ease-in;
}


/* Reservation CTA Button */
.cta-button a {
    display: block;
    width: 100%;
    margin: 1.5rem 0 1rem;
}

/* Extra details list */
.item-extra-bottom ul li {
    line-height: 1;
    margin: .5rem 0;
    font-size: 1rem;
    color: unset;
}

.item-extra-bottom ul li::before {
    content: "";
    background-image: unset;
    display: none;
}



/*************************************/
/*********** Media queries ***********/
/*************************************/

/********* Tablets up to 1024px *********/

@media only screen and (max-width: 1024px) {

    /* Car List Item */
    .car-list-item {
        align-items: center;
    }

    .car-list-item--left {
        flex-basis: 55%;
    }

    /*** Item Details ***/
    .item-details {
        flex-direction: column;
        height: auto;
    }

    /*** Item Left Side ***/
    .car-list-item--left .badges .badge-item {
        flex-basis: 23%;
        column-gap: 0;
    }


    /*** Item Details Left Side ***/
    .item-details--left {
        flex-basis: unset;
        max-width: unset;
    }

    /*** Item Details Right Side ***/
    .item-details--right {
        flex-basis: unset;
        max-width: unset;
        margin-left: 0;
        flex-direction: column;
        align-items: center;
    }

    .car-extras {
        margin: 2rem 0 1rem;
    }

    .car-information {
        margin: 1rem 0;
    }
    

    .item-details--right .car-price-container .car-price-from:before {
        position: unset;
        display: block;
    }

    .item-extra-bottom {
        margin-left: unset;
    }
}

/********* Mobile up to 768px *********/

@media only screen and (max-width: 768px) {
    .final-price-wrapper h1 {
        font-size: 20px;
        line-height: 24px;
    }

    .car-list-item--right {
        flex-basis: unset;
        margin-left: 0;
        width: 100%;
        max-width: 534px;
        margin: 0 auto;
        padding: 10px 0px;
    }

    .section-search-form.margin-top {
        margin-top: 15px;
        position: relative;
    }

    /******* Car Listings ******/
 
    /**** Car List Item ****/
    .car-list-item {
        flex-direction: column;
        padding-right: 0px;
        padding-bottom: 0;
    }

    .car-list-item:not(:last-of-type) {
        margin-bottom: 35px;
    }



    /*** Car List Item Left Side ***/
    .car-list-item--left .badges .badge-item {
        flex-grow: 1;
    }

    /*** Car List Item Right Side ***/
    .car-list-item--right {
        flex-basis: unset;
        margin-left: 0;
        width: 100%;
        max-width: 534px;
        margin: 0 auto;
        padding: 0 15px;
    }

    .car-list-item--right .car-group {
        font-size: .75rem;
        line-height: 1;
        margin-top: 0px;
    }

    /* Item Details Left*/

    .item-details--left {
        margin-bottom: unset;
    }

    .item-details--left h3 {
        font-size: 2rem;
        line-height: 1;
        margin: unset;
        text-align: center;
    }

    .item-details--left .car-group {
        text-align: center;
        font-size: .75rem;
        margin-bottom: unset;
    }

    .item-details {
        padding: 1rem 0;
    }

    /* Care Extras */

    .car-extras {
        display: flex;
        flex-wrap: wrap;
        margin: 2rem auto 0.5rem;
        max-width: unset;
        justify-content: space-evenly;
        align-items: center;
        line-height: 1;
    }
    .car-extras__item:not(:last-of-type) {
        margin: 0;
    }
    .car-extras__item img {
        margin: 0;
    }

    .car-extras__item {
        margin: unset;
        gap: .4rem;
    }

    .car-extras__item span {
        font-size: 1rem;
    }

    /* Car Information */

    .car-information {
        align-items: flex-start;
        justify-content: space-evenly;
        max-width: unset;
        margin-bottom: .3rem;

    }

    .car-information--left {
        margin-right: unset;
    }

    .car-information ul li {
        font-size: 14px;
        line-height: 27px;
    }

    /*** Item Details Right Side ***/
    .item-details--right {
        flex-direction: column;
        align-items: center;
    }

    .item-details--right .car-price-container {
        flex-direction: column;
        align-items: baseline;
    }

    .item-details--right .car-price-container .car-price {
        font-size: 2.7rem;
        padding: unset;
    }

    .item-details--right .car-price-container .car-price sup {
        font-size: 20px;
    }

    .cta-button {
        display: block;
        margin: auto;

        max-width: 100%;
    }

    .cta-button a {
        padding: 1.5rem 4rem;
        font-size: 1.7rem;
        margin: 1rem 0;
    }

    .item-extra-bottom {
        margin: auto;
    }

    .item-extra-bottom ul {
        margin: 1rem 0;
    }

    .item-extra-bottom ul li {
        margin-bottom: 1.2rem;
    }

}

/* Mobile up to 580px */
@media only screen and (max-width: 580px) {
    .flex-input-wrapper__mob-col {
        flex-direction: column;
    }

    .flex-input-wrapper__mob-col .button--winona {
        margin-top: 20px;
    }
}

/********* Desktop Range *********/

@media only screen and (max-width: 1366px) {
    .item-details--right .car-price-container .car-period-tooltip {
        border-radius: 25px 25px 0 25px;
        right: -18px;
        left: unset;
    }
}