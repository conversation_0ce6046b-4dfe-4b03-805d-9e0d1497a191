/*--------------------------------------------------------------------------
	1. Grid System
--------------------------------------------------------------------------*/
/* Container */
.container {
	width: 960px;
	margin: 0 auto;
  /* Columns */;
}

.container .column,
.container .columns {
	float: left;
	display: inline;
	padding-left: 20px;
	padding-right: 20px;
	position: relative;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.container .column.nest,
.container .columns.nest,
.container .column.nested,
.container .columns.nested {
	padding-left: 0px;
	padding-right: 0px;
}

.container .column.nest10,
.container .columns.nest10,
.container .column.nested10,
.container .columns.nested10 {
	padding-left: 10px;
	padding-right: 10px;
}


.desktop-1 {
	width: 80px;
}

.desktop-2 {
	width: 160px;
}

.desktop-3 {
	width: 240px;
}

.desktop-4 {
	width: 320px;
}

.desktop-5 {
	width: 400px;
}

.desktop-6 {
	width: 480px;
}

.desktop-7 {
	width: 560px;
}

.desktop-8 {
	width: 640px;
}

.desktop-9 {
	width: 720px;
}

.desktop-10 {
	width: 800px;
}

.desktop-11 {
	width: 880px;
}

.desktop-12 {
	width: 960px;
}

.offset-1 {
	margin-left: 80px;
}

.offset-2 {
	margin-left: 160px;
}

.offset-3 {
	margin-left: 240px;
}

.offset-4 {
	margin-left: 320px;
}

.offset-5 {
	margin-left: 400px;
}

.offset-6 {
	margin-left: 480px;
}

.offset-7 {
	margin-left: 560px;
}

.offset-8 {
	margin-left: 640px;
}

.offset-9 {
	margin-left: 720px;
}

.offset-10 {
	margin-left: 800px;
}

.offset-11 {
	margin-left: 880px;
}



/* Widescreen */
@media screen and (min-width: 1300px) {
	form .column_half {
        width:50%!important; 
        float:left;
     -webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
     -moz-box-sizing: border-box;    /* Firefox, other Gecko */
     box-sizing: border-box;         /* Opera/IE 8+ */
    }

   form .column_half  + .column_half {
    padding-left:20px;

    }
    
	.container {
		width: 1200px;
	}

	.desktop-1 {
		width: 100px;
	}

	.desktop-2 {
		width: 200px;
	}

	.desktop-3 {
		width: 300px;
	}

	.desktop-4 {
		width: 400px;
	}

	.desktop-5 {
		width: 500px;
	}

	.desktop-6 {
		width: 600px;
	}

	.desktop-7 {
		width: 700px;
	}

	.desktop-8 {
		width: 800px;
	}

	.desktop-9 {
		width: 900px;
	}

	.desktop-10 {
		width: 1000px;
	}

	.desktop-11 {
		width: 1100px;
	}

	.desktop-12 {
		width: 1200px;
	}

	.offset-1 {
		margin-left: 100px;
	}

	.offset-2 {
		margin-left: 200px;
	}

	.offset-3 {
		margin-left: 300px;
	}

	.offset-4 {
		margin-left: 400px;
	}

	.offset-5 {
		margin-left: 500px;
	}

	.offset-6 {
		margin-left: 600px;
	}

	.offset-7 {
		margin-left: 700px;
	}

	.offset-8 {
		margin-left: 800px;
	}

	.offset-9 {
		margin-left: 900px;
	}

	.offset-10 {
		margin-left: 1000px;
	}

	.offset-11 {
		margin-left: 1100px;
	}
}


/* Widescreen extra */
@media screen and (min-width: 1300px) {
 
    
    .container {
        width: 1200px;
    }

    .wide-1 {
        width: 100px;
    }

    .wide-2 {
        width: 200px;
    }

    .wide-3 {
        width: 300px;
    }

    .wide-4 {
        width: 400px;
    }

    .wide-5 {
        width: 500px;
    }

    .wide-6 {
        width: 600px;
    }

    .wide-7 {
        width: 700px;
    }

    .wide-8 {
        width: 800px;
    }

    .wide-9 {
        width: 900px;
    }

    .wide-10 {
        width: 1000px;
    }

    .wide-11 {
        width: 1100px;
    }

    .wide-12 {
        width: 1200px;
    }

    .offset-wide-1 {
        margin-left: 100px;
    }

    .offset-wide-2 {
        margin-left: 200px;
    }

    .offset-wide-3 {
        margin-left: 300px;
    }

    .offset-wide-4 {
        margin-left: 400px;
    }

    .offset-wide-5 {
        margin-left: 500px;
    }

    .offset-wide-6 {
        margin-left: 600px;
    }

    .offset-wide-7 {
        margin-left: 700px;
    }

    .offset-wide-8 {
        margin-left: 800px;
    }

    .offset-wide-9 {
        margin-left: 900px;
    }

    .offset-wide-10 {
        margin-left: 1000px;
    }

    .offset-wide-11 {
        margin-left: 1100px;
    }
}


/* Tablet */
@media screen and (min-width: 768px) and (max-width: 980px) {
	
	.container .column.nested10,
	.container .columns.nested10 {
	 
	padding-left: 5px;
	padding-right: 5px;
	}
	.container {
		width: 660px;
	}

	.desktop-1 {
		width: 55px;
	}

	.desktop-2 {
		width: 110px;
	}

	.desktop-3 {
		width: 165px;
	}

	.desktop-4 {
		width: 220px;
	}

	.desktop-5 {
		width: 275px;
	}

	.desktop-6 {
		width: 330px;
	}

	.desktop-7 {
		width: 385px;
	}

	.desktop-8 {
		width: 440px;
	}

	.desktop-9 {
		width: 495px;
	}

	.desktop-10 {
		width: 550px;
	}

	.desktop-11 {
		width: 605px;
	}

	.desktop-12 {
		width: 660px;
	}

	.tablet-1 {
		width: 55px;
	}

	.tablet-2 {
		width: 110px;
	}

	.tablet-3 {
		width: 165px;
	}

	.tablet-4 {
		width: 220px;
	}

	.tablet-5 {
		width: 275px;
	}

	.tablet-6 {
		width: 330px;
	}

	.tablet-7 {
		width: 385px;
	}

	.tablet-8 {
		width: 440px;
	}

	.tablet-9 {
		width: 495px;
	}

	.tablet-10 {
		width: 550px;
	}

	.tablet-11 {
		width: 605px;
	}

	.tablet-12 {
		width: 660px;
	}

	.offset-1 {
		margin-left: 55px;
	}

	.offset-2 {
		margin-left: 110px;
	}

	.offset-3 {
		margin-left: 165px;
	}

	.offset-4 {
		margin-left: 220px;
	}

	.offset-5 {
		margin-left: 275px;
	}

	.offset-6 {
		margin-left: 330px;
	}

	.offset-7 {
		margin-left: 385px;
	}

	.offset-8 {
		margin-left: 440px;
	}

	.offset-9 {
		margin-left: 495px;
	}

	.offset-10 {
		margin-left: 550px;
	}

	.offset-11 {
		margin-left: 605px;
	}

	.tablet-offset-0 {
		margin-left: 0px;
	}

	.tablet-offset-1 {
		margin-left: 55px;
	}

	.tablet-offset-2 {
		margin-left: 110px;
	}

	.tablet-offset-3 {
		margin-left: 165px;
	}

	.tablet-offset-4 {
		margin-left: 220px;
	}

	.tablet-offset-5 {
		margin-left: 275px;
	}

	.tablet-offset-6 {
		margin-left: 330px;
	}

	.tablet-offset-7 {
		margin-left: 385px;
	}

	.tablet-offset-8 {
		margin-left: 440px;
	}

	.tablet-offset-9 {
		margin-left: 495px;
	}

	.tablet-offset-10 {
		margin-left: 550px;
	}

	.tablet-offset-11 {
		margin-left: 605px;
	}
}
/* Mobile - Portrait */
@media screen and (max-width: 767px) {
	
	.phone-padding { padding:0 5px;}
	
	.column_half {
		width:50%!important; float:left;
	  -webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
	  -moz-box-sizing: border-box;    /* Firefox, other Gecko */
	  box-sizing: border-box;         /* Opera/IE 8+ */
	}
	.phone-centered { text-align:center!important;}
	.container {
		width: 100%;
	}

	.container .column,
	.container .columns {
		padding-left: 20px;
		padding-right: 20px;
		  -webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
	  -moz-box-sizing: border-box;    /* Firefox, other Gecko */
	  box-sizing: border-box;         /* Opera/IE 8+ */
	}

	.desktop-1,
	.tablet-1 {
		width: 100%;
	}

	.desktop-2,
	.tablet-2 {
		width: 100%;
	}

	.desktop-3,
	.tablet-3 {
		width: 100%;
	}

	.desktop-4,
	.tablet-4 {
		width: 100%;
	}

	.desktop-5,
	.tablet-5 {
		width: 100%;
	}

	.desktop-6,
	.tablet-6 {
		width: 100%;
	}

	.desktop-7,
	.tablet-7 {
		width: 100%;
	}

	.desktop-8,
	.tablet-8 {
		width: 100%;
	}

	.desktop-9,
	.tablet-9 {
		width: 100%;
	}

	.desktop-10,
	.tablet-10 {
		width: 100%;
	}

	.desktop-11,
	.tablet-11 {
		width: 100%;
	}

	.desktop-12,
	.tablet-12 {
		width: 100%;
	}

	.mobile-1 {
		width: 100px;
	}

	.mobile-2 {
		width: 100px;
	}

	.offset-1 {
		margin-left: 0;
	}

	.offset-2 {
		margin-left: 0;
	}

	.offset-3 {
		margin-left: 0;
	}

	.offset-4 {
		margin-left: 0;
	}

	.offset-5 {
		margin-left: 0;
	}

	.offset-6 {
		margin-left: 0;
	}

	.offset-7 {
		margin-left: 0;
	}

	.offset-8 {
		margin-left: 0;
	}

	.offset-9 {
		margin-left: 0;
	}

	.offset-10 {
		margin-left: 0;
	}

	.offset-11 {
		margin-left: 0;
	}
}
/* Mobile - Landscape */
@media screen and (min-width: 480px) and (max-width: 767px) {
	.container {
		width: 400px;
	}

	.desktop-1,
	.tablet-1 {
		width: 400px;
	}

	.desktop-2,
	.tablet-2 {
		width: 400px;
	}

	.desktop-3,
	.tablet-3 {
		width: 400px;
	}

	.desktop-4,
	.tablet-4 {
		width: 400px;
	}

	.desktop-5,
	.tablet-5 {
		width: 400px;
	}

	.desktop-6,
	.tablet-6 {
		width: 400px;
	}

	.desktop-7,
	.tablet-7 {
		width: 400px;
	}

	.desktop-8,
	.tablet-8 {
		width: 400px;
	}

	.desktop-9,
	.tablet-9 {
		width: 400px;
	}

	.desktop-10,
	.tablet-10 {
		width: 400px;
	}

	.desktop-11,
	.tablet-11 {
		width: 400px;
	}

	.desktop-12,
	.tablet-12 {
		width: 400px;
	}
}
	.show-phone {
		display: none ;
	}
@media screen and (min-width: 1300px) {
	.padding-desktop-10 {padding:10px;}
	.margin-top-desktop {margin-top: 1em;}
}

@media screen and (min-width: 981px) {
	.hide-desktop { display:none !important }

}

@media screen and (max-width: 959px) {
	.hide-tablet {
		display: none !important;
	}
}

@media screen and (min-width: 768px) and (max-width: 959px) {
	.clear-tablet {clear:both;; }
	.margin-top-tablet{margin-top:2em}
}

@media screen and (max-width: 767px) {
	.hide-phone {
		display: none !important;
	}
	.clear-tablet {clear:both; }
	.margin-top-tablet{margin-top:2em}
}

@media screen and (min-width: 480px) and (max-width: 767px) {
	
	.show-phone {
		display: inline-block !important;
	}
	
}

@media screen and (max-width: 479px) {
	
	.show-phone {
		display: inline-block !important;
	}
}


html, body {
  height: 100%;
}
#container {
  min-height: 100%;
  /* equal to footer height */
  margin-bottom: -130px; 
}
#container:after {
  content: "";
  display: block;
}
 

/* #Clearing
================================================== */
/* Self Clearing Goodness */
.container:after {
	content: "\0020";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
/* Use clearfix class on parent to clear nested columns,
or wrap each row of columns in a <div class="row"> */
.clearfix:before,
.clearfix:after,
.row:before,
.row:after {
	content: '\0020';
	display: block;
	overflow: hidden;
	visibility: hidden;
	width: 0;
	height: 0;
}

.row:after,
.clearfix:after {
	clear: both;
}

.row,
.clearfix {
	zoom: 1;
}
/* You can also use a <br class="clear" /> to clear columns */
.clear {
	clear: both;
	display: block;
	overflow: hidden;
	visibility: hidden;
	width: 0;
	height: 0;
}

.responsive-img {
	width: 100%;
	height: auto;
}
.img-border {
	border: 2px solid #eee;
}
.text-center{
	text-align: center;
}
.custom-box {
	background: #fff;
	border: 1px solid #e7e7e7;
	padding: 30px 10px 10px 10px;
	min-height: 160px;
	box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.06);
}