{"version": 3, "file": "lang/summernote-he-IL.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,MAFJ;AAGJC,QAAAA,SAAS,EAAE,UAHP;AAIJC,QAAAA,KAAK,EAAE,WAJH;AAKJC,QAAAA,MAAM,EAAE,MALJ;AAMJC,QAAAA,IAAI,EAAE,MANF;AAOJC,QAAAA,aAAa,EAAE,SAPX;AAQJC,QAAAA,SAAS,EAAE,UARP;AASJC,QAAAA,WAAW,EAAE,UATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,YAFH;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,aAAa,EAAE,aALV;AAMLC,QAAAA,SAAS,EAAE,aANN;AAOLC,QAAAA,UAAU,EAAE,aAPP;AAQLC,QAAAA,SAAS,EAAE,KARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,iBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,gBAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,aAlBA;AAmBLC,QAAAA,MAAM,EAAE,WAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,cAFN;AAGLpB,QAAAA,MAAM,EAAE,YAHH;AAILgB,QAAAA,GAAG,EAAE,cAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,OADF;AAEJtB,QAAAA,MAAM,EAAE,YAFJ;AAGJuB,QAAAA,MAAM,EAAE,WAHJ;AAIJC,QAAAA,IAAI,EAAE,MAJF;AAKJC,QAAAA,aAAa,EAAE,YALX;AAMJT,QAAAA,GAAG,EAAE,OAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,WAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,SALC;AAMLC,QAAAA,EAAE,EAAE,SANC;AAOLC,QAAAA,EAAE,EAAE,SAPC;AAQLC,QAAAA,EAAE,EAAE,SARC;AASLC,QAAAA,EAAE,EAAE,SATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,eADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,UAAU,EAAE,SAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,MADF;AAETC,QAAAA,OAAO,EAAE,YAFA;AAGTC,QAAAA,MAAM,EAAE,YAHC;AAITC,QAAAA,IAAI,EAAE,aAJG;AAKTC,QAAAA,MAAM,EAAE,aALC;AAMTC,QAAAA,KAAK,EAAE,aANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,WAFD;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,WAAW,EAAE,MALR;AAMLC,QAAAA,cAAc,EAAE,WANX;AAOLC,QAAAA,KAAK,EAAE,OAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,cADH;AAERC,QAAAA,KAAK,EAAE,MAFC;AAGRC,QAAAA,cAAc,EAAE,aAHR;AAIRC,QAAAA,MAAM,EAAE,OAJA;AAKRC,QAAAA,mBAAmB,EAAE,cALb;AAMRC,QAAAA,aAAa,EAAE,aANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,WADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-he-IL.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'he-IL': {\n      font: {\n        bold: 'מודגש',\n        italic: 'נטוי',\n        underline: 'קו תחתון',\n        clear: 'נקה עיצוב',\n        height: 'גובה',\n        name: 'ג<PERSON><PERSON><PERSON>',\n        strikethrough: 'קו חוצה',\n        subscript: 'כתב תחתי',\n        superscript: 'כתב עילי',\n        size: 'גודל גופן',\n      },\n      image: {\n        image: 'תמונה',\n        insert: 'הוסף תמונה',\n        resizeFull: 'גודל מלא',\n        resizeHalf: 'להקטין לחצי',\n        resizeQuarter: 'להקטין לרבע',\n        floatLeft: 'יישור לשמאל',\n        floatRight: 'יישור לימין',\n        floatNone: 'ישר',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'גרור תמונה לכאן',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'בחר מתוך קבצים',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'נתיב לתמונה',\n        remove: 'הסר תמונה',\n        original: 'Original',\n      },\n      video: {\n        video: 'סרטון',\n        videoLink: 'קישור לסרטון',\n        insert: 'הוסף סרטון',\n        url: 'קישור לסרטון',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion או Youku)',\n      },\n      link: {\n        link: 'קישור',\n        insert: 'הוסף קישור',\n        unlink: 'הסר קישור',\n        edit: 'ערוך',\n        textToDisplay: 'טקסט להציג',\n        url: 'קישור',\n        openInNewWindow: 'פתח בחלון חדש',\n      },\n      table: {\n        table: 'טבלה',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'הוסף קו',\n      },\n      style: {\n        style: 'עיצוב',\n        p: 'טקסט רגיל',\n        blockquote: 'ציטוט',\n        pre: 'קוד',\n        h1: 'כותרת 1',\n        h2: 'כותרת 2',\n        h3: 'כותרת 3',\n        h4: 'כותרת 4',\n        h5: 'כותרת 5',\n        h6: 'כותרת 6',\n      },\n      lists: {\n        unordered: 'רשימת תבליטים',\n        ordered: 'רשימה ממוספרת',\n      },\n      options: {\n        help: 'עזרה',\n        fullscreen: 'מסך מלא',\n        codeview: 'תצוגת קוד',\n      },\n      paragraph: {\n        paragraph: 'פסקה',\n        outdent: 'הקטן כניסה',\n        indent: 'הגדל כניסה',\n        left: 'יישור לשמאל',\n        center: 'יישור למרכז',\n        right: 'יישור לימין',\n        justify: 'מיושר',\n      },\n      color: {\n        recent: 'צבע טקסט אחרון',\n        more: 'עוד צבעים',\n        background: 'צבע רקע',\n        foreground: 'צבע טקסט',\n        transparent: 'שקוף',\n        setTransparent: 'קבע כשקוף',\n        reset: 'איפוס',\n        resetToDefault: 'אפס לברירת מחדל',\n      },\n      shortcut: {\n        shortcuts: 'קיצורי מקלדת',\n        close: 'סגור',\n        textFormatting: 'עיצוב הטקסט',\n        action: 'פעולה',\n        paragraphFormatting: 'סגנונות פסקה',\n        documentStyle: 'עיצוב המסמך',\n        extraKeys: 'קיצורים נוספים',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'בטל פעולה',\n        redo: 'בצע שוב',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}