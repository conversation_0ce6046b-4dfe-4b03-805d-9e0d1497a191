<?php

namespace Tests\Feature\Hodor;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\User;
use App\Reservation;
use Carbon\Carbon;

class CustomerRetentionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    /**
     * Test that the customer retention route exists and is accessible.
     */
    public function test_customer_retention_route_exists(): void
    {
        $response = $this->get(route('hodor.analytics.customer.retention'));
        
        $response->assertStatus(200);
        $response->assertViewIs('hodor::analytics.customer_retention');
    }

    /**
     * Test that the customer retention page loads with year parameter.
     */
    public function test_customer_retention_page_loads_with_year_parameter(): void
    {
        $response = $this->get(route('hodor.analytics.customer.retention', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', '2023');
        $response->assertViewHas('analytics_data');
    }

    /**
     * Test that page title includes the selected year.
     */
    public function test_page_title_includes_year(): void
    {
        $response = $this->get(route('hodor.analytics.customer.retention', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        $response->assertViewHas('page_title');
        
        $pageTitle = $response->viewData('page_title');
        $this->assertStringContainsString('2023', $pageTitle);
        $this->assertStringContainsString('Customer Retention', $pageTitle);
    }

    /**
     * Test customer retention calculations with test data.
     */
    public function test_customer_retention_calculations_with_test_data(): void
    {
        // Create customers from previous year (2022)
        Reservation::factory()->create([
            'customer_id' => 1,
            'pickup_date' => '2022-06-15',
            'final_price' => 500,
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'customer_id' => 2,
            'pickup_date' => '2022-08-20',
            'final_price' => 600,
            'show' => 'show'
        ]);

        // Create reservations for 2023 - some returning, some new customers
        Reservation::factory()->create([
            'customer_id' => 1, // Returning customer
            'pickup_date' => '2023-03-15',
            'final_price' => 800,
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'customer_id' => 3, // New customer
            'pickup_date' => '2023-05-20',
            'final_price' => 700,
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'customer_id' => 4, // New customer
            'pickup_date' => '2023-07-10',
            'final_price' => 900,
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.customer.retention', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check customer counts
        $this->assertEquals(3, $analyticsData['customer_counts']['total']); // 3 unique customers in 2023
        $this->assertEquals(2, $analyticsData['customer_counts']['new']); // Customers 3 and 4
        $this->assertEquals(1, $analyticsData['customer_counts']['returning']); // Customer 1
        $this->assertEquals(50, $analyticsData['customer_counts']['retention_rate']); // 1 out of 2 previous customers returned
        
        // Check revenue split
        $this->assertEquals(800, $analyticsData['revenue']['returning']); // Customer 1's revenue
        $this->assertEquals(1600, $analyticsData['revenue']['new']); // Customers 3 and 4's revenue
    }

    /**
     * Test that no-show reservations are excluded from calculations.
     */
    public function test_customer_retention_excludes_no_show_reservations(): void
    {
        // Create previous year customer
        Reservation::factory()->create([
            'customer_id' => 1,
            'pickup_date' => '2022-06-15',
            'final_price' => 500,
            'show' => 'show'
        ]);

        // Create show reservation for 2023
        Reservation::factory()->create([
            'customer_id' => 1,
            'pickup_date' => '2023-03-15',
            'final_price' => 400,
            'show' => 'show'
        ]);
        
        // Create no-show reservation for 2023
        Reservation::factory()->create([
            'customer_id' => 2,
            'pickup_date' => '2023-05-20',
            'final_price' => 600,
            'show' => 'no-show'
        ]);

        $response = $this->get(route('hodor.analytics.customer.retention', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Only the 'show' reservation should be counted
        $this->assertEquals(1, $analyticsData['customer_counts']['total']);
        $this->assertEquals(0, $analyticsData['customer_counts']['new']);
        $this->assertEquals(1, $analyticsData['customer_counts']['returning']);
        $this->assertEquals(400, $analyticsData['revenue']['returning']);
        $this->assertEquals(0, $analyticsData['revenue']['new']);
    }

    /**
     * Test monthly retention data calculation.
     */
    public function test_monthly_retention_data_calculation(): void
    {
        // Create previous year customers
        Reservation::factory()->create([
            'customer_id' => 1,
            'pickup_date' => '2022-06-15',
            'final_price' => 500,
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'customer_id' => 2,
            'pickup_date' => '2022-08-20',
            'final_price' => 600,
            'show' => 'show'
        ]);

        // Create January 2023 reservations
        Reservation::factory()->create([
            'customer_id' => 1, // Returning
            'pickup_date' => '2023-01-15',
            'final_price' => 400,
            'show' => 'show'
        ]);
        
        Reservation::factory()->create([
            'customer_id' => 3, // New
            'pickup_date' => '2023-01-20',
            'final_price' => 500,
            'show' => 'show'
        ]);

        // Create March 2023 reservations
        Reservation::factory()->create([
            'customer_id' => 2, // Returning
            'pickup_date' => '2023-03-10',
            'final_price' => 600,
            'show' => 'show'
        ]);

        $response = $this->get(route('hodor.analytics.customer.retention', ['analytics_year' => '2023']));
        
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check January data
        $this->assertEquals(2, $analyticsData['monthly']['01']['total']);
        $this->assertEquals(1, $analyticsData['monthly']['01']['new']);
        $this->assertEquals(1, $analyticsData['monthly']['01']['returning']);
        $this->assertEquals(50, $analyticsData['monthly']['01']['retention_rate']);
        
        // Check March data
        $this->assertEquals(1, $analyticsData['monthly']['03']['total']);
        $this->assertEquals(0, $analyticsData['monthly']['03']['new']);
        $this->assertEquals(1, $analyticsData['monthly']['03']['returning']);
        $this->assertEquals(50, $analyticsData['monthly']['03']['retention_rate']);
        
        // Check February data (should be empty)
        $this->assertEquals(0, $analyticsData['monthly']['02']['total']);
        $this->assertEquals(0, $analyticsData['monthly']['02']['new']);
        $this->assertEquals(0, $analyticsData['monthly']['02']['returning']);
        $this->assertEquals(0, $analyticsData['monthly']['02']['retention_rate']);
    }

    /**
     * Test that invalid year parameters are handled gracefully.
     */
    public function test_invalid_year_parameters_handled_gracefully(): void
    {
        // Test with invalid year
        $response = $this->get(route('hodor.analytics.customer.retention', ['analytics_year' => 'invalid']));
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', date('Y'));

        // Test with future year
        $futureYear = date('Y') + 10;
        $response = $this->get(route('hodor.analytics.customer.retention', ['analytics_year' => $futureYear]));
        $response->assertStatus(200);
        $response->assertViewHas('analytics_year', date('Y'));
    }

    /**
     * Test that analytics data structure is correct.
     */
    public function test_analytics_data_structure_is_correct(): void
    {
        $response = $this->get(route('hodor.analytics.customer.retention'));
        $response->assertStatus(200);
        
        $analyticsData = $response->viewData('analytics_data');
        
        // Check that required data structure exists
        $this->assertArrayHasKey('customer_counts', $analyticsData);
        $this->assertArrayHasKey('revenue', $analyticsData);
        $this->assertArrayHasKey('monthly', $analyticsData);
        
        // Check customer_counts structure
        $this->assertArrayHasKey('total', $analyticsData['customer_counts']);
        $this->assertArrayHasKey('new', $analyticsData['customer_counts']);
        $this->assertArrayHasKey('returning', $analyticsData['customer_counts']);
        $this->assertArrayHasKey('retention_rate', $analyticsData['customer_counts']);
        
        // Check revenue structure
        $this->assertArrayHasKey('returning', $analyticsData['revenue']);
        $this->assertArrayHasKey('new', $analyticsData['revenue']);
        
        // Check monthly structure
        $this->assertArrayHasKey('01', $analyticsData['monthly']);
        $this->assertArrayHasKey('12', $analyticsData['monthly']);
    }
}
