<?php

namespace Tests\Unit\Hodor;

use Tests\TestCase;

class BudgetPerDayCalculationTest extends TestCase
{
    /**
     * Test that the average price per day calculation is correct.
     */
    public function test_average_price_per_day_calculation_is_correct(): void
    {
        // Test data
        $revenue = 1800;
        $totalDays = 14;

        // Calculate average price per day
        $avgPricePerDay = $revenue / $totalDays;

        // Assert that the calculation is correct
        $this->assertEquals(128.57142857142858, $avgPricePerDay);
    }
}
