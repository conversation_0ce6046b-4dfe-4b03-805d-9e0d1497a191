<?php
/**
 * Created by PhpStorm.
 * User: bmenekl
 * Date: 5/27/15
 * Time: 16:17
 */

use App\Services\Offer\CouponDiscount;
use Illuminate\Database\Eloquent\Collection;

class CouponDiscountTest extends TestCase {

    public function setUp()
    {
        parent::setUp();

        $this->mockBasicDiscount = Mockery::mock('App\Services\Offer\BasicDiscount');

        $this->mockCouponRepo = Mockery::mock('Eloquent', 'Coupon');
    }

    public function testInvalidCouponDiscountIsZero()
    {
        $this->mockBasicDiscount->shouldReceive('getDiscount')
            ->once()
            ->andReturn(0.5);

        $this->mockCouponRepo->shouldReceive('findBy')
            ->once()
            ->with('text', 'foo')
            ->andReturn(new Collection());

        $couponDiscount = new CouponDiscount($this->mockBasicDiscount, $this->mockCouponRepo, 'foo');

        $this->assertEquals(0.5, $couponDiscount->getDiscount());
    }
}