<?php

namespace Database\Seeders;

use App\GoogleReview;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class GoogleReviewsTextSeeder extends Seeder
{

    protected $data = array();
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        GoogleReview::chunk(300, function ($reviews)
        {
            foreach ($reviews as $review)
            {
                // proceed on the condition that the translated_text field is empty
                if( empty($review->translated_text) )
                {
                    // translated text comes first
                    $curated_text = Str::before($review->review_text, '(Original');
                    // then we strip the '(Translated Text)' string
                    $curated_text = Str::remove('(Translated by Google)', $curated_text);

                    $review->translated_text = $curated_text;
                    $review->save();
                }
            }
        });
    }


}
