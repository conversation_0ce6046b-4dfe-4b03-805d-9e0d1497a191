<?php

namespace Database\Seeders;

use App\Category;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Seeder;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $featuredCategories = ['featured' => 'The default category for rentcars-crete featured listings', 'featured_cretan' => 'The default category for cretanrentals featured listings'];
        foreach (array_keys($featuredCategories) as $featuredCategory) {
            $fcat = Category::where(['name' => $featuredCategory])->first();
            if (!$fcat) {
                DB::table('categories')->insert([
                    "name" => $featuredCategory,
                    "description" => $featuredCategories[$featuredCategory],
                    "locked" => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            } else {
                $fcat->name = $featuredCategory;
                $fcat->description = $featuredCategories[$featuredCategory];
                $fcat->locked = true;
                $fcat->save();
            }

        }
        $this->command->info('Categories table seeded!');
    }
}
