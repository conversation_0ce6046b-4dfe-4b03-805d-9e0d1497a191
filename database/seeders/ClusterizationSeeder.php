<?php

namespace Database\Seeders;

use DB;
use Illuminate\Database\Seeder;

class ClusterizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // seed 'site' table
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('site')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        DB::table('site')->insert([
            'name' => 'eurodollar'
        ]);

        DB::table('site')->insert([
            'name' => 'cretanrentals'
        ]);

        // seed 'site' field in customers,reservations,returning customers, coupons tables (default to 1->eurodollar)
        $tables = [
            'customers',
            'coupons',
            'reservations',
            'repeating_clients'
        ];

        foreach ($tables as $table) {
            DB::table($table)->update(array('site' => 1));
        }
    }
}
