<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOffersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('offers', function($table)
        {
            $table->increments('id');
            $table->integer('customer_id')->unsigned()->nullable();
            $table->date('pickup_date');
            $table->date('dropoff_date');
            $table->time('pickup_time');
            $table->time('dropoff_time');
            $table->integer('pickup_location')->unsigned();
            $table->integer('dropoff_location')->unsigned();
            $table->integer('listing_id');
            $table->text('comment');
            $table->integer('offered_price');
            $table->string('slug');
            $table->boolean('one_off')->default(true);
            $table->boolean('enabled')->default(true);
            $table->timestamps();

            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('offers');
    }
}
