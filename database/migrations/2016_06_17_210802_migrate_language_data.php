<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MigrateLanguageData extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
    	// Get records from listings.
    	$resultsL = DB::table('listings')->select(array('id', 'description'))->get();
    	// Loop through the results and insert into listings_translations table
    	foreach($resultsL as $result)
    	{
    		DB::table('listings_translations')->insert([
    				"locale"    =>  'en',
    				"listing_id"    =>  $result->id,
    				"description"    =>  $result->description,
    				]);
    	}
    	
    	// Get records from accessories.
    	$resultsA = DB::table('accessories')->select(array('id', 'name', 'description'))->get();
    	// Loop through the results and insert into accessories_translations table
    	foreach($resultsA as $result)
    	{
    		DB::table('accessories_translations')->insert([
    				"locale"    =>  'en',
    				"accessory_id"    =>  $result->id,
    				"name"    =>  $result->name,
    				"description"    =>  $result->description,
    				]);
    	}
    	
    	// Get records from groups.
    	$resultsA = DB::table('groups')->select(array('id', 'description'))->get();
    	// Loop through the results and insert into groups_translations table
    	foreach($resultsA as $result)
    	{
    		DB::table('groups_translations')->insert([
    				"locale"    =>  'en',
    				"group_id"    =>  $result->id,
    				"description"    =>  $result->description,
    				]);
    	}
    	
    	// Get records from locations.
    	$resultsA = DB::table('locations')->select(array('id', 'name'))->get();
    	// Loop through the results and insert into locations_translations table
    	foreach($resultsA as $result)
    	{
    		DB::table('locations_translations')->insert([
    				"locale"    =>  'en',
    				"location_id"    =>  $result->id,
    				"name"    =>  $result->name,
    				]);
    	}
    	
    	// Add records for languages.
    	DB::table('languages_translations')->insert([
    			"locale"    			=> 'en',
    			"language_id"			=> 1,
    			"text"					=> "English",
    			]);
    	DB::table('languages_translations')->insert([
    			"locale"    			=> 'en',
    			"language_id"			=> 2,
    			"text"					=> "Greek",
    			]);
    	// Add records for languages.
    	DB::table('languages_translations')->insert([
    			"locale"    			=> 'el',
    			"language_id"			=> 1,
    			"text"					=> "Αγγλικά",
    			]);
    	DB::table('languages_translations')->insert([
    			"locale"    			=> 'el',
    			"language_id"			=> 2,
    			"text"					=> "Ελληνικά",
    			]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
