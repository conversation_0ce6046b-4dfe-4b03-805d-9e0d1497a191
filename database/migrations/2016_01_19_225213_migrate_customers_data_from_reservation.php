<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class MigrateCustomersDataFromReservation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
    	// Get records from reservations.
    	$results = DB::table('reservations')->select(array('id', 'customer_name', 'customer_email', 'customer_address', 'customer_country', 'customer_telephone', 'updated_at'))->get();
    	
    	// Loop through the results and insert into customers table
    	// and update the customer_id column in the reservations table
    	foreach($results as $result)
    	{
    		$id = DB::table('customers')->insertGetId([
    				"name"    =>  $result->customer_name,
    				"email"    =>  $result->customer_email,
    				"address"    =>  $result->customer_address,
    				"country"    =>  $result->customer_country,
    				"telephone"    =>  $result->customer_telephone,
    				"created_at"    =>  $result->updated_at,
    				"updated_at"    =>  $result->updated_at,
    				]);
    		DB::table('reservations')
    		->where('id', $result->id)
    		->update(array('customer_id' => $id));
    	}
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    	DB::table('reservations')
    	->update(array('customer_id' => null));
    }
}
