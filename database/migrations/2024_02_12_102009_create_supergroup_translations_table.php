<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('supergroup_translations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('supergroup_id');
            $table->string('locale')->index();

            $table->string('title');
            $table->string('page_heading')->nullable();
            $table->string('page_subheading')->nullable();
            $table->text('description')->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->timestamps();

            $table->unique(['supergroup_id', 'locale']);

            $table->foreign('supergroup_id')
                ->references('id')
                ->on('supergroups')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('supergroup_translations');
    }
};
