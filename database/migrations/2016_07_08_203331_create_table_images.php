<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTableImages extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('images', function ($table)
        {
            $table->increments('id');
            $table->text('original_name');
            $table->text('filename');
            $table->timestamps();
        });
        
        Schema::create('image_listing', function ($table)
        {
            $table->increments('id');
			$table->integer('image_id')->unsigned()->nullable();
			$table->integer('listing_id')->unsigned()->nullable();
            $table->foreign('image_id')->references('id')->on('images')->onDelete('cascade');
            $table->foreign('listing_id')->references('id')->on('listings')->onDelete('cascade');
        });
        
//         // Get records from listings.
//         $resultsL = DB::table('listings')->select(array('id', 'image'))->get();
//         // Loop through the results and insert into images table
//         foreach($resultsL as $result)
//         {
//         	// Insert only if image is not null
//         	if($result->image === null){
//         		continue;
//         	}
//         	$imageId = DB::table('images')->insertGetId([
//         			"image"			=>  $result->image,
//         			"order"			=>  1,
//         			]);
        	
//         	DB::table('image_listing')->insert([
//         			"listing_id"	=>  $result->id,
//         			"image_id"		=>  $imageId,
//         			]);
//         }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		Schema::drop('image_listing');
		Schema::drop('images');
    }
}
