<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTableLanguages extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('languages', function (Blueprint $table) {
            $table->increments('id');
			$table->string('locale', 2);
			$table->string('language', 20);
        });
        
        // Create the language entries
        DB::table('languages')->insert([
        		"locale"	=>  "en",
        		"language"	=>  "en",
        		]);
        DB::table('languages')->insert([
        		"locale"	=>  "el",
        		"language"	=>  "el",
        		]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		Schema::drop('languages');
    }
}
