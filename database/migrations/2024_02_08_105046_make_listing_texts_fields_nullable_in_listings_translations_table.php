<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('listings_translations', function (Blueprint $table) {
            $table->text('description')->nullable(true)->change();
            $table->string('meta_title')->nullable(true)->change();
            $table->text('meta_description')->nullable(true)->change();
            $table->string('meta_title_cr')->nullable(true)->change();
            $table->text('meta_description_cr')->nullable(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('listings_translations', function (Blueprint $table) {
            $table->text('description')->nullable(false)->change();
            $table->string('meta_title')->nullable(false)->change();
            $table->text('meta_description')->nullable(false)->change();
            $table->string('meta_title_cr')->nullable(false)->change();
            $table->text('meta_description_cr')->nullable(false)->change();
        });
    }
};
