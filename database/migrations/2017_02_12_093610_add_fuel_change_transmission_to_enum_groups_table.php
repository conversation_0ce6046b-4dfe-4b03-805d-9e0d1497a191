<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFuelChangeTransmissionToEnumGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('groups', function (Blueprint $table) {
            $table->dropColumn('transmission');
            $table->enum('fuel', ['petrol', 'diesel'])->default('petrol');
        });

        Schema::table('groups', function (Blueprint $table) {
            $table->enum('transmission', ['manual', 'automatic'])->default('manual');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('groups', function (Blueprint $table)
        {
            $table->dropColumn('fuel');
        });
    }
}
