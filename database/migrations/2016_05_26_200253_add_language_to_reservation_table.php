<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLanguageToReservationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('reservations', function($table)
        {
            $table->integer('language_id')->unsigned()->nullable()->after('customer_id');
            $table->foreign('language_id')->references('id')->on('languages')->onDelete('set null');
        });
    		DB::table('reservations')
    		->update(array('language_id' => 1));
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('reservations', function($table)
        {
            $table->dropForeign('reservations_language_id_foreign');
            $table->dropColumn('language_id');
        });
    }
}
