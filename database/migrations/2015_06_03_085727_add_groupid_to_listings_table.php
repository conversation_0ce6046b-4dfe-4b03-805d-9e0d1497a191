<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddGroupidToListingsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
        Schema::table('listings', function($table)
        {
            $table->integer('group_id')->unsigned()->nullable()->after('description');
            $table->foreign('group_id')->references('id')->on('groups')->onDelete('set null');
        });
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
        Schema::table('listings', function($table)
        {
            $table->dropForeign('listings_group_id_foreign');
            $table->dropColumn('group_id');
        });
	}

}
