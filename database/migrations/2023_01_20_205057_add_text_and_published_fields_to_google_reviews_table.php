<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTextAndPublishedFieldsToGoogleReviewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('google_reviews', function (Blueprint $table) {
            $table->boolean('published')->default(0)->after('id');
            $table->text('translated_text')->nullable()->after('review_text');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('google_reviews', function (Blueprint $table) {
            $table->dropColumn('published');
            $table->dropColumn('translated_text');
        });
    }
}
