<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateReservationToAccessoryTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('reservation_to_accessory', function($table)
		{
			$table->increments('id');
			$table->integer('reservation_id');
			$table->integer('accessory_id');
			$table->integer('accessory_amount');
			$table->integer('accessory_price');
			$table->string('accessory_name', 200);
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('reservation_to_accessory');
	}

}
