<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Carbon\Carbon;

class CreateTableCategories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('categories', function ($table)
        {
            $table->increments('id');
			$table->string('name', 200);
			$table->string('description', 1000);
			$table->timestamps();
        });
        
        // Create the first entry which will be the default category for featured listings
        DB::table('categories')->insert([
        		"name"    		=>  "featured",
        		"description"	=>  "The default category for featured listings",
				'created_at' => Carbon::now(),
				'updated_at' => Carbon::now(),
        		]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		Schema::drop('categories');
    }
}
