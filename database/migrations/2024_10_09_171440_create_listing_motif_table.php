<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('listing_motif', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('motif_id');
            $table->unsignedInteger('listing_id');
            $table->timestamps();

            $table->foreign('motif_id')
                ->references('id')
                ->on('motifs')
                ->onDelete('cascade');
            $table->foreign('listing_id')
                ->references('id')
                ->on('listings')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('listing_motif');
    }
};
