<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGoogleReviewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('google_reviews', function (Blueprint $table) {
            $table->id();
            $table->string('review_id')->nullable();
            $table->string('author_link')->nullable();
            $table->string('author_title')->nullable();
            $table->string('author_id')->nullable();
            $table->string('author_image')->nullable();
            $table->text('review_text')->nullable();
            $table->string('review_img_urls')->nullable();
            $table->text('owner_answer')->nullable();
            $table->datetime('owner_answer_datetime')->nullable();
            $table->string('review_link')->nullable();
            $table->unsignedInteger('review_rating')->default(0);
            $table->dateTime('review_datetime')->nullable();
            $table->unsignedInteger('review_likes')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('google_reviews');
    }
}
