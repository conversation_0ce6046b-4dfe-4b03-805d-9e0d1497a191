#!/bin/bash

function_prepare_container(){
    chown -R www-data:www-data /var/www/.composer
    sudo -u www-data composer install
    if ! test -f ".env"; then
        echo "Creating .env file"
        sudo -u www-data cp .env.example .env
        echo "Creating application key"
        sudo -u www-data php artisan key:generate --ansi
    fi
    sed -i 's/remote_enable=on/mode=debug/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    sed -i 's/remote_host/client_host/g' /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
}
function_prepare_container &
